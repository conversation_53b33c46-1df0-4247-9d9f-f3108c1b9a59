<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Leaderboard</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      }

      body {
        background-color: #f5f7fa;
        padding: 20px;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #f9fafb;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 24px;
      }

      h1 {
        font-size: 28px;
        font-weight: bold;
        text-align: center;
        color: #1f2937;
        margin-bottom: 32px;
      }

      .top-users {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 16px;
        margin-bottom: 32px;
      }

      .top-user {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 144px;
        padding: 16px;
        border-radius: 8px;
        background-color: white;
        border: 1px solid #e5e7eb;
      }

      .top-user.first {
        background-color: #fef3c7;
        border: 2px solid #fcd34d;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transform: scale(1.1);
      }

      .badge {
        margin-bottom: 8px;
        font-size: 24px;
      }

      .badge.gold {
        color: #f59e0b;
      }

      .badge.silver {
        color: #9ca3af;
      }

      .badge.bronze {
        color: #b45309;
      }

      .position {
        font-weight: bold;
        font-size: 18px;
      }

      .avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        overflow: hidden;
        background-color: #e5e7eb;
        margin: 8px 0;
      }

      .avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .username {
        font-weight: 600;
        color: #1f2937;
        margin-top: 4px;
      }

      .score {
        font-weight: bold;
        font-size: 18px;
        color: #4f46e5;
      }

      .users-table {
        width: 100%;
        background-color: white;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        overflow: hidden;
      }

      .table-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 16px;
        background-color: #f3f4f6;
        border-bottom: 1px solid #e5e7eb;
      }

      .header-rank {
        font-weight: 600;
        color: #4b5563;
      }

      .header-user {
        font-weight: 600;
        color: #4b5563;
        flex: 1;
        margin-left: 24px;
      }

      .header-score {
        font-weight: 600;
        color: #4b5563;
      }

      .user-row {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f3f4f6;
      }

      .user-row:hover {
        background-color: #f9fafb;
      }

      .user-rank {
        font-weight: 500;
        color: #1f2937;
        width: 32px;
      }

      .user-info {
        display: flex;
        align-items: center;
        flex: 1;
      }

      .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        background-color: #e5e7eb;
        margin-right: 12px;
      }

      .user-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .user-name {
        font-weight: 500;
        color: #1f2937;
      }

      .user-score {
        font-weight: bold;
        color: #4f46e5;
      }

      .pagination {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background-color: #f9fafb;
      }

      .pagination-info {
        font-size: 14px;
        color: #4b5563;
      }

      .pagination-controls {
        display: flex;
        gap: 8px;
      }

      .pagination-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 4px;
        border: none;
        background-color: transparent;
        cursor: pointer;
      }

      .pagination-button:disabled {
        color: #9ca3af;
        cursor: not-allowed;
      }

      .pagination-button:not(:disabled):hover {
        background-color: #e5e7eb;
      }

      .pagination-position {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 12px;
        height: 32px;
        background-color: white;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
      }

      .leaderboard-controls {
        max-width: 800px;
        margin: 0 auto 24px auto;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;
        justify-content: center;
        background: #fff;
        padding: 20px 24px 16px 24px;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(79, 70, 229, 0.07);
      }

      .leaderboard-controls label {
        font-weight: 500;
        color: #4f46e5;
      }

      .leaderboard-controls input,
      .leaderboard-controls select {
        margin-left: 8px;
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 15px;
        width: 220px;
        outline: none;
      }

      .leaderboard-controls button {
        padding: 10px 28px;
        font-size: 16px;
        background: linear-gradient(90deg, #6366f1 60%, #818cf8 100%);
        color: #fff;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(99, 102, 241, 0.08);
        font-weight: 600;
        transition: background 0.2s;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Leaderboard</h1>

      <!-- Top 3 Users -->
      <div class="top-users" id="topUsers">
        <!-- Will be populated by JavaScript -->
      </div>

      <!-- Rest of users (paginated) -->
      <div class="users-table">
        <div class="table-header">
          <div class="header-rank">Rank</div>
          <div class="header-user">User</div>
          <div class="header-score">Score</div>
        </div>

        <div id="usersList">
          <!-- Will be populated by JavaScript -->
        </div>

        <!-- Pagination controls -->
        <div class="pagination">
          <div class="pagination-info" id="paginationInfo">
            <!-- Will be populated by JavaScript -->
          </div>
          <div class="pagination-controls">
            <button id="prevButton" class="pagination-button" disabled>
              &#10094;
            </button>
            <div class="pagination-position" id="pagePosition">
              <!-- Will be populated by JavaScript -->
            </div>
            <button id="nextButton" class="pagination-button">&#10095;</button>
          </div>
        </div>
      </div>

      <div class="leaderboard-controls">
        <label
          >Batch ID:
          <input
            id="batchIdInput"
            type="text"
            value="6825e2e495126cbb2bc384a6"
            placeholder="Enter Batch ID"
          />
        </label>
        <label
          >Period:
          <select id="periodSelect">
            <option value="overall">Overall</option>
            <option value="monthly">Monthly</option>
            <option value="weekly">Weekly</option>
            <option value="daily">Daily</option>
          </select>
        </label>
        <button id="fetchLeaderboardButton">Fetch Leaderboard</button>
      </div>
    </div>

    <script>
      // --- CONFIG ---
      const API_BASE_URL =
        'http://localhost:4040/api/v3/public/leaderboard/6825e2e495126cbb2bc384a6';
      const LOGIN_URL = 'http://localhost:8080/api/v3/public/users/login';
      const USERS_PER_PAGE = 5;
      const PERIOD = 'overall'; // or 'daily', 'weekly', 'monthly' as needed

      // --- STATE ---
      let currentPage = 1;
      let totalPages = 1;
      let totalUsers = 0;
      let topUsers = [];
      let paginatedUsers = [];
      let isLoading = false;
      let errorMsg = '';

      // --- DOM ELEMENTS ---
      const topUsersEl = document.getElementById('topUsers');
      const usersListEl = document.getElementById('usersList');
      const paginationInfoEl = document.getElementById('paginationInfo');
      const pagePositionEl = document.getElementById('pagePosition');
      const prevButtonEl = document.getElementById('prevButton');
      const nextButtonEl = document.getElementById('nextButton');
      const batchIdInputEl = document.getElementById('batchIdInput');
      const periodSelectEl = document.getElementById('periodSelect');
      const fetchLeaderboardButtonEl = document.getElementById(
        'fetchLeaderboardButton'
      );

      // --- HELPERS ---
      function formatScore(score) {
        return new Intl.NumberFormat().format(score);
      }

      function showLoading() {
        usersListEl.innerHTML =
          '<div style="padding:24px;text-align:center;color:#6366f1;">Loading...</div>';
      }
      function showError(msg) {
        usersListEl.innerHTML = `<div style="padding:24px;text-align:center;color:#dc2626;">${msg}</div>`;
      }

      // --- API FETCH ---
      async function fetchLeaderboard(page = 1) {
        isLoading = true;
        errorMsg = '';
        showLoading();
        const batchId = batchIdInputEl.value.trim();
        const period = periodSelectEl.value;
        if (!batchId) {
          showError('Please enter a Batch ID.');
          isLoading = false;
          return;
        }
        try {
          const url = `http://localhost:4040/api/v3/public/leaderboard/${encodeURIComponent(batchId)}?period=${encodeURIComponent(period)}&page=${page}&limit=${USERS_PER_PAGE}`;
          const res = await fetch(url, { credentials: 'include' });
          if (!res.ok) throw new Error('Failed to fetch leaderboard');
          const result = await res.json();
          console.log('API Response:', result);
          if (
            !result.success ||
            !result.data ||
            !Array.isArray(result.data.rankings)
          )
            throw new Error('Malformed API response');
          const rankings = result.data.rankings;
          const pagination = result.data.pagination || {};
          totalUsers = pagination.total || rankings.length;
          totalPages = pagination.totalPages || 1;
          currentPage = pagination.page || 1;
          if (currentPage === 1) {
            topUsers = rankings.slice(0, 3);
            paginatedUsers = rankings.slice(3);
          } else {
            topUsers = [];
            paginatedUsers = rankings;
          }
        } catch (err) {
          errorMsg = err.message || 'Unknown error';
          topUsers = [];
          paginatedUsers = [];
        } finally {
          isLoading = false;
          render();
        }
      }

      // --- RENDER ---
      function renderTopUsers() {
        topUsersEl.innerHTML = '';
        if (!topUsers.length) return;
        topUsers.forEach((user, index) => {
          let badge = '',
            badgeClass = '',
            position = '';
          if (index === 0) {
            badge = '👑';
            badgeClass = 'gold';
            position = '1st';
          } else if (index === 1) {
            badge = '🥈';
            badgeClass = 'silver';
            position = '2nd';
          } else {
            badge = '🥉';
            badgeClass = 'bronze';
            position = '3rd';
          }
          const userEl = document.createElement('div');
          userEl.className = `top-user ${index === 0 ? 'first' : ''}`;
          userEl.innerHTML = `
          <div class="badge ${badgeClass}">${badge}</div>
          <div class="position">${position}</div>
          <div class="avatar">
            <img src="${user.avatar || 'https://via.placeholder.com/50'}" alt="${user.username || user.name || 'User'}">
          </div>
          <div class="username">${user.username || user.name || 'User'}</div>
          <div class="score">${formatScore(user.score || user.points || user.totalPoints || 0)}</div>
        `;
          topUsersEl.appendChild(userEl);
        });
      }

      function renderPaginatedUsers() {
        usersListEl.innerHTML = '';
        if (isLoading) {
          showLoading();
          return;
        }
        if (errorMsg) {
          showError(errorMsg);
          return;
        }
        if (!paginatedUsers.length) {
          usersListEl.innerHTML =
            '<div style="padding:24px;text-align:center;color:#6b7280;">No users found.</div>';
          return;
        }
        // Calculate start index (skip top 3 for first page)
        let startIndex =
          currentPage === 1 ? 3 : USERS_PER_PAGE * (currentPage - 1);
        paginatedUsers.forEach((user, idx) => {
          const userEl = document.createElement('div');
          userEl.className = 'user-row';
          userEl.innerHTML = `
          <div class="user-rank">${startIndex + idx + 1}</div>
          <div class="user-info">
            <div class="user-avatar">
              <img src="${user.avatar || 'https://via.placeholder.com/32'}" alt="${user.username || user.name || 'User'}">
            </div>
            <div class="user-name">${user.username || user.name || 'User'}</div>
          </div>
          <div class="user-score">${formatScore(user.score || user.points || user.totalPoints || 0)}</div>
        `;
          usersListEl.appendChild(userEl);
        });
        // Pagination info
        const showingFrom = Math.min(
          totalUsers,
          (currentPage - 1) * USERS_PER_PAGE + (currentPage === 1 ? 4 : 1)
        );
        const showingTo = Math.min(
          totalUsers,
          (currentPage - 1) * USERS_PER_PAGE +
            paginatedUsers.length +
            (currentPage === 1 ? 3 : 0)
        );
        paginationInfoEl.textContent = `Showing ${showingFrom} to ${showingTo} of ${totalUsers} users`;
        pagePositionEl.textContent = `${currentPage} / ${totalPages}`;
        prevButtonEl.disabled = currentPage === 1;
        nextButtonEl.disabled = currentPage === totalPages;
      }

      function render() {
        renderTopUsers();
        renderPaginatedUsers();
      }

      // --- INITIAL FETCH ---
      // Optionally, auto-fetch leaderboard on page load:
      // fetchLeaderboard(1);

      fetchLeaderboardButtonEl.addEventListener('click', function () {
        currentPage = 1;
        fetchLeaderboard(currentPage);
      });

      prevButtonEl.addEventListener('click', function () {
        if (currentPage > 1) {
          currentPage--;
          fetchLeaderboard(currentPage);
        }
      });

      nextButtonEl.addEventListener('click', function () {
        if (currentPage < totalPages) {
          currentPage++;
          fetchLeaderboard(currentPage);
        }
      });
    </script>
  </body>
</html>
