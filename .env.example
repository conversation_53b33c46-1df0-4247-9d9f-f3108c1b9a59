PORT=1234 # Port number for the server 
MONGO_CONNECTION_STRING=mongodb://localhost:27017/development # MongoDB connection string
NODE_ENV=development # Node environment
CORS_ORIGIN=[http://localhost:3000, http://localhost:3001, http://localhost:3002] # CORS origin
JWT_ACCESS_SECRET=secret_example123vAiiY5IT7+xgRulFmG7Nc1X/f0Y # JWT access secret
JWT_ACCESS_EXPIRATION=10m
JWT_REFRESH_SECRET=secret_example123vAiiY5IT7+xgRulFmG7Nc1X/f0Y # JWT refresh secret
JWT_REFRESH_EXPIRATION=90d
IMAGEKIT_PUBLIC_KEY=public_example123vAiiY5IT7+xgRulFmG7Nc1X/f0Y= # ImageKit public key
IMAGEKIT_PRIVATE_KEY=private_example123vAiiY5IT7+xgRulFmG7Nc1X/f0Y= # ImageKit private key
IMAGEKIT_URL_ENDPOINT=https://ik.imagekit.io/example123 # ImageKit URL endpoint
EMAIL=<EMAIL> # Email address for sending emails
EMAIL_REFRESH_TOKEN=12345678-abcd4324l2jlkjslkgjslkjl345.apps.googleusercontent.com # Email refresh token
EMAIL_CLIENT_ID=GOSCPX-EXAMPLE123 # Email client ID
EMAIL_CLIENT=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 # Email client
REDIS_URL="redis[s]://[[username][:password]@][host][:port][/db-number]" # Redis URL
RAZORPAY_KEY_ID=rzp_test_123456789abcdef # Razorpay key ID
RAZORPAY_KEY_SECRET=123456789abcdef # Razorpay key secret
MSG91_AUTH_KEY=123456789abcdef # MSG91 authentication key
MSG91_OTP_URL=https://control.msg91.com/api/v5/otp # MSG91 OTP URL
MSG91_OTP_TEMPLATE_ID=123456789abcdef # MSG91 OTP template ID
DEFAULT_THUMBNAIL=https://ik.imagekit.io/example123/default_image.png?updatedAt=1719013530330 # Default thumbnail URL
COOKIE_ACCESS_MAX_AGE=600000 # Cookie access max age (in milliseconds)
COOKIE_REFRESH_MAX_AGE=7776000000 # Cookie refresh max age (in milliseconds)
MESSAGE_BROKER=redis://default:
VDOCIPHER_API_SECRET=VdoCipherAPIsecret
JUDGE0_API_KEY=JudgeOnAPIKey
JUDGE0_API_URL=https://judgeon.com/api/v1
CERTIFICATE_COMPLETION=COMPLETION
CERTIFICATE_COMPLETION_PERCENTAGE=65
CERTIFICATE_EXCELLENCE=EXCELLENCE
CERTIFICATE_EXCELLENCE_PERCENTAGE=80
PENALTY_PERCENTAGE=30
SUBMODULE_COMPLETION_PERCENTAGE=70
TZ=Asia/Kolkata # Timezone