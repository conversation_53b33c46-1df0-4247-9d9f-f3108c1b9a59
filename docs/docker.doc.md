# Sheryians 3.0 API Docker Guidelines

> This document outlines Docker setup, commands, and best practices for the Sheryians 3.0 API project.

## Quick Start

### Build Docker Image

```bash
# macOS/Linux
docker build -t sheryians-3.0-backend:dev -f docker/development/Dockerfile .

# Windows (PowerShell)
docker build -t sheryians-3.0-backend:dev -f docker/development/Dockerfile .

# Windows (CMD)
docker build -t sheryians-3.0-backend:dev -f docker/development/Dockerfile .
```

### Run Docker Container

```bash
# macOS/Linux
docker run --name sheryains-3.0-backend --rm -it -v $(pwd):/usr/src/app -v /usr/src/app/node_modules --env-file $(pwd)/.env -p 8080:8080 sheryians-3.0-backend:dev

# Windows (PowerShell)
docker run --name sheryains-3.0-backend --rm -it -v ${PWD}:/usr/src/app -v /usr/src/app/node_modules --env-file ${PWD}/.env -p 8080:8080 sheryians-3.0-backend:dev

# Windows (CMD)
docker run --name sheryains-3.0-backend --rm -it -v %cd%:/usr/src/app -v /usr/src/app/node_modules --env-file %cd%\.env -p 8080:8080 sheryians-3.0-backend:dev
```

## Docker Commands Explained

### Build Command

```bash
docker build -t sheryians-3.0-backend:dev -f docker/development/Dockerfile .
```

- **`docker build`**: Command to build a Docker image
- **`-t sheryians-3.0-backend:dev`**: Tags the image with name and version
- **`-f docker/development/Dockerfile`**: Specifies the Dockerfile path
- **`.`**: Build context (current directory)

### Run Command

```bash
# macOS/Linux
docker run --name sheryains-3.0-backend --rm -it -v $(pwd):/usr/src/app -v /usr/src/app/node_modules --env-file $(pwd)/.env -p 8080:8080 sheryians-3.0-backend:dev
```

- **`docker run`**: Command to create and start a container
- **`--name sheryains-3.0-backend`**: Names the container
- **`--rm`**: Automatically removes the container when it exits
- **`-it`**: Interactive mode with terminal access
- **`-v $(pwd):/usr/src/app`**: Maps your local project directory to container's working directory
  - Windows PowerShell: Use `${PWD}` instead of `$(pwd)`
  - Windows CMD: Use `%cd%` instead of `$(pwd)`
- **`-v /usr/src/app/node_modules`**: Creates a volume for node_modules (prevents overwriting)
- **`--env-file $(pwd)/.env`**: Loads environment variables from .env file
  - Windows PowerShell: Use `${PWD}\.env` instead
  - Windows CMD: Use `%cd%\.env` instead
- **`-p 8080:8080`**: Maps port 8080 on your machine to port 8080 in the container
- **`sheryians-3.0-backend:dev`**: Image to use for the container

## Common Docker Commands

### Container Management

```bash
# List running containers
docker ps

# List all containers (including stopped)
docker ps -a

# Stop container
docker stop sheryains-3.0-backend

# Remove container
docker rm sheryains-3.0-backend

# Stop a running container
If container is running in interactive mode.
Press Ctrl/CMD+C in the terminal where container is running

# If container is running in detached mode.
# First list all running containers
docker ps

# Then stop the container using container ID or name
docker stop <container id>
```

### Image Management

```bash
# List images
docker images

# Remove image
docker rmi sheryians-3.0-backend:dev

# Clean up unused images
docker image prune
```

### Logs and Debugging

```bash
# View container logs
docker logs sheryains-3.0-backend

# Follow container logs
docker logs -f sheryains-3.0-backend

# Execute commands in running container
docker exec -it sheryains-3.0-backend bash
```

## Docker Compose (Alternative)

For multi-container setups, consider using Docker Compose. Create a `docker-compose.yml` file:

```yaml
version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: docker/development/Dockerfile
    container_name: sheryains-3.0-backend
    ports:
      - '8080:8080'
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    env_file:
      - .env
    depends_on:
      - mongodb
      - redis

  mongodb:
    image: mongo:latest
    container_name: mongodb
    ports:
      - '27017:27017'
    volumes:
      - mongo-data:/data/db

  redis:
    image: redis:alpine
    container_name: redis
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data

volumes:
  mongo-data:
  redis-data:
```

Run with:

```bash
docker-compose up
```

## Best Practices

1. **Use specific versions** for base images in Dockerfile (e.g., `FROM node:18-alpine` instead of `FROM node:latest`)

2. **Multi-stage builds** to keep production images small:

   ```dockerfile
   # Build stage
   FROM node:18-alpine AS builder
   WORKDIR /usr/src/app
   COPY package*.json ./
   RUN npm ci
   COPY . .
   RUN npm run build

   # Production stage
   FROM node:18-alpine
   WORKDIR /usr/src/app
   COPY --from=builder /usr/src/app/dist ./dist
   COPY package*.json ./
   RUN npm ci --only=production
   CMD ["node", "dist/server.js"]
   ```

3. **Layer caching**: Order Dockerfile commands from least to most frequently changing

4. **Health checks** to ensure container is running properly:

   ```dockerfile
   HEALTHCHECK --interval=30s --timeout=10s --retries=3 \
     CMD curl -f http://localhost:8080/health || exit 1
   ```

5. **Non-root user** for security:
   ```dockerfile
   RUN addgroup -g 1000 appuser && \
       adduser -u 1000 -G appuser -s /bin/sh -D appuser
   USER appuser
   ```

## Environment Configuration

Always use environment variables for configuration. In development:

1. Use `.env` file with `--env-file` flag
2. Never commit `.env` files to git (use `.env.example` instead)
3. Use environment-specific variables for different stages (dev/staging/prod)

## Troubleshooting

### Container Won't Start

Check logs:

```bash
docker logs sheryains-3.0-backend
```

### Port Already In Use

Change the host port:

```bash
docker run -p 8081:8080 sheryians-3.0-backend:dev
```

### File Permission Issues

Check volume mounts and user permissions inside the container:

```bash
docker exec -it sheryains-3.0-backend ls -la
```

### Can't Connect to Services

Ensure services are on the same Docker network:

```bash
docker network create sheryians-network
docker run --network sheryians-network ...
```
