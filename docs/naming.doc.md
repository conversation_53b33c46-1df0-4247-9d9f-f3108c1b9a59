# Sheryians 3.0 API Naming Conventions

# Press(cmd/ctrl + shift + v)

> Consistent naming conventions improve code readability, maintainability and collaboration between developers.

## File & Directory Naming

- **File Names**: `kebab-case`

  - Example: ✅ `user-register.controller.ts`, `payment-razorpay.service.ts`, `user-auth.middleware.ts`
  - Avoid: ❌ `UserController.ts`, `paymentService.ts`, `Auth_Middleware.ts`

- **Directory Names**: `kebab-case`

  - Example: ✅ `models/`, `user-profiles/`, `payment-services/`
  - Avoid: ❌ `Models/`, `userProfiles/`, `payment_services/`

- **Special Files**: Some files should follow specific patterns:
  - Test files: `[name].test.ts` or `[name].spec.ts`
  - Interface files: `[name].interface.ts`
  - Type definition files: `[name].d.ts`

## JavaScript/TypeScript Conventions

### Variables and Functions

- **Variables**: `camelCase`

  - Example: ✅ `userId`, `paymentStatus`, `isAuthenticated`
  - Avoid: ❌ `UserID`, `payment_status`, `is-authenticated`

- **Functions/Methods**: `camelCase`

  - Example: ✅ `getUserData()`, `processPayment()`, `validateInput()`
  - Avoid: ❌ `GetUserData()`, `process_payment()`, `validate-input()`

- **Private Methods/Properties**: Prefix with underscore + `camelCase`
  - Example: ✅ `_privateMethod()`, `_internalState`

### Classes, Interfaces and Types

- **Classes**: `PascalCase`

  - Example: ✅ `UserController`, `PaymentService`, `AuthMiddleware`
  - Avoid: ❌ `userController`, `payment_Service`, `authMiddleware`

- **Interfaces**: `PascalCase` (often prefixed with `I`)

  - Example: ✅ `UserProfile`, `IUserProfile`, `PaymentDetails`
  - Avoid: ❌ `userProfile`, `user_profile`

- **Type Aliases**: `PascalCase`
  - Example: ✅ `UserRole`, `PaymentMethod`, `ApiResponse`

### Constants and Environment Variables

- **Constants**: `MACRO_CASE` (uppercase with underscores)

  - Example: ✅ `MAX_RETRY_COUNT`, `API_TIMEOUT`, `DEFAULT_CURRENCY`
  - Avoid: ❌ `MaxRetryCount`, `apiTimeout`, `default-currency`

- **Environment Variables**: `MACRO_CASE`
  - Example: ✅ `DATABASE_URL`, `JWT_SECRET`, `REDIS_HOST`

### Enumerations

- **Enum Names**: `PascalCase`

  - Example: ✅ `UserRole`, `PaymentStatus`, `BatchStatus`

- **Enum Members**: `snake_case`
  - Example: ✅ `UserRole.admin_user`, `PaymentStatus.payment_pending`
  - Avoid: ❌ `UserRole.ADMIN_USER`, `PaymentStatus.paymentPending`

## Database Conventions

- **Collection/Table Names**: Plural `snake_case` or `kebab-case` (depending on DB)

  - Example: ✅ `users`, `payment_transactions` or `payment-transactions`

- **Field/Column Names**: `snake_case` for MongoDB, `camelCase` for code usage
  - Example: ✅ `user_id`, `created_at` in database
  - Example: ✅ `userId`, `createdAt` in JavaScript/TypeScript

## API Endpoint Conventions

- **API Routes**: `kebab-case`
  - Example: ✅ `/api/user-profiles`, `/api/payment-status`
  - Avoid: ❌ `/api/userProfiles`, `/api/PaymentStatus`

## Additional Best Practices

- **Be descriptive yet concise**

  - Prefer `getUserById` over `getUser` or `getUserInformation`

- **Avoid abbreviations unless widely understood**

  - Prefer `userAuthentication` over `userAuth`
  - OK to use: `id`, `url`, `http`

- **Use meaningful names**

  - Prefer `isActive` over `flag1`
  - Prefer `calculateTotal` over `calc`

- **Boolean variables should start with**:

  - `is`, `has`, `can`, or `should`
  - Example: ✅ `isActive`, `hasPermission`, `canEdit`, `shouldRefresh`

- **Arrays should use plural names**

  - Example: ✅ `users`, `transactions`, `products`

- **Functions should use verbs**

  - Example: ✅ `getUser()`, `validateInput()`, `calculateTotal()`

- **Consistency is key**
  - Choose one convention and stick to it throughout the codebase
  - Document exceptions to the rules when necessary
