# Deadline Optimization Solutions

## Current Problem

Updating one submodule deadline causes:

- 50 batch-submodule deadline updates
- 10,000 user deadline updates (200 users × 50 submodules)
- **Total: 10,050 database writes for 1 deadline change**

## Solution 1: Computed Deadlines (Recommended)

### Core Concept

- Store base deadlines at batch-submodule level
- Store only user-specific adjustments (not full deadlines)
- Compute final deadlines on-the-fly

### New Schema Design

#### 1. Keep Batch-Submodule Deadlines (No Change)

```javascript
// batch-submodule-deadline.model.ts - UNCHANGED
const batchSubmoduleDeadlineSchema = new Schema({
  batch: { type: ObjectId, ref: 'Batch', required: true },
  submodule: { type: ObjectId, ref: 'SubModule', required: true },
  startDate: { type: Date, required: true },
  deadline: { type: Date, required: true },
  dependsOnPreviousSubmodule: { type: Boolean, default: true },
  penaltyRules: [{ daysLate: Number, penaltyPercentage: Number }],
  isActive: { type: Boolean, default: true }
});
```

#### 2. Replace User-Deadline with User-Deadline-Adjustments

```javascript
// user-deadline-adjustment.model.ts - NEW
const userDeadlineAdjustmentSchema = new Schema({
  user: { type: ObjectId, ref: 'User', required: true },
  batch: { type: ObjectId, ref: 'Batch', required: true },

  // Global adjustments (affects all submodules)
  totalPauseDays: { type: Number, default: 0 },

  // Submodule-specific adjustments (only when different from global)
  submoduleAdjustments: [
    {
      submodule: { type: ObjectId, ref: 'SubModule' },
      additionalDays: { type: Number, default: 0 }, // Extra days beyond pause
      customStartDate: { type: Date }, // Override start date
      isExempt: { type: Boolean, default: false } // Skip this submodule
    }
  ],

  isActive: { type: Boolean, default: true }
});

// Compound index for fast lookups
userDeadlineAdjustmentSchema.index({ user: 1, batch: 1 }, { unique: true });
```

### Deadline Computation Logic

```javascript
// utils/deadline-calculator.util.ts
class DeadlineCalculator {
  static async getUserDeadline(userId, batchId, submoduleId) {
    // 1. Get base deadline from batch-submodule
    const batchDeadline = await BatchSubmoduleDeadline.findOne({
      batch: batchId,
      submodule: submoduleId,
      isActive: true
    });

    // 2. Get user adjustments
    const userAdjustment = await UserDeadlineAdjustment.findOne({
      user: userId,
      batch: batchId,
      isActive: true
    });

    // 3. Calculate final deadline
    let finalDeadline = new Date(batchDeadline.deadline);
    let finalStartDate = new Date(batchDeadline.startDate);

    if (userAdjustment) {
      // Apply global pause days
      finalDeadline.setDate(
        finalDeadline.getDate() + userAdjustment.totalPauseDays
      );
      finalStartDate.setDate(
        finalStartDate.getDate() + userAdjustment.totalPauseDays
      );

      // Apply submodule-specific adjustments
      const submoduleAdj = userAdjustment.submoduleAdjustments.find(
        adj => adj.submodule.toString() === submoduleId.toString()
      );

      if (submoduleAdj) {
        if (submoduleAdj.customStartDate) {
          finalStartDate = submoduleAdj.customStartDate;
        }
        finalDeadline.setDate(
          finalDeadline.getDate() + submoduleAdj.additionalDays
        );
      }
    }

    return {
      startDate: finalStartDate,
      deadline: finalDeadline,
      originalDeadline: batchDeadline.deadline
    };
  }

  static async getBatchUserDeadlines(userId, batchId) {
    // Get all batch deadlines at once
    const batchDeadlines = await BatchSubmoduleDeadline.find({
      batch: batchId,
      isActive: true
    }).populate('submodule');

    // Get user adjustments once
    const userAdjustment = await UserDeadlineAdjustment.findOne({
      user: userId,
      batch: batchId,
      isActive: true
    });

    // Compute all deadlines in memory
    return batchDeadlines.map(batchDeadline => {
      // Same logic as above but for all submodules
      // ... computation logic
    });
  }
}
```

## Benefits of Solution 1

### Performance Gains

- **Deadline Updates**: 50 writes → 50 writes (no change in batch deadlines)
- **User Records**: 10,000 records → ~200 records (one per user per batch)
- **Storage**: ~95% reduction in deadline-related data
- **Query Performance**: Faster due to smaller collections

### Operational Benefits

- **Batch Pause**: Update 1 record instead of 50+ records
- **Individual Extensions**: Add submodule-specific adjustment
- **Bulk Operations**: Much faster for batch-wide changes

## Solution 2: Event-Driven Updates (Alternative)

If you prefer keeping the current structure:

### Use Background Jobs

```javascript
// When updating submodule deadline
async function updateSubmoduleDeadline(submoduleId, newDeadline) {
  // 1. Update batch deadlines immediately
  await BatchSubmoduleDeadline.updateMany(
    { submodule: submoduleId },
    { deadline: newDeadline }
  );

  // 2. Queue background job for user deadlines
  await deadlineUpdateQueue.add('updateUserDeadlines', {
    submoduleId,
    newDeadline
  });

  return { message: 'Deadline update initiated' };
}

// Background worker
deadlineUpdateQueue.process('updateUserDeadlines', async job => {
  const { submoduleId, newDeadline } = job.data;

  // Update in batches to avoid memory issues
  const batchSize = 1000;
  let skip = 0;

  while (true) {
    const result = await UserDeadline.updateMany(
      { submodule: submoduleId },
      { adjustedDeadline: newDeadline },
      { skip, limit: batchSize }
    );

    if (result.modifiedCount < batchSize) break;
    skip += batchSize;
  }
});
```

## Solution 3: Hybrid Approach

Combine both approaches:

- Use computed deadlines for reads
- Keep user deadlines for complex queries
- Sync periodically or on-demand

## Recommendation

**Go with Solution 1 (Computed Deadlines)** because:

1. **Immediate Impact**: Reduces 10,000 writes to ~200 records
2. **Scalability**: Performance improves as user base grows
3. **Simplicity**: Easier to maintain and debug
4. **Flexibility**: Easy to add new adjustment types
5. **Cost**: Significant database cost reduction

Would you like me to implement Solution 1 with the new schema and utility functions?
