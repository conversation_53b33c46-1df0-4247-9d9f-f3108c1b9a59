# Sheryians 3.0 API Linting Guidelines

> (Press(cmd/ctrl + shift + v) Lin<PERSON> helps maintain code quality and consistency across the team. This document outlines our linting setup and practices.

## Linting Tools

The project uses the following linting tools:

- **ESLint**: JavaScript/TypeScript code quality checker
- **Prettier**: Code formatter for consistent styling
- **Husky**: Git hooks for pre-commit validation
- **lint-staged**: Run linters on staged files only

## Running Lint Commands

### Check for Issues

```bash
# Check code for linting issues
npm run lint:check

# Check formatting issues
npm run format:check
```

### Fix Issues Automatically

```bash
# Auto-fix linting issues where possible
npm run lint:fix

# Auto-format code
npm run format:fix
```

## Key Linting Rules

### Error Prevention

- **`eqeqeq`**: Always use `===` instead of `==` for equality checks

  - ✅ `if (value === undefined)`
  - ❌ `if (value == undefined)`

- **`no-duplicate-imports`**: Prevent duplicate import declarations

  - ✅ `import { useState, useEffect } from 'react'`
  - ❌ `import { useState } from 'react'; import { useEffect } from 'react'`

- **`no-eval`**: Forbid the use of `eval()` due to security risks
  - ❌ `eval('console.log("unsafe")')`

### Code Quality

- **`max-depth`**: Limit nested blocks to maximum depth of 4

  - ❌ Nesting more than 4 levels of if/for/while blocks

- **`prefer-const`**: Use `const` for variables that aren't reassigned

  - ✅ `const user = getUser()`
  - ❌ `let user = getUser()` (when `user` isn't reassigned)

- **`dot-notation`**: Use dot notation when accessing properties
  - ✅ `object.property`
  - ❌ `object['property']` (unless property name is dynamic)

### Debugging

- **`no-console`**: Restrict `console` usage in production code
  - ✅ `console.warn()`, `console.error()` (allowed)
  - ❌ `console.log()` (disallowed)

## TypeScript-Specific Rules

TypeScript ESLint is configured with the recommended settings, providing type-aware linting:

- Type checking in your linting process
- Detection of unreachable code
- Proper handling of promises
- Correct usage of types and interfaces

## Editor Integration

VSCode is configured to automatically:

- Format documents on save using Prettier
- Run ESLint fix actions on save
- Highlight linting errors and warnings in-editor

VSCode settings are included in the repository at `.vscode/settings.json`.

## Git Hooks

The project uses Husky to enforce code quality at commit time:

- **Pre-commit hook**: Runs linting and formatting on staged files
- **Configuration**: See the `lint-staged` section in `package.json`

## Best Practices

1. **Don't Ignore Errors**: Fix linting errors rather than ignoring them

2. **Use Disable Comments Sparingly**: Only use `// eslint-disable-next-line` when absolutely necessary and include a comment explaining why

3. **Keep Dependencies Updated**: Regularly update ESLint and its plugins

4. **Run Linting Before Pull Requests**: Always run linting locally before submitting code for review

5. **Extend Configurations Carefully**: When adding new rules or plugins, consider the impact on the development workflow

6. **Custom Rules**: Discuss with the team before adding custom rules or modifying existing rules
