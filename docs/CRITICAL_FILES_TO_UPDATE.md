# 🚨 CRITICAL FILES THAT MUST BE UPDATED FOR DEADLINE OPTIMIZATION

## ⚠️ PRODUCTION RISK: Missing any of these updates will cause errors!

Based on comprehensive codebase analysis, here are ALL files that need changes:

---

## 📁 **PHASE 1: CORE MODEL & INTERFACE UPDATES**

### ✅ Already Created (New Files)
- `src/interfaces/user-deadline-adjustment.interface.ts` ✅
- `src/models/user-deadline-adjustment.model.ts` ✅  
- `src/utils/deadline-calculator.util.ts` ✅

---

## 📁 **PHASE 2: CRITICAL SERVICE LAYER UPDATES**

### 🔴 **HIGH PRIORITY - MUST UPDATE**

#### 1. **`src/services/public/user-deadline.service.ts`**
**Current Issue:** Uses old `userDeadlineDao.adjustDeadline()`
**Required Changes:**
```typescript
// BEFORE
export const pauseUserDeadlineAdjustment = async (
  userId: string | mongoose.Types.ObjectId,
  batchId: string | mongoose.Types.ObjectId,
  pauseDays: number,
  pauseStartDate: Date
): Promise<number | null> => {
  const deadlineAdjusted = await userDeadlineDao.adjustDeadline(
    userId, batchId, pauseDays, pauseStartDate
  );
  // ... rest
};

// AFTER
import UserDeadlineAdjustment from '../../models/user-deadline-adjustment.model';

export const pauseUserDeadlineAdjustment = async (
  userId: string | mongoose.Types.ObjectId,
  batchId: string | mongoose.Types.ObjectId,
  pauseDays: number,
  pauseStartDate: Date
): Promise<{ success: boolean; totalPauseDays: number; remainingDays: number } | null> => {
  const userObjId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
  const batchObjId = typeof batchId === 'string' ? new mongoose.Types.ObjectId(batchId) : batchId;
  
  const result = await UserDeadlineAdjustment.applyBatchPause(
    userObjId, batchObjId, pauseDays, pauseStartDate
  );
  
  if (!result.success) {
    throw new ErrorHandler(400, `Cannot pause for ${pauseDays} days. Only ${result.remainingDays} days remaining.`);
  }
  
  return result;
};

export const resumePauseEarlyAdjustment = async (
  userId: string | mongoose.Types.ObjectId,
  batchId: string | mongoose.Types.ObjectId,
  actualDaysUsed: number
): Promise<{ success: boolean; daysAdjustedBack: number } | null> => {
  const userObjId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
  const batchObjId = typeof batchId === 'string' ? new mongoose.Types.ObjectId(batchId) : batchId;
  
  return await UserDeadlineAdjustment.resumeBatchPause(userObjId, batchObjId, actualDaysUsed);
};
```

#### 2. **`src/controllers/public/user-deadline.controller.ts`**
**Current Issue:** Expects number return, new system returns object
**Required Changes:**
```typescript
// Update response handling for new return types
export const adjustUserDeadlinesOnPause = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    // ... existing validation
    
    const response = await pauseUserDeadlineAdjustment(
      user._id, batchId, pauseDays, pauseStartDate
    );
    
    if (!response || !response.success) {
      return next(new ErrorHandler(400, 'Failed to adjust user deadline.'));
    }
    
    return ResponseHandler.success({
      message: 'User deadline adjusted successfully',
      totalPauseDays: response.totalPauseDays,
      remainingDays: response.remainingDays
    }, 'User deadline adjusted successfully').send(res);
  }
);
```

#### 3. **`src/controllers/public/batch-pause.controller.ts`**
**Current Issue:** Line 72-79 calls old deadline adjustment
**Required Changes:**
```typescript
// REPLACE lines 72-79
// OLD:
// await pauseUserDeadlineAdjustment(userId, batchId, daysBetween, start.toDate())

// NEW:
try {
  const deadlineResult = await pauseUserDeadlineAdjustment(
    userId, batchId, daysBetween, start.toDate()
  );
  if (!deadlineResult?.success) {
    console.warn('Failed to adjust deadlines:', deadlineResult);
  }
} catch (error) {
  console.error('Error adjusting user deadline:', error);
}
```

---

## 📁 **PHASE 3: MODEL HOOK UPDATES**

#### 4. **`src/models/batch-pause.model.ts`**
**Current Issue:** Lines 147-194 use old UserDeadline model
**Required Changes:**
```typescript
// REPLACE the entire post-save hook (lines 147-194)
batchPauseSchema.post('save', async function () {
  try {
    if (this.isNew && this.status === 'active') {
      const pauseDays = this.daysUsed;
      const userId = this.user;
      const batchId = this.batch;

      // Use new UserDeadlineAdjustment model
      const UserDeadlineAdjustment = mongoose.model('UserDeadlineAdjustment');
      
      const result = await UserDeadlineAdjustment.applyBatchPause(
        userId, batchId, pauseDays, this.startDate
      );

      if (result.success) {
        console.warn(`Successfully applied pause: ${pauseDays} days for user ${userId.toString()}`);
      } else {
        console.error(`Failed to apply pause: ${result.remainingDays} days remaining`);
      }
    }
  } catch (error: unknown) {
    console.error('Error applying batch pause:', error);
  }
});
```

---

## 📁 **PHASE 4: ENROLLMENT & INITIALIZATION UPDATES**

#### 5. **`src/services/private/job-handler.service.ts`**
**Current Issue:** Lines 23-49 use old UserDeadline.initializeUserDeadlines()
**Required Changes:**
```typescript
// REPLACE initializeUserDeadlines function (lines 23-49)
export async function initializeUserDeadlines(
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  session: mongoose.ClientSession,
  jobId: string,
  enrollmentId: string
): Promise<boolean> {
  try {
    // Check if adjustment record already exists
    const existing = await UserDeadlineAdjustment.findOne({
      user: userId,
      batch: batchId
    }).session(session);
    
    if (existing) {
      logger.info('User deadline adjustment already initialized', {
        jobId, enrollmentId, userId: userId.toString(), batchId: batchId.toString()
      });
      return true;
    }

    // Create new adjustment record (starts with zero adjustments)
    await UserDeadlineAdjustment.getOrCreateAdjustment(userId, batchId, session);
    
    logger.info('User deadline adjustment initialized successfully', {
      jobId, enrollmentId, userId: userId.toString(), batchId: batchId.toString()
    });
    
    return true;
  } catch (error) {
    logger.error('Error initializing user deadline adjustment:', { error, jobId, enrollmentId });
    return false;
  }
}
```

#### 6. **`src/utils/submodule-access.util.ts`**
**Current Issue:** Line 510 calls old UserDeadline.initializeUserDeadlines()
**Required Changes:**
```typescript
// REPLACE line 510
// OLD: await UserDeadline.initializeUserDeadlines(userId, batchId);
// NEW: 
await UserDeadlineAdjustment.getOrCreateAdjustment(userId, batchId);
```

---

## 📁 **PHASE 5: DAO LAYER UPDATES**

#### 7. **`src/dao/user-deadline.dao.ts`**
**Current Issue:** Methods call old UserDeadline static methods
**Required Changes:**
```typescript
// ADD new methods for backward compatibility
async getComputedDeadlines(
  userId: string,
  batchId: string,
  submoduleId?: string
): Promise<ComputedDeadline[]> {
  const userObjId = new mongoose.Types.ObjectId(userId);
  const batchObjId = new mongoose.Types.ObjectId(batchId);
  
  if (submoduleId) {
    const submoduleObjId = new mongoose.Types.ObjectId(submoduleId);
    const deadline = await DeadlineCalculator.getUserSubmoduleDeadline(
      userObjId, batchObjId, submoduleObjId
    );
    return deadline ? [deadline] : [];
  }
  
  return await DeadlineCalculator.getUserBatchDeadlines(userObjId, batchObjId);
}

// UPDATE existing methods to use new system
async adjustDeadline(
  userId: string | mongoose.Types.ObjectId,
  batchId: string | mongoose.Types.ObjectId,
  pauseDays: number,
  pauseStartDate: Date
): Promise<number> {
  const userObjId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
  const batchObjId = typeof batchId === 'string' ? new mongoose.Types.ObjectId(batchId) : batchId;
  
  const result = await UserDeadlineAdjustment.applyBatchPause(
    userObjId, batchObjId, pauseDays, pauseStartDate
  );
  
  return result.success ? 1 : 0; // Return 1 for success, 0 for failure (backward compatibility)
}
```

---

## 📁 **PHASE 6: IMPORT STATEMENTS**

#### 8. **Add Required Imports to All Updated Files:**
```typescript
// Add to files that need the new models:
import UserDeadlineAdjustment from '../models/user-deadline-adjustment.model';
import { DeadlineCalculator } from '../utils/deadline-calculator.util';
import type { ComputedDeadline } from '../utils/deadline-calculator.util';
```

---

## ⚠️ **TESTING REQUIREMENTS**

### Before Deployment:
1. **Test pause functionality** with new system
2. **Test enrollment process** with new deadline initialization  
3. **Test deadline calculations** for accuracy
4. **Test batch deadline updates** (should only affect 50 records, not 10,000)
5. **Test remaining pause days calculation**

### Migration Script:
```typescript
// Run this BEFORE deploying new code
async function migrateExistingData() {
  const userDeadlines = await UserDeadline.aggregate([
    { $group: {
        _id: { user: '$user', batch: '$batch' },
        totalPauseDays: { $first: '$pauseAdjustmentDays' }
      }
    }
  ]);

  for (const group of userDeadlines) {
    await UserDeadlineAdjustment.getOrCreateAdjustment(
      group._id.user, 
      group._id.batch
    );
  }
}
```

---

## 🎯 **DEPLOYMENT ORDER**

1. **Deploy new models** (UserDeadlineAdjustment, DeadlineCalculator)
2. **Update service layer** (user-deadline.service.ts)
3. **Update controllers** (user-deadline.controller.ts, batch-pause.controller.ts)
4. **Update model hooks** (batch-pause.model.ts)
5. **Update job handlers** (job-handler.service.ts)
6. **Update utilities** (submodule-access.util.ts)
7. **Run migration script**
8. **Test thoroughly**
9. **Remove old UserDeadline model** (after validation)

**Missing ANY of these updates will cause production errors!** 🚨
