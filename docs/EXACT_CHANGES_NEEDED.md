# 📋 EXACT CHANGES NEEDED - COPY & PASTE READY

## 🚨 CRITICAL: These are the EXACT changes needed to avoid production errors

---

## **FILE 1: `src/services/public/user-deadline.service.ts`**

**REPLACE ENTIRE FILE CONTENT:**

```typescript
import mongoose from 'mongoose';
import UserDeadlineAdjustment from '../../models/user-deadline-adjustment.model';
import <PERSON>rror<PERSON>andler from '../../utils/error-handler';

export const pauseUserDeadlineAdjustment = async (
  userId: string | mongoose.Types.ObjectId,
  batchId: string | mongoose.Types.ObjectId,
  pauseDays: number,
  pauseStartDate: Date
): Promise<{ success: boolean; totalPauseDays: number; remainingDays: number } | null> => {
  const userObjId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
  const batchObjId = typeof batchId === 'string' ? new mongoose.Types.ObjectId(batchId) : batchId;
  
  const result = await UserDeadlineAdjustment.applyBatchPause(
    userObjId, batchObjId, pauseDays, pauseStartDate
  );
  
  if (!result.success) {
    throw new ErrorHandler(400, `Cannot pause for ${pauseDays} days. Only ${result.remainingDays} days remaining.`);
  }
  
  return result;
};

export const resumePauseEarlyAdjustment = async (
  userId: string | mongoose.Types.ObjectId,
  batchId: string | mongoose.Types.ObjectId,
  actualDaysUsed: number
): Promise<{ success: boolean; daysAdjustedBack: number } | null> => {
  const userObjId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
  const batchObjId = typeof batchId === 'string' ? new mongoose.Types.ObjectId(batchId) : batchId;
  
  const result = await UserDeadlineAdjustment.resumeBatchPause(userObjId, batchObjId, actualDaysUsed);
  
  if (!result.success) {
    throw new ErrorHandler(400, 'Failed to resume batch pause. Please check the user ID and batch ID.');
  }
  
  return result;
};
```

---

## **FILE 2: `src/controllers/public/user-deadline.controller.ts`**

**REPLACE ENTIRE FILE CONTENT:**

```typescript
import { Request, Response, NextFunction } from 'express';
import {
  pauseUserDeadlineAdjustment,
  resumePauseEarlyAdjustment
} from '../../services/public/user-deadline.service';
import asyncHandler from '../../utils/async-handler';
import { IUser } from '../../interfaces/user.interface';
import ResponseHandler from '../../utils/response-handler';
import ErrorHandler from '../../utils/error-handler';

/**
 * Adjust user deadlines when a batch is paused
 */
export const adjustUserDeadlinesOnPause = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { batchId, pauseDays, pauseStartDate } = req.body as {
      batchId: string;
      pauseDays: number;
      pauseStartDate: Date;
    };
    const user = req.user as IUser;
    if (!user) {
      return next(new ErrorHandler(401, 'Unauthorized'));
    }
    
    const response = await pauseUserDeadlineAdjustment(
      user._id,
      batchId,
      pauseDays,
      pauseStartDate
    );
    
    if (!response || !response.success) {
      return next(new ErrorHandler(400, 'Failed to adjust user deadline.'));
    }

    return ResponseHandler.success({
      message: 'User deadline adjusted successfully',
      totalPauseDays: response.totalPauseDays,
      remainingDays: response.remainingDays
    }, 'User deadline adjusted successfully').send(res);
  }
);

export const adjustUserDeadlinesOnResume = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { batchId, actualDaysUsed } = req.body as {
      batchId: string;
      actualDaysUsed: number;
    };
    const user = req.user as IUser;
    if (!user) {
      return next(new ErrorHandler(401, 'Unauthorized'));
    }
    
    const response = await resumePauseEarlyAdjustment(
      user._id,
      batchId,
      actualDaysUsed
    );

    if (!response || !response.success) {
      return next(new ErrorHandler(400, 'Failed to resume batch pause.'));
    }

    return ResponseHandler.success({
      message: 'Batch pause resumed successfully',
      daysAdjustedBack: response.daysAdjustedBack
    }, 'Batch pause resumed successfully').send(res);
  }
);
```

---

## **FILE 3: `src/controllers/public/batch-pause.controller.ts`**

**FIND AND REPLACE lines 72-79:**

```typescript
// FIND THIS (lines 72-79):
    // Adjust user deadline
    await pauseUserDeadlineAdjustment(
      userId,
      batchId,
      daysBetween,
      start.toDate()
    ).catch(error => {
      console.error('Error adjusting user deadline:', error);
    });

// REPLACE WITH:
    // Adjust user deadline
    try {
      const deadlineResult = await pauseUserDeadlineAdjustment(
        userId,
        batchId,
        daysBetween,
        start.toDate()
      );
      if (!deadlineResult?.success) {
        console.warn('Failed to adjust deadlines:', deadlineResult);
      }
    } catch (error) {
      console.error('Error adjusting user deadline:', error);
    }
```

---

## **FILE 4: `src/models/batch-pause.model.ts`**

**FIND AND REPLACE the post-save hook (lines 147-194):**

```typescript
// FIND THIS (lines 147-194):
// Post-save hook to shift deadlines when a batch pause is created
batchPauseSchema.post('save', async function () {
  try {
    // Only process for newly created active pauses
    if (this.isNew && this.status === 'active') {
      const pauseDays = this.daysUsed;
      const userId = this.user;
      const batchId = this.batch;

      // Import the UserDeadline model with proper interface
      interface UserDeadlineModel {
        initializeUserDeadlines(
          userId: mongoose.Types.ObjectId,
          batchId: mongoose.Types.ObjectId
        ): Promise<number>;
        adjustDeadlinesForPause(
          userId: mongoose.Types.ObjectId,
          batchId: mongoose.Types.ObjectId,
          pauseDays: number,
          pauseStartDate: Date
        ): Promise<number>;
      }

      const UserDeadline = mongoose.model(
        'UserDeadline'
      ) as unknown as UserDeadlineModel;

      // First, ensure user deadlines are initialized
      // This is important for users who are pausing for the first time
      await UserDeadline.initializeUserDeadlines(userId, batchId);

      // Now adjust the deadlines for this specific user
      const updatedCount = await UserDeadline.adjustDeadlinesForPause(
        userId,
        batchId,
        pauseDays,
        this.startDate
      );

      // Use console.warn instead of console.log to comply with ESLint rules
      console.warn(
        `Successfully shifted ${updatedCount} deadlines by ${pauseDays} days for user ${userId.toString()} in batch ${batchId.toString()}`
      );
    }
  } catch (error: unknown) {
    console.error('Error shifting deadlines after batch pause:', error);
  }
});

// REPLACE WITH:
// Post-save hook to shift deadlines when a batch pause is created
batchPauseSchema.post('save', async function () {
  try {
    // Only process for newly created active pauses
    if (this.isNew && this.status === 'active') {
      const pauseDays = this.daysUsed;
      const userId = this.user;
      const batchId = this.batch;

      // Use new UserDeadlineAdjustment model
      const UserDeadlineAdjustment = mongoose.model('UserDeadlineAdjustment');
      
      const result = await UserDeadlineAdjustment.applyBatchPause(
        userId,
        batchId,
        pauseDays,
        this.startDate
      );

      if (result.success) {
        console.warn(
          `Successfully applied pause: ${pauseDays} days for user ${userId.toString()} in batch ${batchId.toString()}`
        );
      } else {
        console.error(
          `Failed to apply pause: Only ${result.remainingDays} days remaining for user ${userId.toString()}`
        );
      }
    }
  } catch (error: unknown) {
    console.error('Error applying batch pause:', error);
  }
});
```

---

## **FILE 5: `src/services/private/job-handler.service.ts`**

**ADD IMPORT at the top:**
```typescript
import UserDeadlineAdjustment from '../../models/user-deadline-adjustment.model';
```

**FIND AND REPLACE the initializeUserDeadlines function (lines 23-49):**

```typescript
// FIND THIS (lines 23-49):
export async function initializeUserDeadlines(
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  session: mongoose.ClientSession,
  jobId: string,
  enrollmentId: string
): Promise<boolean> {
  try {
    // Check if deadlines already exist for this user and batch
    const existing = await UserDeadline.findOne({
      user: userId,
      batch: batchId
    }).session(session);
    if (existing) {
      logger.info('User deadlines already initialized', {
        jobId,
        enrollmentId,
        userId: userId.toString(),
        batchId: batchId.toString()
      });
      return true;
    }
    const deadlinesCount = await UserDeadline.initializeUserDeadlines(
      userId,
      batchId,
      session
    );
    // ... rest of function
  }
}

// REPLACE WITH:
export async function initializeUserDeadlines(
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  session: mongoose.ClientSession,
  jobId: string,
  enrollmentId: string
): Promise<boolean> {
  try {
    // Check if adjustment record already exists
    const existing = await UserDeadlineAdjustment.findOne({
      user: userId,
      batch: batchId
    }).session(session);
    
    if (existing) {
      logger.info('User deadline adjustment already initialized', {
        jobId,
        enrollmentId,
        userId: userId.toString(),
        batchId: batchId.toString()
      });
      return true;
    }

    // Create new adjustment record (starts with zero adjustments)
    await UserDeadlineAdjustment.getOrCreateAdjustment(userId, batchId, session);
    
    logger.info('User deadline adjustment initialized successfully', {
      jobId,
      enrollmentId,
      userId: userId.toString(),
      batchId: batchId.toString()
    });
    
    return true;
  } catch (error) {
    logger.error('Error initializing user deadline adjustment:', { error, jobId, enrollmentId });
    return false;
  }
}
```

---

## **FILE 6: `src/utils/submodule-access.util.ts`**

**ADD IMPORT at the top:**
```typescript
import UserDeadlineAdjustment from '../models/user-deadline-adjustment.model';
```

**FIND AND REPLACE line 510:**
```typescript
// FIND THIS (line 510):
      await UserDeadline.initializeUserDeadlines(userId, batchId);

// REPLACE WITH:
      await UserDeadlineAdjustment.getOrCreateAdjustment(userId, batchId);
```

---

## **🎯 DEPLOYMENT CHECKLIST**

- [ ] Update `src/services/public/user-deadline.service.ts`
- [ ] Update `src/controllers/public/user-deadline.controller.ts`  
- [ ] Update `src/controllers/public/batch-pause.controller.ts`
- [ ] Update `src/models/batch-pause.model.ts`
- [ ] Update `src/services/private/job-handler.service.ts`
- [ ] Update `src/utils/submodule-access.util.ts`
- [ ] Test pause functionality
- [ ] Test enrollment process
- [ ] Deploy to production

**⚠️ ALL changes above are MANDATORY to prevent production errors!**
