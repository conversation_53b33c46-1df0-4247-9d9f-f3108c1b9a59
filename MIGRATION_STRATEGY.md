# Migration Strategy: From User Deadlines to Computed Deadlines

## Overview
This migration will transform your deadline system from storing 10,000+ individual user deadline records to storing only ~200 user adjustment records, dramatically improving performance.

## Performance Impact
- **Before**: 10,050 writes for 1 submodule deadline update
- **After**: 50 writes for 1 submodule deadline update (only batch deadlines)
- **Improvement**: 99.5% reduction in writes

## Migration Steps

### Phase 1: Deploy New Models (Zero Downtime)
1. Deploy the new models alongside existing ones
2. No breaking changes to existing functionality
3. Start using new system for new users/batches

### Phase 2: Data Migration Script
Create a script to migrate existing user deadlines to the new format:

```javascript
// migration/migrate-user-deadlines.js
async function migrateUserDeadlines() {
  const userDeadlines = await UserDeadline.aggregate([
    {
      $group: {
        _id: { user: '$user', batch: '$batch' },
        totalPauseDays: { $first: '$pauseAdjustmentDays' },
        submodules: {
          $push: {
            submodule: '$submodule',
            pauseAdjustmentDays: '$pauseAdjustmentDays',
            originalDeadline: '$originalDeadline',
            adjustedDeadline: '$adjustedDeadline',
            adjustedStartDate: '$adjustedStartDate'
          }
        }
      }
    }
  ]);

  for (const userGroup of userDeadlines) {
    const { user, batch } = userGroup._id;
    
    // Create user adjustment record
    const adjustment = new UserDeadlineAdjustment({g
      user,
      batch,
      totalPauseDays: userGroup.totalPauseDays,
      pauseHistory: userGroup.totalPauseDays > 0 ? [{
        startDate: new Date(), // Approximate
        daysUsed: userGroup.totalPauseDays,
        status: 'completed'
      }] : [],
      submoduleAdjustments: [],
      isActive: true
    });

    await adjustment.save();
  }
}
```

### Phase 3: Update Controllers
Replace existing deadline queries with the new calculator:

```javascript
// Before
const userDeadlines = await UserDeadline.find({
  user: userId,
  batch: batchId,
  isActive: true
});

// After
const userDeadlines = await DeadlineCalculator.getUserBatchDeadlines(
  userId,
  batchId
);
```

### Phase 4: Update Pause Logic
```javascript
// Before - Updates 50+ records
await UserDeadline.adjustDeadlinesForPause(userId, batchId, pauseDays, startDate);

// After - Updates 1 record
await UserDeadlineAdjustment.applyBatchPause(userId, batchId, pauseDays, startDate);
```

### Phase 5: Remove Old Model
After thorough testing, remove the old UserDeadline model.

## API Changes Required

### 1. Batch Pause Endpoint
```javascript
// controllers/batch-pause.controller.ts
async pauseBatch(req, res) {
  const { userId, batchId, pauseDays, startDate } = req.body;
  
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    // Apply pause using new system
    const result = await UserDeadlineAdjustment.applyBatchPause(
      userId, batchId, pauseDays, new Date(startDate), session
    );
    
    if (!result.success) {
      return res.status(400).json({
        message: `Cannot pause for ${pauseDays} days. Only ${result.remainingDays} days remaining.`
      });
    }
    
    await session.commitTransaction();
    res.json({ 
      message: 'Batch paused successfully',
      totalPauseDays: result.totalPauseDays,
      remainingDays: result.remainingDays
    });
  } catch (error) {
    await session.abortTransaction();
    res.status(500).json({ message: 'Error pausing batch' });
  } finally {
    session.endSession();
  }
}
```

### 2. Get User Progress Endpoint
```javascript
// controllers/progress.controller.ts
async getUserProgress(req, res) {
  const { userId, batchId } = req.params;
  
  try {
    // Get computed deadlines
    const deadlines = await DeadlineCalculator.getUserBatchDeadlines(
      new mongoose.Types.ObjectId(userId),
      new mongoose.Types.ObjectId(batchId)
    );
    
    // Get remaining pause days
    const remainingPauseDays = await DeadlineCalculator.getRemainingPauseDays(
      new mongoose.Types.ObjectId(userId),
      new mongoose.Types.ObjectId(batchId)
    );
    
    res.json({
      deadlines,
      remainingPauseDays,
      hasActivePause: await DeadlineCalculator.hasActivePause(
        new mongoose.Types.ObjectId(userId),
        new mongoose.Types.ObjectId(batchId)
      )
    });
  } catch (error) {
    res.status(500).json({ message: 'Error fetching user progress' });
  }
}
```

### 3. Update Submodule Deadline (Admin)
```javascript
// controllers/admin/deadline.controller.ts
async updateSubmoduleDeadline(req, res) {
  const { submoduleId, newDeadline } = req.body;
  
  try {
    // Only update batch deadlines - no cascade needed!
    const result = await BatchSubmoduleDeadline.updateMany(
      { submodule: submoduleId, isActive: true },
      { deadline: new Date(newDeadline) }
    );
    
    res.json({
      message: `Updated ${result.modifiedCount} batch deadlines`,
      modifiedCount: result.modifiedCount
    });
  } catch (error) {
    res.status(500).json({ message: 'Error updating deadline' });
  }
}
```

## Testing Strategy

### 1. Unit Tests
```javascript
// tests/deadline-calculator.test.js
describe('DeadlineCalculator', () => {
  test('should calculate deadline with pause adjustment', async () => {
    const deadline = await DeadlineCalculator.getUserSubmoduleDeadline(
      userId, batchId, submoduleId
    );
    
    expect(deadline.pauseAdjustmentDays).toBe(7);
    expect(deadline.deadline).toEqual(expectedDate);
  });
  
  test('should handle submodule exemptions', async () => {
    // Add exemption
    await UserDeadlineAdjustment.addSubmoduleAdjustment(
      userId, batchId, submoduleId, { isExempt: true }
    );
    
    const deadline = await DeadlineCalculator.getUserSubmoduleDeadline(
      userId, batchId, submoduleId
    );
    
    expect(deadline.isExempt).toBe(true);
  });
});
```

### 2. Performance Tests
```javascript
// tests/performance.test.js
describe('Performance Comparison', () => {
  test('deadline update performance', async () => {
    const startTime = Date.now();
    
    // Update submodule deadline
    await BatchSubmoduleDeadline.updateMany(
      { submodule: submoduleId },
      { deadline: newDeadline }
    );
    
    const endTime = Date.now();
    expect(endTime - startTime).toBeLessThan(100); // Should be very fast
  });
});
```

## Rollback Plan
If issues arise:
1. Keep old UserDeadline model during migration
2. Add feature flag to switch between systems
3. Can revert to old system instantly
4. Migrate back if needed

## Benefits Summary
1. **99.5% reduction** in deadline update operations
2. **95% reduction** in storage for deadline data
3. **Faster queries** due to smaller collections
4. **Better scalability** as user base grows
5. **Simpler maintenance** with computed deadlines
6. **Flexible adjustments** for individual users/submodules

## Next Steps
1. Review and approve this migration strategy
2. Deploy new models to staging
3. Run migration script on test data
4. Update controllers and test thoroughly
5. Deploy to production with feature flag
6. Monitor performance improvements
7. Remove old system after validation
