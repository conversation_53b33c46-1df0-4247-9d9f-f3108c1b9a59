# Dependencies
node_modules/
package-lock.json
yarn.lock
pnpm-lock.yaml

# Build outputs
dist/
build/
out/
.next/
coverage/

# Generated files
.nyc_output/
*.min.js
*.bundle.js

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Cache directories
.cache/
.npm/
.eslintcache
.stylelintcache

# Environment variables
.env
.env.*

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Other files
public/assets/