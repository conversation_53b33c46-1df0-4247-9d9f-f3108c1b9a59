# Sheryians 3.0 Classroom Flow - Developer Implementation Guide

This document provides a comprehensive implementation guide for the Sheryians 3.0 classroom platform, from user enrollment to certificate completion. It includes detailed business logic, controller implementations, data validation rules, and practical examples to guide developers through the entire development process.

## Table of Contents

1. [System Overview](#system-overview)
2. [Development Environment Setup](#development-environment-setup)
3. [Database Schema Implementation](#database-schema-implementation)
4. [User Enrollment Flow](#user-enrollment-flow)
5. [Instructor Content Management](#instructor-content-management)
6. [Classroom Structure](#classroom-structure)
7. [Content Management](#content-management)
8. [Learning Flow](#learning-flow)
9. [Batch Management](#batch-management)
10. [Progress Tracking](#progress-tracking)
11. [Assessment System](#assessment-system)
12. [Doubt Resolution System](#doubt-resolution-system)
13. [Bookmarking System](#bookmarking-system)
14. [Code Pair Sessions](#code-pair-sessions)
15. [Certification Process](#certification-process)
16. [API Routes & Controller Implementation](#api-routes--controller-implementation)
17. [Authentication & Authorization](#authentication--authorization)
18. [Error Handling & Logging](#error-handling--logging)
19. [Testing Strategy](#testing-strategy)
20. [Deployment Process](#deployment-process)
21. [Edge Cases and Considerations](#edge-cases-and-considerations)
22. [Performance Optimization](#performance-optimization)

## System Overview

The Sheryians 3.0 classroom platform is built with a modular architecture that separates the pre-purchase experience (monolithic) from the post-purchase classroom features. The classroom system focuses on providing a structured learning experience with progress tracking, assessments, and interactive features.

### Key Components:

- **User Management**: Authentication, enrollment, and user profiles
- **Course Structure**: Courses, modules, submodules, and content
- **Content Types**: Videos, notes, MCQs, coding problems, and coding labs
- **Progress Tracking**: User progress at course, module, submodule, and content levels
- **Assessment System**: MCQs and coding problems with automated evaluation
- **Batch Management**: Deadlines, pausing, and batch-specific settings
- **Support Systems**: Doubt resolution, code pair sessions, and announcements

### Technical Stack:

- **Backend**: Node.js with Express.js
- **Database**: MongoDB with Mongoose ODM
- **Caching**: Redis for performance optimization
- **Authentication**: JWT-based authentication
- **Storage**: AWS S3 for media storage
- **Video Hosting**: Generic video service integration (not tied to a specific provider)
- **Code Execution**: Judge0 API for code compilation and execution
- **Real-time Communication**: Socket.io for real-time features

### System Architecture:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Client (React) │────▶│  API Gateway    │────▶│  Auth Service   │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                │
                                │
                                ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Content Service│◀───▶│  Core Service   │────▶│  Progress Service│
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                │
                                │
                                ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Doubt Service  │◀───▶│  Assessment     │◀───▶│  Code Execution │
│                 │     │  Service        │     │  Service        │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Development Approach:

We'll follow a modular monolith approach initially, with clear boundaries between different domains to allow for future microservice extraction if needed. This approach provides:

1. Faster initial development
2. Simpler deployment and maintenance
3. Clear domain boundaries
4. Future scalability options

## Development Environment Setup

### Prerequisites:

1. Node.js (v16+)
2. MongoDB (v4.4+)
3. Redis (v6+)
4. Git
5. Docker (optional, for containerization)
6. AWS Account (for S3 storage)
7. Judge0 API access (for code execution)

### Local Setup Steps:

1. **Clone the repository**:

   ```bash
   git clone https://github.com/your-org/classroom-sheryians-3.0.git
   cd classroom-sheryians-3.0
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Environment configuration**:
   Create a `.env` file with the following variables:

   ```
   # Server
   PORT=3000
   NODE_ENV=development

   # Database
   MONGODB_URI=mongodb://localhost:27017/sheryians-classroom

   # Redis
   REDIS_URL=redis://localhost:6379

   # JWT
   JWT_SECRET=your-secret-key
   JWT_EXPIRES_IN=7d

   # AWS
   AWS_ACCESS_KEY_ID=your-access-key
   AWS_SECRET_ACCESS_KEY=your-secret-key
   AWS_REGION=ap-south-1
   AWS_S3_BUCKET=your-bucket-name

   # Judge0
   JUDGE0_API_KEY=your-api-key
   JUDGE0_API_URL=https://judge0-ce.p.rapidapi.com

   # Email
   EMAIL_SERVICE=smtp
   EMAIL_HOST=smtp.example.com
   EMAIL_PORT=587
   EMAIL_USER=your-email
   EMAIL_PASSWORD=your-password

   # Pre-purchase integration
   PRE_PURCHASE_URL=http://localhost:4000
   SERVER_API_KEY=your-api-key
   ```

4. **Start development server**:

   ```bash
   npm run dev
   ```

5. **Database seeding** (optional):
   ```bash
   npm run seed
   ```

### Docker Setup (Alternative):

```bash
# Build the Docker image
docker-compose build

# Start the services
docker-compose up -d

# Stop the services
docker-compose down
```

## Database Schema Implementation

Before diving into implementation, let's understand the core database schemas and their relationships. We're using MongoDB with Mongoose, so our schemas will be defined as Mongoose models.

### Core Models and Their Relationships:

```
User ────┐
         │
         ▼
      Enrollment ───┐
         │          │
         │          ▼
         │       UserProgress
         │          │
         ▼          │
      Batch ────────┘
         │
         ▼
Course ──┼──▶ Module ──▶ Submodule
         │                  │
         │                  ▼
         │          SubmoduleContent
         │                  │
         │                  ▼
         └──────────────────┼──▶ Video
                            │
                            ├──▶ Note
                            │
                            ├──▶ MCQ
                            │
                            ├──▶ CodingProblem
                            │
                            └──▶ CodingLab
```

### Key Schema Implementations:

1. **User Schema**:

   ```javascript
   const userSchema = new Schema(
     {
       name: { type: String, required: true },
       email: { type: String, required: true, unique: true },
       password: { type: String, required: true },
       role: {
         type: String,
         enum: ['student', 'instructor', 'mentor', 'admin'],
         default: 'student'
       },
       avatar: String,
       phone: String,
       isActive: { type: Boolean, default: true },
       lastLogin: Date
     },
     { timestamps: true }
   );
   ```

2. **Course Schema**:

   ```javascript
   const courseSchema = new Schema(
     {
       title: { type: String, required: true },
       description: { type: String, required: true },
       thumbnail: String,
       duration: Number, // in weeks
       level: {
         type: String,
         enum: ['beginner', 'intermediate', 'advanced'],
         default: 'beginner'
       },
       isActive: { type: Boolean, default: true }
     },
     { timestamps: true }
   );
   ```

3. **Module Schema**:

   ```javascript
   const moduleSchema = new Schema(
     {
       title: { type: String, required: true },
       description: { type: String, required: true },
       course: {
         type: mongoose.Schema.Types.ObjectId,
         ref: 'Course',
         required: true
       },
       order: { type: Number, required: true },
       duration: Number, // in minutes
       isActive: { type: Boolean, default: true }
     },
     { timestamps: true }
   );
   ```

4. **Submodule Schema**:

   ```javascript
   const submoduleSchema = new Schema(
     {
       title: { type: String, required: true },
       description: { type: String, required: true },
       module: {
         type: mongoose.Schema.Types.ObjectId,
         ref: 'Module',
         required: true
       },
       order: { type: Number, required: true },
       duration: Number, // in minutes
       points: { type: Number, default: 0 },
       isActive: { type: Boolean, default: true }
     },
     { timestamps: true }
   );
   ```

5. **Content Type Schemas**:

   **Video Schema**:

   ```javascript
   const videoSchema = new Schema(
     {
       title: { type: String, required: true },
       description: { type: String, required: true },
       videoId: { type: String, required: true }, // Generic ID from video service
       provider: String, // e.g., 'vdocipher', 'youtube'
       duration: Number, // in seconds
       points: { type: Number, default: 1 },
       tags: [String],
       isActive: { type: Boolean, default: true }
     },
     { timestamps: true }
   );
   ```

   **Note Schema**:

   ```javascript
   const noteSchema = new Schema(
     {
       title: { type: String, required: true },
       content: { type: String, required: true },
       images: [String], // Array of image URLs
       points: { type: Number, default: 1 },
       tags: [String],
       isActive: { type: Boolean, default: true }
     },
     { timestamps: true }
   );
   ```

   **MCQ Schema**:

   ```javascript
   const mcqSchema = new Schema(
     {
       title: { type: String, required: true },
       question: { type: String, required: true },
       image: String,
       codeSnippets: [
         {
           language: { type: String, required: true },
           code: { type: String, required: true }
         }
       ],
       options: [
         {
           text: { type: String, required: true },
           isCorrect: { type: Boolean, required: true }
         }
       ],
       selectionType: {
         type: String,
         enum: ['single', 'multiple'],
         default: 'single'
       },
       points: { type: Number, default: 1 },
       explanation: {
         text: String,
         video: String,
         image: String
       },
       difficulty: {
         type: String,
         enum: ['easy', 'medium', 'hard'],
         default: 'medium'
       },
       tags: [String],
       isActive: { type: Boolean, default: true }
     },
     { timestamps: true }
   );
   ```

6. **Submodule Content Schema** (for linking content to submodules):

   ```javascript
   const submoduleContentSchema = new Schema(
     {
       submodule: {
         type: mongoose.Schema.Types.ObjectId,
         ref: 'Submodule',
         required: true
       },
       contentType: {
         type: String,
         enum: ['video', 'note', 'mcq', 'coding_problem', 'coding_lab'],
         required: true
       },
       contentId: {
         type: mongoose.Schema.Types.ObjectId,
         refPath: 'contentModel',
         required: true
       },
       contentModel: {
         type: String,
         required: true,
         enum: ['Video', 'Note', 'MCQ', 'CodingProblem', 'CodingLab']
       },
       section: {
         type: String,
         enum: ['lesson', 'practice'],
         required: true
       },
       order: {
         type: Number,
         required: true
       },
       isActive: { type: Boolean, default: true }
     },
     { timestamps: true }
   );
   ```

7. **User Progress Schema**:

   ```javascript
   const userProgressSchema = new Schema(
     {
       user: {
         type: mongoose.Schema.Types.ObjectId,
         ref: 'User',
         required: true
       },
       course: {
         type: mongoose.Schema.Types.ObjectId,
         ref: 'Course',
         required: true
       },
       batch: {
         type: mongoose.Schema.Types.ObjectId,
         ref: 'Batch',
         required: true
       },
       overallProgress: {
         type: Number,
         default: 0,
         min: 0,
         max: 100
       },
       totalPointsEarned: {
         type: Number,
         default: 0
       },
       moduleProgress: [
         {
           module: {
             type: mongoose.Schema.Types.ObjectId,
             ref: 'Module',
             required: true
           },
           progress: {
             type: Number,
             default: 0,
             min: 0,
             max: 100
           },
           pointsEarned: {
             type: Number,
             default: 0
           },
           completedAt: Date
         }
       ],
       subModuleProgress: [
         {
           subModule: {
             type: mongoose.Schema.Types.ObjectId,
             ref: 'Submodule',
             required: true
           },
           completed: {
             type: Boolean,
             default: false
           },
           pointsEarned: {
             type: Number,
             default: 0
           },
           completedAt: Date
         }
       ],
       contentItemProgress: [
         {
           contentType: {
             type: String,
             enum: ['video', 'note', 'mcq', 'coding_problem', 'coding_lab'],
             required: true
           },
           contentId: {
             type: mongoose.Schema.Types.ObjectId,
             refPath: 'contentModel',
             required: true
           },
           contentModel: {
             type: String,
             enum: ['Video', 'Note', 'MCQ', 'CodingProblem', 'CodingLab'],
             required: true
           },
           completed: {
             type: Boolean,
             default: false
           },
           pointsEarned: {
             type: Number,
             default: 0
           },
           completedAt: Date,
           // For video content
           watchedDuration: {
             type: Number,
             default: 0
           },
           // For MCQs
           selectedOptions: [String],
           attempts: {
             type: Number,
             default: 0
           },
           // For coding problems and labs
           submissions: [
             {
               code: String,
               language: String,
               submittedAt: {
                 type: Date,
                 default: Date.now
               },
               testCaseResults: [
                 {
                   testCaseId: String,
                   passed: Boolean,
                   output: String,
                   pointsEarned: {
                     type: Number,
                     default: 0
                   }
                 }
               ],
               totalPoints: {
                 type: Number,
                 default: 0
               }
             }
           ]
         }
       ],
       lastActivityAt: {
         type: Date,
         default: Date.now
       }
     },
     { timestamps: true }
   );
   ```

8. **Batch Schema**:

   ```javascript
   const batchSchema = new Schema(
     {
       name: { type: String, required: true },
       course: {
         type: mongoose.Schema.Types.ObjectId,
         ref: 'Course',
         required: true
       },
       startDate: {
         type: Date,
         required: true
       },
       endDate: {
         type: Date,
         required: true
       },
       maxStudents: {
         type: Number,
         default: 100
       },
       enrolledStudents: {
         type: Number,
         default: 0
       },
       mentors: [
         {
           type: mongoose.Schema.Types.ObjectId,
           ref: 'User'
         }
       ],
       isActive: {
         type: Boolean,
         default: true
       }
     },
     { timestamps: true }
   );
   ```

9. **Batch Submodule Deadline Schema**:

   ```javascript
   const batchSubmoduleDeadlineSchema = new Schema(
     {
       batch: {
         type: mongoose.Schema.Types.ObjectId,
         ref: 'Batch',
         required: true
       },
       submodule: {
         type: mongoose.Schema.Types.ObjectId,
         ref: 'Submodule',
         required: true
       },
       startDate: {
         type: Date,
         required: true
       },
       deadline: {
         type: Date,
         required: true
       },
       dependsOnPreviousSubmodule: {
         type: Boolean,
         default: true
       },
       isActive: {
         type: Boolean,
         default: true
       }
     },
     { timestamps: true }
   );
   ```

10. **User Deadline Schema** (for user-specific deadlines):

    ```javascript
    const userDeadlineSchema = new Schema(
      {
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true
        },
        batch: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Batch',
          required: true
        },
        submodule: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Submodule',
          required: true
        },
        originalDeadline: {
          type: Date,
          required: true
        },
        adjustedDeadline: {
          type: Date,
          required: true
        },
        adjustedStartDate: {
          type: Date,
          required: true
        },
        pauseAdjustmentDays: {
          type: Number,
          default: 0
        },
        isActive: {
          type: Boolean,
          default: true
        }
      },
      { timestamps: true }
    );
    ```

11. **Batch Pause Schema**:
    ```javascript
    const batchPauseSchema = new Schema(
      {
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true
        },
        batch: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Batch',
          required: true
        },
        startDate: {
          type: Date,
          required: true
        },
        endDate: Date,
        duration: {
          type: Number, // in days
          required: true
        },
        status: {
          type: String,
          enum: ['active', 'completed', 'cancelled'],
          default: 'active'
        },
        reason: String
      },
      { timestamps: true }
    );
    ```

### Schema Validation and Business Rules:

1. **Implement pre-save hooks** for validation and data processing:

   ```javascript
   // Example: Hash password before saving
   userSchema.pre('save', async function (next) {
     if (!this.isModified('password')) return next();

     try {
       const salt = await bcrypt.genSalt(10);
       this.password = await bcrypt.hash(this.password, salt);
       next();
     } catch (error) {
       next(error);
     }
   });
   ```

2. **Add custom methods** to schemas for business logic:

   ```javascript
   // Example: Check if batch can be paused
   batchSchema.methods.canBePaused = async function (userId) {
     const totalPauseDays = await BatchPause.getTotalPauseDays(
       userId,
       this._id
     );
     return totalPauseDays < 60; // 60-day limit
   };
   ```

3. **Create static methods** for common operations:

   ```javascript
   // Example: Get total pause days for a user in a batch
   batchPauseSchema.statics.getTotalPauseDays = async function (
     userId,
     batchId
   ) {
     const pauses = await this.find({
       user: userId,
       batch: batchId,
       status: { $in: ['active', 'completed'] }
     });

     return pauses.reduce((total, pause) => {
       if (pause.status === 'completed') {
         return total + pause.duration;
       } else {
         // For active pauses, calculate days so far
         const today = new Date();
         const startDate = new Date(pause.startDate);
         const daysSoFar = Math.floor(
           (today - startDate) / (1000 * 60 * 60 * 24)
         );
         return total + daysSoFar;
       }
     }, 0);
   };
   ```

4. **Create indexes** for performance optimization:
   ```javascript
   // Example: Create indexes for common queries
   userProgressSchema.index({ user: 1, course: 1, batch: 1 }, { unique: true });
   userProgressSchema.index({ user: 1, lastActivityAt: -1 });
   submoduleContentSchema.index({ submodule: 1, section: 1, order: 1 });
   ```

## User Enrollment Flow

### 1. Pre-Purchase to Post-Purchase Transition

```mermaid
graph TD
    A[User purchases course] --> B[Enrollment created]
    B --> C[User added to batch]
    C --> D[Initialize user deadlines]
    D --> E[Create user progress record]
    E --> F[User gains access to classroom]
```

### 2. Enrollment Process

1. **Purchase Confirmation**

   - User completes payment in the pre-purchase system
   - Webhook triggers enrollment creation

2. **Batch Assignment**

   - User is assigned to the current active batch
   - System initializes batch-specific deadlines for the user

3. **Initial Setup**
   - Create user progress record
   - Initialize module and submodule progress tracking
   - Set up user-specific deadlines based on batch deadlines

### Routes to Implement:

- `POST /api/enrollments` - Create a new enrollment (internal)
- `GET /api/users/enrolled-courses` - Get user's enrolled courses
- `GET /api/classroom/dashboard` - Get user's classroom dashboard

### Controller Implementation Guidelines:

1. **Enrollment Controller**:

   - Validate incoming request data using Joi or similar validation library
   - Check if user exists and is eligible for enrollment
   - Find active batch for the course with available slots
   - Verify user isn't already enrolled in the course
   - Create enrollment record with appropriate status
   - Initialize user progress tracking records
   - Set up user-specific deadlines based on batch deadlines

2. **Request Validation**:

   - Validate ObjectIds for user and course references
   - Ensure payment information is complete and valid
   - Implement custom validators for domain-specific rules

3. **Route Configuration**:

   - Protect enrollment creation with API key for internal use
   - Secure user routes with JWT authentication
   - Implement role-based authorization for admin functions

4. **API Key Middleware**:

   - Validate API key from headers for internal API calls
   - Return appropriate error responses for invalid keys

5. **Authentication Middleware**:
   - Extract and verify JWT tokens
   - Check if user exists and is active
   - Add user information to request object
   - Implement role-based authorization

### Integration with Pre-Purchase System:

The pre-purchase system will call our enrollment API when a user completes payment:

- Verify webhook signatures for security
- Extract user, course, and payment information
- Call enrollment API with appropriate data
- Handle success and error cases appropriately

## Instructor Content Management

Instructors need a comprehensive set of tools to create, manage, and organize course content. This section outlines the instructor workflows and APIs needed for content management.

### 1. Content Creation Workflow

```mermaid
graph TD
    A[Instructor logs in] --> B[Select course to manage]
    B --> C[Create/edit module]
    C --> D[Create/edit submodule]
    D --> E[Add content to submodule]
    E --> F[Organize content in sections]
    F --> G[Set deadlines]
    G --> H[Publish content]
```

### 2. Content Creation APIs

#### Module Management

- `POST /api/instructor/courses/:courseId/modules` - Create a new module
- `PUT /api/instructor/modules/:moduleId` - Update module details
- `DELETE /api/instructor/modules/:moduleId` - Delete a module
- `PUT /api/instructor/modules/reorder` - Reorder modules in a course

#### Submodule Management

- `POST /api/instructor/modules/:moduleId/submodules` - Create a new submodule
- `PUT /api/instructor/submodules/:submoduleId` - Update submodule details
- `DELETE /api/instructor/submodules/:submoduleId` - Delete a submodule
- `PUT /api/instructor/submodules/reorder` - Reorder submodules in a module

#### Content Creation

- `POST /api/instructor/content/video` - Create a new video
- `POST /api/instructor/content/note` - Create a new note
- `POST /api/instructor/content/mcq` - Create a new MCQ
- `POST /api/instructor/content/coding-problem` - Create a new coding problem
- `POST /api/instructor/content/coding-lab` - Create a new coding lab

#### Content Organization

- `POST /api/instructor/submodules/:submoduleId/content` - Add content to submodule
- `PUT /api/instructor/submodules/:submoduleId/content/:contentId` - Update content in submodule
- `DELETE /api/instructor/submodules/:submoduleId/content/:contentId` - Remove content from submodule
- `PUT /api/instructor/submodules/:submoduleId/content/reorder` - Reorder content in submodule

### 3. Content Creation Guidelines

#### Video Content

- Upload videos to the configured video provider (e.g., VdoCipher)
- Set proper titles and descriptions
- Add timestamps for important sections
- Keep videos under 20 minutes for better engagement
- Include downloadable resources when applicable

#### Notes

- Use markdown for formatting
- Include images and diagrams where helpful
- Organize with clear headings and subheadings
- Link to external resources when appropriate
- Include code snippets with proper syntax highlighting

#### MCQs

- Write clear, unambiguous questions
- Provide 4-6 options for each question
- For multiple-answer questions, clearly indicate that multiple selections are required
- Include explanations for both correct and incorrect answers
- Use code snippets or images when relevant

#### Coding Problems

- Write clear problem statements with examples
- Define input/output formats precisely
- Create comprehensive test cases (including edge cases)
- Provide starter code for each supported language
- Include hints that guide without giving away the solution
- Provide a model solution for each supported language

#### Coding Labs

- Create step-by-step instructions
- Define clear objectives for each lab
- Provide starter code and resources
- Include validation criteria
- Create a reference solution

### 4. Batch Deadline Management

- `POST /api/instructor/batches/:batchId/deadlines` - Set deadlines for a batch
- `PUT /api/instructor/batches/:batchId/deadlines/:submoduleId` - Update deadline for a submodule
- `GET /api/instructor/batches/:batchId/deadlines` - Get all deadlines for a batch

### 5. Content Reusability

Instructors should be encouraged to create reusable content that can be shared across different courses and submodules:

- Content is created independently of courses/submodules
- Content is linked to submodules through the SubmoduleContent model
- The same content can be used in multiple submodules
- Updates to content are reflected everywhere it's used

### 6. Implementation Considerations

1. **Content Validation**:

   - Validate all content before saving
   - Ensure required fields are present
   - Validate MCQ options (at least one correct answer)
   - Validate coding problem test cases

2. **Media Handling**:

   - Use presigned URLs for secure media uploads
   - Process and optimize images before storage
   - Store media files in S3 or similar storage
   - Generate thumbnails for videos

3. **Version Control**:

   - Consider implementing content versioning
   - Allow instructors to see content history
   - Enable reverting to previous versions

4. **Permissions**:
   - Only course instructors can add/edit content
   - Course admins can manage instructor access
   - Content creators can only edit their own content unless given explicit permission

## Classroom Structure

### 1. Hierarchical Organization

```
Course
├── Module 1
│   ├── Submodule 1.1
│   │   ├── Lesson Section
│   │   │   ├── Video
│   │   │   ├── Note
│   │   │   └── MCQ
│   │   └── Practice Section
│   │       ├── MCQ
│   │       ├── Coding Problem
│   │       └── Coding Lab
│   └── Submodule 1.2
└── Module 2
```

### 2. Content Organization

- **Lesson Section**: Contains videos, notes, and basic MCQs for learning concepts
- **Practice Section**: Contains MCQs, coding problems, and coding labs for applying concepts

### Routes to Implement:

- `GET /api/courses/:courseId` - Get course details
- `GET /api/courses/:courseId/modules` - Get modules for a course
- `GET /api/modules/:moduleId/submodules` - Get submodules for a module
- `GET /api/submodules/:submoduleId/content` - Get content for a submodule

## Content Management

### 1. Content Types

- **Video**: Video content with tracking for watched duration
- **Note**: Text-based content with images
- **MCQ**: Multiple-choice questions with single or multiple correct answers
- **Coding Problem**: Programming challenges with test cases
- **Coding Lab**: Interactive coding environments with instructions

### 2. Content Reusability

Content items are designed to be reusable across different courses and submodules without duplication. The `SubmoduleContent` model links content to submodules with section and order information.

### 3. Content Access Flow

```mermaid
graph TD
    A[User selects submodule] --> B[Check unlock status]
    B -->|Unlocked| C[Fetch submodule content]
    C --> D[Organize by section]
    D --> E[Present content in order]
    B -->|Locked| F[Show unlock requirements]
```

### Routes to Implement:

- `GET /api/content/video/:id` - Get video details
- `GET /api/content/note/:id` - Get note details
- `GET /api/content/mcq/:id` - Get MCQ details
- `GET /api/content/coding-problem/:id` - Get coding problem details
- `GET /api/content/coding-lab/:id` - Get coding lab details
- `POST /api/content/video/:id/progress` - Update video progress
- `POST /api/content/mcq/:id/submit` - Submit MCQ answer
- `POST /api/content/coding-problem/:id/submit` - Submit coding solution

## Learning Flow

### 1. User Journey

```mermaid
graph TD
    A[Login to classroom] --> B[View enrolled courses]
    B --> C[Select course]
    C --> D[View modules]
    D --> E[Select module]
    E --> F[View submodules]
    F --> G[Select submodule]
    G --> H[Access content]
    H --> I[Complete content items]
    I --> J[Take assessments]
    J --> K[Move to next submodule]
    K --> L[Complete module]
    L --> M[Complete course]
    M --> N[Receive certificate]
```

### 2. Content Progression

1. **Submodule Access**

   - Submodules are unlocked based on completion of previous submodules
   - First submodule is always unlocked
   - Submodules may have specific start dates

2. **Content Completion**
   - Videos: Mark as complete after watching (threshold percentage)
   - Notes: Mark as complete after viewing
   - MCQs: Mark as complete after correct answer
   - Coding Problems: Mark as complete after passing test cases
   - Coding Labs: Mark as complete after submitting working solution

### Routes to Implement:

- `GET /api/classroom/courses/:courseId/progress` - Get course progress
- `GET /api/submodules/:submoduleId/unlock-status` - Check if submodule is unlocked
- `POST /api/content/:type/:id/complete` - Mark content as complete

## Batch Management

### 1. Batch Structure

- Each course has multiple batches
- Batches have specific start and end dates
- Submodules have batch-specific deadlines

### 2. Batch Pause Feature

```mermaid
graph TD
    A[User requests batch pause] --> B[Check remaining pause days]
    B -->|Days available| C[Create pause record]
    C --> D[Adjust all future deadlines]
    D --> E[Notify user of new deadlines]
    B -->|No days left| F[Show error message]
```

- Users can pause their batch for up to 60 days total
- Pausing shifts all future deadlines forward by the pause duration
- Users can resume a batch pause at any time
- No approval required from instructors or mentors

### Routes to Implement:

- `GET /api/batches/:batchId` - Get batch details
- `GET /api/batches/:batchId/deadlines` - Get batch deadlines
- `POST /api/batches/:batchId/pause` - Pause batch
- `POST /api/batches/:batchId/resume` - Resume batch
- `GET /api/batches/:batchId/pause-history` - Get pause history

## Progress Tracking

### 1. Progress Levels

- **Course Level**: Overall completion percentage
- **Module Level**: Completion percentage per module
- **Submodule Level**: Completion status (complete/incomplete)
- **Content Level**: Completion status and details per content item

### 2. Progress Data Structure

The `UserProgress` model tracks:

- Overall course progress
- Module-specific progress
- Submodule completion status
- Content item progress with type-specific details:
  - Video: Watched duration
  - MCQ: Selected options, attempts
  - Coding: Submissions, test case results

### 3. Points System

- Each content item has assigned points
- Points accumulate as users complete content
- Points contribute to leaderboard rankings
- Late submissions may have point penalties

### Routes to Implement:

- `GET /api/users/progress/:courseId` - Get user progress for a course
- `GET /api/users/progress/:courseId/:moduleId` - Get module progress
- `GET /api/users/progress/:courseId/points` - Get points earned
- `GET /api/leaderboard/:batchId` - Get batch leaderboard

## Assessment System

### 1. MCQ Assessments

- Single or multiple correct answers
- Support for code snippets and images
- Immediate feedback with explanations
- Limited attempts based on configuration

### 2. Coding Assessments

```mermaid
graph TD
    A[User views coding problem] --> B[Read problem statement]
    B --> C[Write solution]
    C --> D[Submit solution]
    D --> E[Run against test cases]
    E -->|All tests pass| F[Mark as complete]
    E -->|Some tests fail| G[Show feedback]
    G --> C
```

- Multiple test cases with hidden tests
- Support for multiple programming languages
- Automated evaluation using Judge0
- Points assigned per test case
- Hints and solutions available

### Routes to Implement:

- `POST /api/mcq/:id/submit` - Submit MCQ answer
- `POST /api/coding-problem/:id/submit` - Submit coding solution
- `GET /api/coding-problem/:id/test-results` - Get test results
- `GET /api/coding-problem/:id/hints` - Get hints
- `GET /api/coding-problem/:id/solution` - Get solution (if available)

## Doubt Resolution System

### 1. Doubt Creation

```mermaid
graph TD
    A[User encounters issue] --> B[Create doubt]
    B --> C[Attach content reference]
    C --> D[Add code snippet/screenshots]
    D --> E[Submit doubt]
    E --> F[Notify mentors]
```

### 2. Doubt Resolution Flow

- Students can create doubts linked to specific content
- Doubts can include code snippets and attachments
- Mentors/instructors respond to doubts
- Multiple responses can form a conversation
- Doubts can be marked as resolved

### Routes to Implement:

- `POST /api/doubts` - Create a new doubt
- `GET /api/doubts` - Get user's doubts
- `GET /api/doubts/:id` - Get doubt details
- `POST /api/doubts/:id/responses` - Add response to doubt
- `PUT /api/doubts/:id/resolve` - Mark doubt as resolved
- `GET /api/mentor/doubts` - Get doubts for mentor

## Bookmarking System

### 1. Bookmark Management

- Users can bookmark any content item
- Bookmarks are organized by submodule
- Bookmarks can be toggled on/off
- Bookmarks are displayed in a dedicated section

### Routes to Implement:

- `POST /api/bookmarks/toggle` - Toggle bookmark status
- `GET /api/bookmarks` - Get user's bookmarks
- `GET /api/bookmarks/check/:contentType/:contentId` - Check if item is bookmarked

## Code Pair Sessions

### 1. Session Flow

```mermaid
graph TD
    A[Student requests session] --> B[Select topic/problem]
    B --> C[Choose mentor]
    C --> D[Schedule session]
    D --> E[Mentor accepts]
    E --> F[Session conducted]
    F --> G[Code saved]
    G --> H[Feedback provided]
```

### 2. Session Features

- Real-time code collaboration
- Video/audio communication
- Session recording
- Code saving and sharing
- Feedback and rating system

### Routes to Implement:

- `POST /api/code-pair-sessions` - Request a new session
- `GET /api/code-pair-sessions` - Get user's sessions
- `PUT /api/code-pair-sessions/:id/accept` - Accept session (mentor)
- `PUT /api/code-pair-sessions/:id/complete` - Complete session
- `POST /api/code-pair-sessions/:id/feedback` - Provide feedback

## Certification Process

### 1. Certificate Eligibility

- Complete at least 90% of course content
- Pass required assessments
- Meet minimum points requirement

### 2. Certificate Types

- **Progress Certificate**: For completing 90%+ of the course
- **Excellence Certificate**: For outstanding performance (top percentile)

### 3. Certificate Generation

- Automatically generated when requirements are met
- Unique certificate ID for verification
- Downloadable PDF format
- Shareable link for social media

### Routes to Implement:

- `GET /api/certificates/eligibility/:courseId` - Check certificate eligibility
- `POST /api/certificates/generate/:courseId` - Generate certificate
- `GET /api/certificates` - Get user's certificates
- `GET /api/certificates/:id` - Get certificate details
- `GET /api/certificates/:id/download` - Download certificate

## API Routes Overview

### User Management

- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile

### Course Management

- `GET /api/courses` - Get all courses
- `GET /api/courses/:id` - Get course details
- `GET /api/courses/:id/modules` - Get course modules
- `GET /api/modules/:id/submodules` - Get module submodules
- `GET /api/submodules/:id/content` - Get submodule content

### Content Access

- `GET /api/content/:type/:id` - Get content details
- `POST /api/content/:type/:id/progress` - Update content progress
- `POST /api/content/:type/:id/complete` - Mark content as complete

### Progress Tracking

- `GET /api/progress/:courseId` - Get course progress
- `GET /api/progress/:courseId/:moduleId` - Get module progress
- `GET /api/leaderboard/:batchId` - Get batch leaderboard

### Batch Management

- `GET /api/batches/:id` - Get batch details
- `POST /api/batches/:id/pause` - Pause batch
- `POST /api/batches/:id/resume` - Resume batch
- `GET /api/batches/:id/deadlines` - Get batch deadlines

### Assessment System

- `GET /api/mcq/:id` - Get MCQ details
- `POST /api/mcq/:id/submit` - Submit MCQ answer
- `GET /api/coding-problem/:id` - Get coding problem
- `POST /api/coding-problem/:id/submit` - Submit solution

### Doubt System

- `POST /api/doubts` - Create doubt
- `GET /api/doubts` - Get user's doubts
- `POST /api/doubts/:id/responses` - Add response

### Bookmarks

- `POST /api/bookmarks/toggle` - Toggle bookmark
- `GET /api/bookmarks` - Get user's bookmarks

### Code Pair Sessions

- `POST /api/code-pair-sessions` - Request session
- `GET /api/code-pair-sessions` - Get sessions

### Certificates

- `GET /api/certificates/eligibility/:courseId` - Check eligibility
- `POST /api/certificates/generate/:courseId` - Generate certificate

## Authentication & Authorization

Proper authentication and authorization are critical for securing the classroom platform and ensuring users only have access to the resources they're permitted to use.

### 1. Authentication System

- **JWT-based Authentication**: Use JSON Web Tokens for stateless authentication
- **Token Management**:
  - Access tokens (short-lived, 15-30 minutes)
  - Refresh tokens (longer-lived, stored securely)
  - Token rotation for security
- **Multi-factor Authentication**: Optional for sensitive operations
- **Session Management**: Track active sessions with the ability to revoke

### 2. User Roles and Permissions

- **Student**: Can access enrolled courses, track progress, submit assignments
- **Instructor**: Can create and manage course content, review student progress
- **Mentor**: Can assist students, respond to doubts, conduct code pair sessions
- **Admin**: Has full access to the platform, can manage users and courses

### 3. Role-Based Access Control

```javascript
// Example middleware for role-based access control (simplified)
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ success: false, message: 'Unauthorized' });
    }
    next();
  };
};
```

### 4. Resource-Level Permissions

- **Course Access**: Students can only access courses they're enrolled in
- **Content Management**: Instructors can only modify their own content
- **Doubt Resolution**: Mentors can only see doubts assigned to them
- **Batch Management**: Admins control batch creation and assignment

### 5. Security Best Practices

- **Password Policies**: Enforce strong passwords with regular rotation
- **Rate Limiting**: Prevent brute force attacks on authentication endpoints
- **CORS Configuration**: Restrict cross-origin requests to trusted domains
- **Input Validation**: Sanitize all user inputs to prevent injection attacks
- **Audit Logging**: Log all authentication and authorization events

## Error Handling & Logging

A robust error handling and logging system is essential for maintaining the reliability and debuggability of the platform.

### 1. Error Handling Strategy

- **Centralized Error Handler**: Implement a global error handling middleware
- **Structured Error Responses**: Consistent error format across all APIs
- **Error Types**: Categorize errors (validation, authentication, server, etc.)
- **User-Friendly Messages**: Provide clear error messages for the frontend

```javascript
// Example global error handler (simplified)
app.use((err, req, res, next) => {
  // Log the error
  logger.error({ message: err.message, path: req.path });

  // Send response
  res.status(err.statusCode || 500).json({
    success: false,
    message: err.message || 'Internal server error'
  });
});
```

### 2. Logging System

- **Log Levels**: Use appropriate log levels (debug, info, warn, error)
- **Structured Logging**: JSON-formatted logs for easier parsing
- **Context Enrichment**: Include request ID, user ID, and other context
- **Log Rotation**: Implement log rotation to manage file sizes
- **Log Aggregation**: Use a centralized logging service (e.g., ELK stack)

### 3. Monitoring and Alerting

- **Health Checks**: Implement API endpoints for system health monitoring
- **Performance Metrics**: Track response times, error rates, and resource usage
- **Alerting Rules**: Set up alerts for critical errors and performance issues
- **Dashboard**: Create a monitoring dashboard for system status

### 4. Debugging Tools

- **Request Tracing**: Implement distributed tracing for request flows
- **Error Stack Analysis**: Tools to analyze and group similar errors
- **Playback Capability**: Ability to replay problematic requests in development

## Testing Strategy

A comprehensive testing strategy ensures the reliability and quality of the platform.

### 1. Test Types

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test interactions between components
- **API Tests**: Test API endpoints and responses
- **End-to-End Tests**: Test complete user flows
- **Performance Tests**: Test system performance under load

### 2. Test Implementation

- **Test Framework**: Use Jest for JavaScript/TypeScript testing
- **API Testing**: Use Supertest for API endpoint testing
- **Mocking**: Use mock objects for external dependencies
- **Test Database**: Use a separate test database with seeded data
- **CI Integration**: Run tests automatically on code changes

### 3. Test Coverage

- **Code Coverage**: Aim for at least 80% code coverage
- **Critical Path Testing**: Ensure all critical user flows are tested
- **Edge Case Testing**: Test boundary conditions and error scenarios
- **Regression Testing**: Prevent reintroduction of fixed bugs

### 4. Test Data Management

- **Test Data Generation**: Tools to generate realistic test data
- **Data Seeding**: Scripts to populate test database with consistent data
- **Test Isolation**: Ensure tests don't interfere with each other

## Deployment Process

A well-defined deployment process ensures reliable and consistent releases.

### 1. Environment Setup

- **Development**: Local development environment
- **Testing**: Isolated environment for testing
- **Staging**: Production-like environment for final validation
- **Production**: Live environment for end users

### 2. Continuous Integration/Continuous Deployment (CI/CD)

- **Build Pipeline**: Automated build process triggered by code changes
- **Test Automation**: Run tests automatically as part of the pipeline
- **Deployment Automation**: Automate deployment to different environments
- **Rollback Capability**: Ability to quickly revert to previous versions

### 3. Deployment Strategy

- **Blue-Green Deployment**: Maintain two identical production environments
- **Feature Flags**: Control feature availability without code changes
- **Canary Releases**: Gradually roll out changes to a subset of users
- **Scheduled Maintenance**: Plan for regular maintenance windows

### 4. Monitoring Post-Deployment

- **Deployment Verification**: Verify successful deployment
- **Health Checks**: Monitor system health after deployment
- **User Impact Analysis**: Track user-facing metrics after changes
- **Feedback Loop**: Collect and analyze user feedback on new features

## Edge Cases and Considerations

### 1. User Experience

- **Offline Access**: Consider implementing offline access for content
- **Mobile Responsiveness**: Ensure all features work on mobile devices
- **Slow Connections**: Optimize for users with slow internet connections

### 2. Content Management

- **Version Control**: Plan for content updates and version management
- **Content Migration**: Strategy for moving content between environments
- **Content Archiving**: Process for archiving outdated content

### 3. Batch Management

- **Batch Transfers**: Allow users to transfer between batches
- **Batch Extensions**: Process for extending batch end dates
- **Batch Pause Limits**: Enforce the 60-day total pause limit
- **Deadline Conflicts**: Handle cases where deadlines overlap after pausing

### 4. Progress Tracking

- **Data Consistency**: Ensure progress data is consistent across levels
- **Progress Recovery**: Handle cases where progress data is lost
- **Late Submissions**: Define policies for submissions after deadlines
- **Point Calculations**: Ensure accurate point calculations with penalties

### 5. Assessment System

- **Plagiarism Detection**: Implement measures to detect copied code
- **Test Case Failures**: Provide helpful feedback for failed test cases
- **Language Support**: Ensure consistent support across programming languages
- **Timeout Handling**: Handle long-running code submissions

### 6. Security Considerations

- **Content Protection**: Prevent unauthorized access to premium content
- **Assessment Integrity**: Prevent cheating in assessments
- **API Rate Limiting**: Implement rate limiting to prevent abuse
- **Data Privacy**: Ensure compliance with data protection regulations

### 7. Performance Considerations

- **Caching Strategy**: Implement caching for frequently accessed data
- **Database Indexing**: Ensure proper indexing for query performance
- **Content Delivery**: Use CDN for media content delivery
- **Background Processing**: Use queues for long-running tasks

### 8. Error Handling

- **Graceful Degradation**: Ensure system functions with partial failures
- **Error Logging**: Comprehensive error logging for debugging
- **User Feedback**: Clear error messages for users
- **Recovery Procedures**: Documented recovery procedures for system failures

## Performance Optimization

Optimizing performance is crucial for providing a smooth user experience and handling a large number of concurrent users.

### 1. Frontend Optimization

- **Code Splitting**: Split JavaScript bundles for faster initial load
- **Lazy Loading**: Load components and resources only when needed
- **Image Optimization**: Use responsive images and modern formats (WebP)
- **Caching Strategy**: Implement effective browser caching
- **Minification**: Minify CSS, JavaScript, and HTML
- **Tree Shaking**: Remove unused code from bundles

### 2. Backend Optimization

- **Database Query Optimization**:
  - Use proper indexing for frequently queried fields
  - Optimize complex queries with aggregation pipelines
  - Implement pagination for large result sets
  - Use projection to limit returned fields

```javascript
// Example of optimized MongoDB query (simplified)
const users = await User.find({ role: 'student' })
  .select('name email')
  .sort({ lastLogin: -1 })
  .limit(10)
  .lean();
```

- **Caching Strategy**:
  - Use Redis for caching frequently accessed data
  - Implement multi-level caching (memory, Redis, CDN)
  - Cache invalidation strategies for data consistency
  - Cache response data for read-heavy endpoints

```javascript
// Example of Redis caching middleware (simplified)
const cacheMiddleware = duration => {
  return async (req, res, next) => {
    const key = `cache:${req.originalUrl}`;
    const cached = await redisClient.get(key);

    if (cached) return res.json(JSON.parse(cached));

    // Store response
    res.sendResponse = res.json;
    res.json = body => {
      redisClient.set(key, JSON.stringify(body), 'EX', duration);
      res.sendResponse(body);
    };

    next();
  };
};
```

### 3. Database Optimization

- **Indexing Strategy**:

  - Create indexes for all fields used in queries
  - Use compound indexes for multi-field queries
  - Avoid over-indexing (increases write time)
  - Regularly analyze and optimize indexes

- **Data Modeling**:

  - Design schemas for query patterns, not just data structure
  - Use denormalization for read-heavy operations
  - Implement data partitioning for large collections
  - Consider time-series data optimization for logs and metrics

- **Connection Pooling**:
  - Configure optimal connection pool size
  - Monitor connection usage and adjust as needed
  - Implement connection timeout and retry strategies

### 4. Network Optimization

- **Content Delivery Network (CDN)**:

  - Use CDN for static assets (images, videos, CSS, JS)
  - Configure proper cache headers
  - Implement origin shield to reduce origin server load

- **API Gateway**:

  - Implement request throttling and rate limiting
  - Use compression for response data
  - Enable HTTP/2 for multiplexed connections
  - Consider GraphQL for reducing over-fetching

- **WebSockets Optimization**:
  - Implement connection pooling for WebSockets
  - Use binary protocols for data efficiency
  - Implement heartbeat mechanism to keep connections alive

### 5. Monitoring and Profiling

- **Performance Monitoring**:

  - Track key performance metrics (response time, throughput)
  - Set up alerts for performance degradation
  - Implement distributed tracing for request flows
  - Use APM tools for real-time monitoring

- **Load Testing**:

  - Regularly perform load tests to identify bottlenecks
  - Simulate realistic user behavior
  - Test different scaling strategies
  - Analyze results and implement improvements

- **Profiling**:
  - Use CPU and memory profiling to identify hotspots
  - Analyze database query performance
  - Profile API endpoint response times
  - Implement performance budgets for critical paths

By addressing these considerations and implementing the outlined flows, your development team will have a clear roadmap for building a robust and user-friendly classroom platform.
