import { NextFunction, Request, Response } from 'express';
import { IUser } from '../interfaces/user.interface';
import { updateLastViewedContent } from '../services/public/resume-learning.service';

interface ContentViewRequest extends Request {
  params: {
    id: string;
  };
  body: {
    subModuleId: string;
  };
}

export const trackContentView = async (
  req: ContentViewRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id: contentId } = req.params as {
      id: string;
    };
    const { subModuleId } = req.body as {
      subModuleId: string;
    };
    const user = req.user as IUser;

    if (!user) {
      return next();
    }

    // Update last viewed content in background (don't await)
    await updateLastViewedContent(user._id, contentId, subModuleId);

    next();
  } catch (error) {
    // Just log the error but don't block the request
    console.error('Error in track content view middleware:', error);
    next();
  }
};
