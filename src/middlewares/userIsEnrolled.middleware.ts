import { NextFunction, Request, Response } from 'express';
import { IUser } from '../interfaces/user.interface';
import {
  getEnrolledCourseLinkByIdService,
  getEnrolledCourseLinkByIdServiceIsPaid
} from '../services/private/user.service';
import submoduleDao from '../dao/submodule.dao';
import ErrorHandler from '../utils/error-handler';
import moduleDao from '../dao/module.dao';
import { IEnrolledCourseLink } from '../interfaces/enrolled-course-link.interface';
interface RequestWithEnrollment extends Request {
  enrollmentDetails?: IEnrolledCourseLink;
}

export const isUserEnrolled = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const { courseId, batchId } = req.params;
  const currentUser = req.user as IUser; // Assuming user is set in the request by authentication middleware
  const enrollmentDetails = await getEnrolledCourseLinkByIdServiceIsPaid(
    currentUser._id,
    courseId,
    batchId
  );

  if (!enrollmentDetails) {
    return res.status(403).json({
      success: false,
      message: 'User is not enrolled in this course'
    });
  }
  // If the user is enrolled, you can proceed with the request

  next();
};

export const isUserEnrolledForSubmodule = async (
  req: RequestWithEnrollment,
  res: Response,
  next: NextFunction
) => {
  try {
    const { submoduleId } = req.params;
    const currentUser = req.user as IUser;

    const submoduleDetails = await submoduleDao.findById(submoduleId);
    if (!submoduleDetails) {
      return next(new ErrorHandler(404, 'Submodule not found'));
    }

    const moduleDetails = await moduleDao.findById(
      submoduleDetails.module as string
    );
    if (!moduleDetails) {
      return next(new ErrorHandler(404, 'Module not found'));
    }

    const enrollmentDetails = (await getEnrolledCourseLinkByIdService(
      currentUser._id,
      moduleDetails.course as string
    )) as IEnrolledCourseLink;

    if (!enrollmentDetails) {
      return res.status(403).json({
        success: false,
        message: 'User is not enrolled in this course'
      });
    }

    // ✅ Attach to req
    req.enrollmentDetails = enrollmentDetails;

    next();
  } catch (error) {
    next(error);
  }
};
