import { Request, Response, NextFunction } from 'express';
import { redis } from '../utils/redis';
import { gzip, gunzip } from 'zlib';
import { promisify } from 'util';
import logger from '../config/logger';

// Promisify zlib functions for async usage
const gzipAsync = promisify(gzip);
const gunzipAsync = promisify(gunzip);

/**
 * Generic cache middleware with zlib compression
 * @param entityType Type of entity being cached (e.g. 'course', 'batch')
 * @param keyPrefix Prefix for the cache key
 * @param ttlSeconds Time to live in seconds
 * @param compressionLevel Optional zlib compression level (1-9, default 6)
 */
function createCacheMiddleware(
  entityType: string,
  keyPrefix: string,
  ttlSeconds: number,
  compressionLevel: number = 6
) {
  return async function (req: Request, res: Response, next: NextFunction) {
    try {
      // Only cache GET requests
      if (req.method !== 'GET') {
        return next();
      }

      // Create a cache key based on the request URL and query params
      const cacheKey = `${entityType}:${keyPrefix}:${req.originalUrl}`;

      // Try to get cached data
      const cachedCompressed = await redis.get(cacheKey);

      if (cachedCompressed) {
        try {
          // Decompress and return cached data
          const decompressedBuffer = await gunzipAsync(
            Buffer.from(cachedCompressed, 'base64')
          );
          const jsonData = JSON.parse(
            decompressedBuffer.toString('utf8')
          ) as Record<string, unknown>;
          return res.status(200).json(jsonData);
        } catch (decompressError) {
          logger.error(`Cache decompression error for ${entityType}:`, {
            error: decompressError
          });
          // Fall through to re-fetch the data if decompression fails
        }
      }

      // Cache miss, capture the response
      const originalSend = res.send;

      res.send = function (body): Response {
        // Store the original response to return
        const result = originalSend.call(this, body);

        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // Handle compression asynchronously without affecting the return type
          void (async () => {
            try {
              // Skip caching for large responses (optional threshold)
              // if (body.length > 10 * 1024 * 1024) return;

              // Compress the response body
              const compressedBuffer = await gzipAsync(Buffer.from(body), {
                level: compressionLevel
              });

              // Store compressed data as base64 string
              await redis.setex(
                cacheKey,
                ttlSeconds,
                compressedBuffer.toString('base64')
              );
            } catch (compressError) {
              logger.error(`Cache compression error for ${entityType}:`, {
                error: compressError
              });
            }
          })();
        }

        // Return the original result synchronously
        return result;
      };

      next();
    } catch (error) {
      // If redis is down or any other error, just continue
      logger.error(`Cache middleware error for ${entityType}:`, { error });
      next();
    }
  };
}

// Course cache middleware (existing one)
export function courseCacheMiddleware(keyPrefix: string, ttlSeconds: number) {
  return createCacheMiddleware('course', keyPrefix, ttlSeconds);
}

// Batch cache middleware
export function batchCacheMiddleware(keyPrefix: string, ttlSeconds: number) {
  return createCacheMiddleware('batch', keyPrefix, ttlSeconds);
}

// Payment cache middleware
export function paymentCacheMiddleware(keyPrefix: string, ttlSeconds: number) {
  return createCacheMiddleware('payment', keyPrefix, ttlSeconds);
}

// User cache middleware
export function userCacheMiddleware(keyPrefix: string, ttlSeconds: number) {
  return createCacheMiddleware('user', keyPrefix, ttlSeconds);
}

// Registration cache middleware
export function registrationCacheMiddleware(
  keyPrefix: string,
  ttlSeconds: number
) {
  return createCacheMiddleware('registration', keyPrefix, ttlSeconds);
}

/**
 * Utility function to manually clear cache for specific patterns
 * @param pattern Redis key pattern to match for deletion
 */
export async function clearCache(
  entityType: string,
  specific?: string
): Promise<number> {
  try {
    // Build the pattern to match keys
    const pattern = specific
      ? `${entityType}:*:*${specific}*`
      : `${entityType}:*`;

    const keys = await redis.keys(pattern);

    if (keys.length) {
      const deletedCount = await redis.del(keys);

      if (process.env.NODE_ENV === 'development') {
        logger.warn({
          message: `Cleared ${deletedCount} cache entries for ${entityType}${
            specific ? ` related to ${specific}` : ''
          }`,
          details: { entityType, pattern, keysDeleted: deletedCount }
        });
      }

      return deletedCount;
    }

    return 0;
  } catch (error) {
    logger.error(`Error clearing admin ${entityType} cache:`, { error });
    return 0;
  }
}
