import { config } from '../config/config';
import { NextFunction, Request, Response } from 'express';
// import errorLogDao from '../dao/error-log.dao';
import ErrorHandler from '../utils/error-handler';
import logger from '../config/logger';

// Define custom error types for better type checking
interface MongooseValidationError extends Error {
  errors: Record<string, { message: string }>;
}

interface MongooseDuplicateKeyError extends Error {
  code: number;
  keyValue: Record<string, unknown>;
}

interface MongooseCastError extends Error {
  path: string;
}

// Type guard functions
function isValidationError(err: unknown): err is MongooseValidationError {
  return (
    (err as Error).name === 'ValidationError' &&
    typeof (err as MongooseValidationError).errors === 'object'
  );
}

function isDuplicateKeyError(err: unknown): err is MongooseDuplicateKeyError {
  return (
    (err as MongooseDuplicateKeyError).code === 11000 &&
    typeof (err as MongooseDuplicateKeyError).keyValue === 'object'
  );
}

function isCastError(err: unknown): err is MongooseCastError {
  return (
    (err as Error).name === 'CastError' &&
    typeof (err as MongooseCastError).path === 'string'
  );
}

const errorMiddleware = (
  err: unknown,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction
) => {
  let customError: ErrorHandler;
  // mongoose bad ObjectId
  if (isCastError(err)) {
    const message = `Resource not found. Invalid: ${err.path}`;
    customError = new ErrorHandler(404, message);
  }
  // mongoose duplicate key
  else if (isDuplicateKeyError(err)) {
    const keys = Object.keys(err.keyValue).join(', ');
    const message = `Duplicate ${keys} entered`;
    customError = new ErrorHandler(400, message);
  }
  // mongoose validation error
  else if (isValidationError(err)) {
    const message = Object.values(err.errors)
      .map(value => value.message)
      .join(', ');
    customError = new ErrorHandler(400, message);
  }
  // JWT token error
  else if (err instanceof Error && err.name === 'JsonWebTokenError') {
    const message = 'Invalid token. Please login again';
    customError = new ErrorHandler(401, message);
  }
  // JWT token expired error
  else if (err instanceof Error && err.name === 'TokenExpiredError') {
    const message = 'Token expired. Please login again';
    customError = new ErrorHandler(401, message);
  }
  // If the error is not one of the above, it's an unexpected error
  else {
    customError =
      config.environment === 'production'
        ? new ErrorHandler(500)
        : (err as ErrorHandler);
  }

  // Log the error for development purposes only
  const statusCode = customError.statusCode;
  if (statusCode >= 500 || config.environment === 'development') {
    logger.error('Error occurred:', err);
  }

  // Save 500 errors to database
  //   if (statusCode >= 500) {
  //     try {
  //       const errorLogData = {
  //         message: customError.message,
  //         stack: err instanceof Error ? err.stack : 'No stack trace available',
  //         statusCode: statusCode,
  //         url: req.originalUrl,
  //         method: req.method,
  //         requestBody: req.body as object,
  //         requestParams: req.params as object,
  //         requestQuery: req.query,
  //         userAgent: req.headers['user-agent'],
  //         ip: req.ip,
  //         user: req.user ? req.user._id : null
  //       };

  //       // Save asynchronously using the DAO
  //       errorLogDao.create(errorLogData).catch((saveErr: unknown) => {
  //         logger.error('Failed to save error log', { error: saveErr });
  //       });
  //     } catch (logError) {
  //       logger.error('Error while logging error to database', {
  //         error: logError
  //       });
  //     }
  //   }/Users/<USER>/Main/sheryians3.0/src/middlewares/rateLimit.middleware.ts

  // In production, don't send detailed error messages
  if (config.environment === 'production') {
    customError.message = `Oops! Something unexpected happened on our end. We're on it! Please try again in a few minutes.`;
  }

  // Send error response
  res.status(statusCode).json({
    success: false,
    message: customError.message
  });
};

export default errorMiddleware;
