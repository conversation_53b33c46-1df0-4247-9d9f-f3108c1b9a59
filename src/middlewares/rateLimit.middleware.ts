import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import ResponseHandler from '../utils/response-handler';

// Default rate limiter
export const defaultLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  limit: 100, // 100 requests per window per IP
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: (req: Request, res: Response) => {
    return ResponseHandler.tooManyRequests(
      'Too many requests, please try again later'
    ).send(res);
  }
});

// More strict limiter for authentication routes
export const authLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  limit: 10, // 10 login/signup attempts per hour
  standardHeaders: true,
  legacyHeaders: false,
  message: (req: Request, res: Response) => {
    return ResponseHandler.tooManyRequests(
      'Too many authentication attempts, please try again later'
    ).send(res);
  }
});

// Limiter for OTP verification
export const otpLimiter = rateLimit({
  windowMs: 30 * 60 * 1000, // 30 minutes
  limit: 5, // 5 OTP attempts per 30 minutes
  standardHeaders: true,
  legacyHeaders: false,
  message: (req: Request, res: Response) => {
    return ResponseHandler.tooManyRequests(
      'Too many OTP verification attempts, please try again later'
    ).send(res);
  }
});

// API endpoints limiter
export const apiLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  limit: 50, // 50 requests per 5 minutes
  standardHeaders: true,
  legacyHeaders: false,
  message: (req: Request, res: Response) => {
    return ResponseHandler.tooManyRequests(
      'Rate limit exceeded, please try again later'
    ).send(res);
  }
});

// Payment endpoints limiter
export const paymentLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  limit: 25, // 25 payment attempts per hour
  standardHeaders: true,
  legacyHeaders: false,
  message: (req: Request, res: Response) => {
    return ResponseHandler.tooManyRequests(
      'Too many payment attempts, please try again later'
    ).send(res);
  }
});
