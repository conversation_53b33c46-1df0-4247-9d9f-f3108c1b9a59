import { Request, Response, NextFunction } from 'express';
import logger from '../config/logger';

/**
 * Type definitions for query parameter handling
 */
interface FilterOptions {
  [key: string]: unknown;
}

interface SortOptions {
  [key: string]: 1 | -1;
}

// These interfaces are used to extend Express Request types in custom.d.ts
// They are exported for reference but used implicitly by the middleware functions
export interface PaginationOptions {
  page: number;
  limit: number;
  skip: number;
}

export interface DbQueryOptions {
  filter: FilterOptions;
  sort: SortOptions;
}

/**
 * Helper function to safely parse a date from a string query parameter
 * @param dateString The date string from query param
 * @returns A Date object or undefined if invalid
 */
const parseDateSafely = (dateString: string | undefined): Date | undefined => {
  if (!dateString) return undefined;

  try {
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) return undefined;
    return date;
  } catch (_error) {
    // Silently handle parsing errors
    logger.error('Error parsing date:', { error: _error });
    return undefined;
  }
};

/**
 * Helper function to apply date range filters
 * @param filter The filter object
 * @param fieldName The field to apply the date range to
 * @param startDate Start date string from query
 * @param endDate End date string from query
 */
const applyDateRangeFilter = (
  filter: FilterOptions,
  fieldName: string,
  startDate?: string,
  endDate?: string
): void => {
  if (startDate) {
    const date = parseDateSafely(startDate);
    if (date) {
      filter[fieldName] = {
        ...((filter[fieldName] as Record<string, unknown>) || {}),
        $gte: date
      };
    }
  }

  if (endDate) {
    const date = parseDateSafely(endDate);
    if (date) {
      filter[fieldName] = {
        ...((filter[fieldName] as Record<string, unknown>) || {}),
        $lte: date
      };
    }
  }
};

/**
 * Helper function to apply numeric range filters
 * @param filter The filter object
 * @param fieldName The field to apply the range to
 * @param min Minimum value string from query
 * @param max Maximum value string from query
 * @param parseFunction Function to parse string to number (parseInt or parseFloat)
 */
const applyNumericRangeFilter = (
  filter: FilterOptions,
  fieldName: string,
  min?: string,
  max?: string,
  parseFunction: (val: string) => number = parseFloat
): void => {
  if (min) {
    filter[fieldName] = {
      ...((filter[fieldName] as Record<string, unknown>) || {}),
      $gte: parseFunction(min)
    };
  }

  if (max) {
    filter[fieldName] = {
      ...((filter[fieldName] as Record<string, unknown>) || {}),
      $lte: parseFunction(max)
    };
  }
};

/**
 * Helper function to apply search filter with regex
 * @param filter The filter object
 * @param searchTerm The search term
 * @param fields Array of fields to search in
 * @param appendToExisting Whether to append to existing $or condition
 */
const applySearchFilter = (
  filter: FilterOptions,
  searchTerm: string | undefined,
  fields: string[] = [],
  appendToExisting = false
): void => {
  if (!searchTerm || fields.length === 0) return;

  const searchRegex = new RegExp(searchTerm, 'i');
  const searchConditions = fields.map(
    field => ({ [field]: searchRegex }) as Record<string, unknown>
  );

  if (appendToExisting && Array.isArray(filter.$or)) {
    // Append to existing $or conditions with type assertion
    filter.$or = [
      ...(filter.$or as Record<string, unknown>[]),
      ...searchConditions
    ];
  } else {
    // Set as new $or conditions
    filter.$or = searchConditions;
  }
};

/**
 * Parse and normalize query parameters for pagination, filtering, and sorting
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 */
export const queryParser = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Pagination
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const skip = (page - 1) * limit;

  // Sorting
  let sort: SortOptions = { createdAt: -1 }; // Default sort
  const sortQuery = req.query.sort as string;
  if (sortQuery) {
    const sortField = sortQuery.replace(/^-/, '');
    const sortOrder = sortQuery.startsWith('-') ? -1 : 1;
    sort = { [sortField]: sortOrder };
  }

  const filter: FilterOptions = {};

  // Course state filter
  if (req.query.state) {
    filter.state = req.query.state;
  }

  // Price range filter
  applyNumericRangeFilter(
    filter,
    'price',
    req.query.minPrice as string | undefined,
    req.query.maxPrice as string | undefined,
    parseInt
  );

  // Course type filter
  if (req.query.type) {
    filter.type = req.query.type;
  }

  // Date range filter for createdAt
  applyDateRangeFilter(
    filter,
    'createdAt',
    req.query.startDate as string | undefined,
    req.query.endDate as string | undefined
  );

  // Search filter
  applySearchFilter(filter, req.query.search as string | undefined, [
    'title',
    'metaData.description',
    'metaData.category'
  ]);

  // Instructor filter
  if (req.query.instructor) {
    filter['instructors.id'] = req.query.instructor;
  }

  // Add query params to request
  req.pagination = { page, limit, skip };
  req.dbQuery = { filter, sort };

  next();
};

/**
 * Query parser middleware for batch-related routes
 * Handles batch-specific filtering, pagination, and sorting
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 */
export const batchQueryParser = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // First apply the common query parser
  queryParser(req, res, () => {
    // Then add batch-specific filters
    const filter = (req.dbQuery?.filter || {}) as FilterOptions;

    // Course filter
    if (req.query.course) {
      filter.course = req.query.course;
    }

    // Batch status filter
    if (req.query.status) {
      filter.status = req.query.status;
    }

    // Is active filter
    if (req.query.isActive) {
      filter.isActive = req.query.isActive === 'true';
    }

    // Enrollment dates filters
    applyDateRangeFilter(
      filter,
      'enrollmentEndDate',
      req.query.enrollmentEndAfter as string | undefined,
      req.query.enrollmentEndBefore as string | undefined
    );

    // Batch date filters (separate from createdAt)
    applyDateRangeFilter(
      filter,
      'startDate',
      req.query.startDateAfter as string | undefined,
      req.query.startDateBefore as string | undefined
    );

    // Payment options filter
    if (req.query.paymentOption) {
      filter.paymentOptions = { $in: [req.query.paymentOption] };
    }

    // Mentor filter
    if (req.query.mentor) {
      filter['mentors.id'] = req.query.mentor;
    }

    // Batch name search - append to existing search fields
    if (req.query.search && Array.isArray(filter.$or)) {
      applySearchFilter(
        filter,
        req.query.search as string | undefined,
        ['name'],
        true
      );
    }

    // Update the filter in the request
    if (req.dbQuery) {
      req.dbQuery.filter = filter;
    }

    next();
  });
};

/**
 * Query parser middleware for batch-registration related routes
 * Handles batch-registration-specific filtering, pagination, and sorting
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 */
export const batchRegistrationQueryParser = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // First apply the common query parser
  queryParser(req, res, () => {
    // Then add batch-registration-specific filters
    const filter = (req.dbQuery?.filter || {}) as FilterOptions;

    // Course filter
    if (req.query.course) {
      filter.course = req.query.course;
    }

    // Batch filter
    if (req.query.batch) {
      filter.batch = req.query.batch;
    }

    // User filter
    if (req.query.user) {
      filter['user_details.user'] = req.query.user;
    }

    // Enrollment date filters
    applyDateRangeFilter(
      filter,
      'enrollment_date',
      req.query.enrollmentDateAfter as string | undefined,
      req.query.enrollmentDateBefore as string | undefined
    );

    // Payment status filter
    if (req.query.paymentStatus) {
      filter['payment_details.status'] = req.query.paymentStatus;
    }

    // Update the filter in the request
    if (req.dbQuery) {
      req.dbQuery.filter = filter;
    }

    next();
  });
};

/**
 * Query parser middleware for payment-related routes
 * Handles payment-specific filtering, pagination, and sorting
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 */
export const paymentQueryParser = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // First apply the common query parser for pagination and basic filtering
  queryParser(req, res, () => {
    // Then add payment-specific filters
    const filter = (req.dbQuery?.filter || {}) as FilterOptions;

    // Payment status filter
    if (req.query.status) {
      filter.status = req.query.status;
    }

    // Payment method filter
    if (req.query.method) {
      filter.method = req.query.method;
    }

    // Payment processor filter
    if (req.query.paymentProcessor) {
      filter.paymentProcessor = req.query.paymentProcessor;
    }

    // Amount range filters
    applyNumericRangeFilter(
      filter,
      'amount',
      req.query.minAmount as string | undefined,
      req.query.maxAmount as string | undefined
    );

    // Date range filters for createdAt
    applyDateRangeFilter(
      filter,
      'createdAt',
      req.query.dateFrom as string | undefined,
      req.query.dateTo as string | undefined
    );

    // Payment date filters
    applyDateRangeFilter(
      filter,
      'paymentDate',
      req.query.paymentDateFrom as string | undefined,
      req.query.paymentDateTo as string | undefined
    );

    // Search by payment ID, order ID, or receipt number
    applySearchFilter(filter, req.query.search as string | undefined, [
      'paymentId',
      'orderId',
      'metadata.receiptNumber',
      'metadata.name',
      'metadata.email',
      'metadata.courseName'
    ]);

    // Filter by test payments
    if (req.query.isTestPayment !== undefined) {
      filter.isTestPayment = req.query.isTestPayment === 'true';
    }

    // Update the filter in the request
    if (req.dbQuery) {
      req.dbQuery.filter = filter;
    }

    next();
  });
};

/**
 * Query parser middleware for user-related routes
 * Handles user-specific filtering, pagination, and sorting
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 */
export const userQueryParser = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // First apply the common query parser for pagination and basic filtering
  queryParser(req, res, () => {
    // Then add user-specific filters
    const filter = (req.dbQuery?.filter || {}) as FilterOptions;

    // Role filter
    if (req.query.role) {
      filter.role = req.query.role;
    }

    // Status filters - convert string "true"/"false" to boolean
    if (req.query.isActive !== undefined) {
      filter.isActive = req.query.isActive === 'true';
    }

    if (req.query.isVerified !== undefined) {
      filter.isVerified = req.query.isVerified === 'true';
    }

    if (req.query.isBanned !== undefined) {
      filter.isBanned = req.query.isBanned === 'true';
    }

    // Search by name, email, or phone
    applySearchFilter(filter, req.query.search as string | undefined, [
      'name.firstName',
      'name.lastName',
      'email',
      'phone.number',
      'phone.code'
    ]);

    // Registration date range filters
    applyDateRangeFilter(
      filter,
      'createdAt',
      req.query.registeredFrom as string | undefined,
      req.query.registeredTo as string | undefined
    );

    // Update the filter in the request
    if (req.dbQuery) {
      req.dbQuery.filter = filter;
    }

    next();
  });
};

/**
 * Query parser middleware for request-callback related routes
 * Handles request-callback specific filtering, pagination, and sorting
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 */
export const requestCallbackQueryParser = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // First apply the common query parser for pagination and basic filtering
  queryParser(req, res, () => {
    // Then add request-callback specific filters
    const filter = (req.dbQuery?.filter || {}) as FilterOptions;

    // Status filter
    if (req.query.status) {
      filter.status = req.query.status;
    }

    // Enquiry type filter
    if (req.query.enquiryFor) {
      filter.enquiryFor = req.query.enquiryFor;
    }

    // Course filter
    if (req.query.courseId) {
      filter['course.id'] = req.query.courseId;
    }

    // Course name filter
    if (req.query.courseName) {
      filter['course.name'] = new RegExp(req.query.courseName as string, 'i');
    }

    // Date range filters
    applyDateRangeFilter(
      filter,
      'date',
      req.query.dateFrom as string | undefined,
      req.query.dateTo as string | undefined
    );

    // Search by name or contact information
    applySearchFilter(filter, req.query.search as string | undefined, [
      'name',
      'contact.number',
      'contact.code'
    ]);

    // Contact exact match filter
    const contactCode = req.query['contact.code'];
    const contactNumber = req.query['contact.number'];

    if (contactCode && contactNumber) {
      filter.contact = {
        code: contactCode,
        number: contactNumber
      };
    }

    // Name filter (partial match)
    if (req.query.name) {
      filter.name = new RegExp(req.query.name as string, 'i');
    }

    // Notes contain text search
    if (req.query.notes) {
      filter.notes = new RegExp(req.query.notes as string, 'i');
    }

    // Update the filter in the request
    if (req.dbQuery) {
      req.dbQuery.filter = filter;
    }

    next();
  });
};

export const activityLogQueryParser = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  queryParser(req, res, () => {
    const filter = (req.dbQuery?.filter || {}) as FilterOptions;
    if (req.query.userId) {
      filter.userId = req.query.userId;
    }
    if (req.query.action) {
      filter.action = req.query.action;
    }
    if (req.query.entityId) {
      filter.entityId = req.query.entityId;
    }
    if (req.query.entityType) {
      filter.entityType = req.query.entityType;
    }
    if (req.dbQuery) {
      req.dbQuery.filter = filter;
    }
  });
  next();
};

export const receiptQueryParser = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  queryParser(req, res, () => {
    const filter = (req.dbQuery?.filter || {}) as FilterOptions;
    if (req.query.state) {
      filter.state = req.query.state;
    }
    if (req.query.type) {
      filter.type = req.query.type;
    }
    if (req.query.isActive) {
      filter.isActive = req.query.isActive === 'true';
    }
    if (req.query.isDeleted) {
      filter.isDeleted = req.query.isDeleted === 'true';
    }
    if (req.dbQuery) {
      req.dbQuery.filter = filter;
    }
    if (req.query.receiptId) {
      filter.receiptId = req.query.receiptId;
    }
    if (req.query.batchId) {
      filter.batchId = req.query.batchId;
    }
    if (req.query.courseId) {
      filter.courseId = req.query.courseId;
    }
    if (req.query.userId) {
      filter.userId = req.query.userId;
    }
    if (req.query.email) {
      filter.email = req.query.email;
    }
    const contactCode = req.query['contact.code'];
    const contactNumber = req.query['contact.number'];

    if (contactCode && contactNumber) {
      filter.contact = {
        code: contactCode,
        number: contactNumber
      };
    }
    if (req.query.paymentId) {
      filter.paymentId = req.query.paymentId;
    }
  });
  next();
};

export const errorLogQueryParser = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  queryParser(req, res, () => {
    const filter = (req.dbQuery?.filter || {}) as FilterOptions;
    if (req.query.errorType) {
      filter.errorType = req.query.errorType;
    }
    if (req.query.page) {
      filter.page = req.query.page;
    }
    if (req.query.limit) {
      filter.limit = req.query.limit;
    }

    if (req.query.errorMessage) {
      filter.errorMessage = req.query.errorMessage;
    }
    if (req.query.stackTrace) {
      filter.stackTrace = req.query.stackTrace;
    }
    if (req.query.userId) {
      filter.user = req.query.userId;
    }
    if (req.query.batchId) {
      filter.batchId = req.query.batchId;
    }
    if (req.query.statusCode) {
      filter.statusCode = req.query.statusCode;
    }
    if (req.query.message) {
      filter.message = req.query.message;
    }
  });
  next();
};

export const batchSubModuleDeadlineQueryParser = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  queryParser(req, res, () => {
    const filter = (req.dbQuery?.filter || {}) as FilterOptions;
    if (req.query.batchId) {
      filter.batch = req.query.batchId;
    }
    if (req.query.submoduleId) {
      filter.submodule = req.query.submoduleId;
    }
    if (req.query.startDate) {
      filter.startDate = req.query.startDate;
    }
    if (req.query.deadline) {
      filter.deadline = req.query.deadline;
    }
  });
  next();
};
