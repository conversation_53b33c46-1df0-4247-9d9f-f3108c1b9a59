import { Request, Response, NextFunction } from 'express';
import xss from 'xss';
import logger from '../config/logger';

/**
 * Options for configuring the XSS sanitizer middleware
 */
export interface XssSanitizerOptions {
  /** Fields to exclude from sanitization (dot notation supported) */
  excludeFields?: string[];
  /** Custom XSS options to pass to the xss library */
  xssOptions?: XSS.IFilterXSSOptions;
  /** Whether to sanitize query parameters */
  sanitizeQuery?: boolean;
  /** Whether to sanitize URL parameters */
  sanitizeParams?: boolean;
  /** Whether to sanitize request headers */
  sanitizeHeaders?: boolean;
  /** Whether to sanitize cookies */
  sanitizeCookies?: boolean;
  /** Headers that should be sanitized (only used if sanitizeHeaders is true) */
  headersToSanitize?: string[];
  /** Additional logging for debugging */
  debug?: boolean;
}

/**
 * Type for values that can be sanitized
 */
type SanitizableValue =
  | string
  | number
  | boolean
  | null
  | undefined
  | Date
  | SanitizableObject
  | SanitizableArray;

interface SanitizableObject {
  [key: string]: SanitizableValue;
}

type SanitizableArray = Array<SanitizableValue>;

/**
 * Creates middleware that sanitizes Express request objects to prevent XSS attacks
 */
export function xssSanitizer(
  options: XssSanitizerOptions = {}
): (req: Request, res: Response, next: NextFunction) => void {
  const {
    excludeFields = [],
    xssOptions = {},
    sanitizeQuery = false,
    sanitizeParams = false,
    sanitizeHeaders = false,
    sanitizeCookies = false,
    headersToSanitize = ['user-agent', 'referer', 'x-forwarded-for'],
    debug = false
  } = options;

  // Precompile exclusion patterns for better performance
  const exactMatches = new Set(excludeFields);
  const prefixMatches = excludeFields
    .filter(field => !field.includes('['))
    .map(field => `${field}.`);
  const arrayPatterns = excludeFields
    .filter(field => field.includes('['))
    .map(
      field =>
        new RegExp(`^${field.replace(/\[/g, '\\[').replace(/\]/g, '\\]')}$`)
    );
  const arrayPrefixPatterns = excludeFields
    .filter(field => field.includes('['))
    .map(
      field =>
        new RegExp(`^${field.replace(/\[/g, '\\[').replace(/\]/g, '\\]')}\\.`)
    );

  // Fast path function to check if a path should be excluded
  const shouldExclude = (path: string): boolean => {
    // Check exact matches first (fastest)
    if (exactMatches.has(path)) {
      return true;
    }

    // Check prefix matches
    for (const prefix of prefixMatches) {
      if (path.startsWith(prefix)) {
        return true;
      }
    }

    // Check regex patterns (slower)
    for (const pattern of arrayPatterns) {
      if (pattern.test(path)) {
        return true;
      }
    }

    for (const pattern of arrayPrefixPatterns) {
      if (pattern.test(path)) {
        return true;
      }
    }

    return false;
  };

  // Type guard functions
  const isObject = (value: unknown): value is Record<string, unknown> =>
    typeof value === 'object' &&
    value !== null &&
    !Array.isArray(value) &&
    !(value instanceof Date);

  const isString = (value: unknown): value is string =>
    typeof value === 'string';

  // Unified sanitization function with exclusion support and WeakMap for circular references
  function sanitize<T>(
    obj: T,
    parentPath = '',
    cache = new WeakMap<object, unknown>()
  ): T {
    // Handle null/undefined
    if (obj === null || obj === undefined) {
      return obj;
    }

    // Handle primitives
    if (typeof obj !== 'object') {
      return isString(obj) && !shouldExclude(parentPath)
        ? (xss(obj, xssOptions) as unknown as T)
        : obj;
    }

    // Handle circular references - we know obj is an object here
    if (cache.has(obj as object)) {
      return cache.get(obj as object) as T;
    }

    // Handle Date objects
    if (obj instanceof Date) {
      return new Date(obj) as unknown as T;
    }

    // Handle arrays
    if (Array.isArray(obj)) {
      const sanitizedArray: unknown[] = [];
      cache.set(obj, sanitizedArray);

      for (let i = 0; i < obj.length; i++) {
        const itemPath = parentPath ? `${parentPath}[${i}]` : `[${i}]`;
        sanitizedArray[i] = sanitize(obj[i], itemPath, cache);
      }

      return sanitizedArray as unknown as T;
    }

    // Handle objects (we already checked it's not null, array, or Date)
    if (isObject(obj)) {
      const sanitizedObj: Record<string, unknown> = {};
      cache.set(obj, sanitizedObj);

      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const currentPath = parentPath ? `${parentPath}.${key}` : key;
          const value = obj[key];

          if (shouldExclude(currentPath)) {
            sanitizedObj[key] = value;
          } else {
            sanitizedObj[key] = sanitize(value, currentPath, cache);
          }
        }
      }

      return sanitizedObj as unknown as T;
    }

    // Fallback for any other types
    return obj;
  }

  // Return the middleware function
  return function xssSanitizerMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
  ): void {
    try {
      // Debug timing logic
      let timeTaken = 0;
      const startTime = debug ? Date.now() : 0;

      // Sanitize body
      if (req.body) {
        // Check if the body is a string
        if (isString(req.body)) {
          // If it's a string, sanitize it directly
          req.body = xss(req.body, xssOptions);
        } else if (isObject(req.body)) {
          // If it's an object, sanitize it recursively
          // Use the parent path as 'body' for better error messages
          const parentPath = 'body';
          req.body = sanitize<typeof req.body>(req.body, parentPath);
        } else if (Array.isArray(req.body)) {
          // If it's an array, sanitize each item
          req.body = req.body.map((item, index) => {
            const itemPath = `body[${index}]`;
            return sanitize(item, itemPath) as unknown as SanitizableValue;
          });
        }
      }

      // Optionally sanitize query parameters
      if (sanitizeQuery && req.query) {
        req.query = sanitize(req.query, 'query');
      }

      // Optionally sanitize URL parameters
      if (sanitizeParams && req.params) {
        req.params = sanitize(req.params, 'params');
      }

      // Optionally sanitize specific headers
      if (sanitizeHeaders && req.headers) {
        const headers = { ...req.headers };
        for (const header of headersToSanitize) {
          if (headers[header]) {
            headers[header] = sanitize(headers[header], `headers.${header}`);
          }
        }
        req.headers = headers;
      }

      // Optionally sanitize cookies
      if (sanitizeCookies && req.cookies) {
        req.cookies = sanitize(req.cookies, 'cookies');
      }

      if (debug) {
        timeTaken = Date.now() - startTime;
        // Use warn instead of debug for linting compatibility
        logger.warn(`XSS sanitization completed in ${timeTaken}ms`);
      }
    } catch (error) {
      logger.error('XSS sanitization error:', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      if (debug && error instanceof Error && error.stack) {
        logger.warn('XSS sanitization stack:', { stack: error.stack });
      }
      // Continue with request processing even if sanitization fails
    }

    next();
  };
}
