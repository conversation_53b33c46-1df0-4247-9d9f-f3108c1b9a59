import { NextFunction, Request, Response } from 'express';

import <PERSON><PERSON>and<PERSON> from '../../utils/response-handler';
import <PERSON>rro<PERSON><PERSON>andler from '../../utils/error-handler';
import asyncHandler from '../../utils/async-handler';

import {
  createSubmoduleService,
  getSubmoduleByIdService,
  // getAllSubmodulesService,
  updateSubmoduleService
} from '../../services/private/submodule.service';
import { ISubModule } from '../../interfaces/submodule.interface';
import Logger from '../../services/logger.service';
import { IUser } from '../../interfaces/user.interface';
import { simulateSubModuleReorder } from '../../utils/submodule.utils';

export const createSubmoduleController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const data = req.body as Partial<ISubModule>;
    const currentUser = req.user as IUser;

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const submodule = await createSubmoduleService(data);

    if (!submodule) {
      return next(new ErrorHandler(400, 'Failed to create submodule'));
    }

    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: 'create',
      entityId: String(submodule._id),
      entityType: 'submodule',
      description: 'Submodule created',
      details: {
        submoduleId: submodule._id as string,
        submoduleName: submodule.title,
        description: submodule.description
      },
      req
    });

    return ResponseHandler.success(
      submodule,
      'Submodule created successfully'
    ).send(res);
  }
);

export const getSubmoduleByIdController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const populate = req.query.populate as string | string[] | undefined;
    const currentUser = req.user as IUser;

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const submodule = await getSubmoduleByIdService(id, populate);

    if (!submodule) {
      return next(new ErrorHandler(404, 'Submodule not found'));
    }

    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: 'view',
      entityId: String(submodule._id),
      entityType: 'submodule',
      description: 'Submodule viewed',
      details: {
        submoduleId: submodule._id as string,
        submoduleName: submodule.title
      },
      req
    });

    return ResponseHandler.success(
      submodule,
      'Submodule fetched successfully'
    ).send(res);
  }
);

export const updateSubmoduleController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const data = req.body as Partial<ISubModule>;
    const currentUser = req.user as IUser;

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const submodule = await updateSubmoduleService(id, data);

    if (!submodule) {
      return next(new ErrorHandler(404, 'Submodule Could not be updated'));
    }

    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: 'update',
      entityId: String(submodule._id),
      entityType: 'submodule',
      description: 'Submodule updated',
      details: {
        submoduleId: submodule._id as string,
        submoduleName: submodule.title,
        updatedFields: Object.keys(data).join(', ')
      },
      req
    });

    return ResponseHandler.success(
      submodule,
      'Submodule updated successfully'
    ).send(res);
  }
);

export const reorderSubmoduleController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const { order } = req.body as { order: number };
    const currentUser = req.user as IUser;

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const result = await simulateSubModuleReorder(id, order);

    if (!result) {
      return next(new ErrorHandler(400, 'Failed to reorder submodule'));
    }

    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: 'reorder',
      entityId: id,
      entityType: 'submodule',
      description: 'Submodule reordered',
      details: {
        submoduleId: id,
        newOrder: order
      },
      req
    });

    return ResponseHandler.success(
      result,
      'Submodule reordered successfully'
    ).send(res);
  }
);
