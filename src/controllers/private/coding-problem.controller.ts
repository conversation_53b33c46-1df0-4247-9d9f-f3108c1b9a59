import { NextFunction, Request, Response } from 'express';

import <PERSON><PERSON>and<PERSON> from '../../utils/response-handler';
import <PERSON>rror<PERSON>andler from '../../utils/error-handler';
import asyncHandler from '../../utils/async-handler';

import {
  createCodingProblemService,
  getCodingProblemByIdService,
  getCodingProblemByProblemIdService,
  getCodingProblemsByLanguageService,
  updateCodingProblemService,
  deleteCodingProblemService
} from '../../services/private/coding-problem.service';
import { ICodingProblem } from '../../interfaces/coding-problem.interface';
import { IUser } from '../../interfaces/user.interface';
import Logger from '../../services/logger.service';
import { ActionTypes } from '../../interfaces/activity-log.interface';

export const createCodingProblemController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const data = req.body as Partial<ICodingProblem>;
    const currentUser = req.user as IUser;

    const codingProblem = await createCodingProblemService(data);

    if (!codingProblem) {
      return next(new ErrorHandler(400, 'Coding problem creation failed'));
    }

    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.CODING_PROBLEM.CREATE,
      entityId: String(codingProblem._id),
      entityType: 'coding-problem',
      description: 'Coding problem created',
      details: {
        problemId: codingProblem.problemId,
        title: codingProblem.title,
        description: codingProblem.description,
        difficulty: codingProblem.difficulty
      },
      req
    });

    return ResponseHandler.success(
      codingProblem,
      'Coding problem created successfully'
    ).send(res);
  }
);

export const getCodingProblemByIdController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const codingProblem = await getCodingProblemByIdService(id);

    if (!codingProblem) {
      return next(new ErrorHandler(404, 'Coding problem not found'));
    }

    return ResponseHandler.success(
      codingProblem,
      'Coding problem fetched successfully'
    ).send(res);
  }
);

export const getCodingProblemByProblemIdController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { problemId } = req.params;
    const codingProblem = await getCodingProblemByProblemIdService(problemId);

    if (!codingProblem) {
      return next(new ErrorHandler(404, 'Coding problem not found'));
    }

    return ResponseHandler.success(
      codingProblem,
      'Coding problem fetched successfully'
    ).send(res);
  }
);

export const getCodingProblemsByLanguageController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { language } = req.params;

    if (!language) {
      return next(new ErrorHandler(400, 'Language is required'));
    }

    const problems = await getCodingProblemsByLanguageService(language);

    return ResponseHandler.success(
      problems,
      'Coding problems fetched successfully'
    ).send(res);
  }
);

export const updateCodingProblemController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const data = req.body as Partial<ICodingProblem>;
    const updatedCodingProblem = await updateCodingProblemService(id, data);
    if (!updatedCodingProblem) {
      return next(new ErrorHandler(400, 'Coding problem update failed'));
    }

    return ResponseHandler.success(
      updatedCodingProblem,
      'Coding problem updated successfully'
    ).send(res);
  }
);

export const deleteCodingProblemController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const currentUser = req.user as IUser;

    if (!currentUser) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const codingProblem = await deleteCodingProblemService(id);

    if (!codingProblem) {
      return next(new ErrorHandler(404, 'Coding problem not found'));
    }

    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.CODING_PROBLEM.DELETE,
      entityId: String(codingProblem._id),
      entityType: 'coding-problem',
      description: 'Coding problem deleted',
      details: {
        problemId: codingProblem.problemId,
        title: codingProblem.title,
        description: codingProblem.description,
        difficulty: codingProblem.difficulty
      },
      req
    });

    return ResponseHandler.success(
      codingProblem,
      'Coding problem deleted successfully'
    ).send(res);
  }
);
