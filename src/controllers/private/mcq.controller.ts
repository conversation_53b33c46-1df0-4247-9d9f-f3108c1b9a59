import { NextFunction, Request, Response } from 'express';

import <PERSON><PERSON>and<PERSON> from '../../utils/response-handler';
import <PERSON>rro<PERSON><PERSON>andler from '../../utils/error-handler';
import asyncHandler from '../../utils/async-handler';

import {
  createMCQService,
  getMCQByIdService,
  updateMCQService,
  deleteMCQService
} from '../../services/private/mcq.service';
import { IMCQ } from '../../interfaces/mcq.interface';
import Logger from '../../services/logger.service';
import { IUser } from '../../interfaces/user.interface';

export const createMCQController = asyncHandler(
  async (req: Request, res: Response) => {
    const data = req.body as Partial<IMCQ>;
    const currentUser = req.user as IUser;
    const createdMcq = await createMCQService(data);
    if (!createdMcq) {
      return new ErrorHandler(400, 'MCQ creation failed');
    }
    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: 'create',
      entityId: String(createdMcq._id),
      entityType: 'mcq',
      description: 'MCQ created',
      details: {
        mcqId: createdMcq._id as string,
        question: createdMcq.question,
        options: createdMcq.options
      }
    });

    return ResponseHandler.success(createdMcq, 'MCQ created successfully').send(
      res
    );
  }
);

export const getMCQByIdController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const mcq = await getMCQByIdService(id);

    if (!mcq) {
      return next(new ErrorHandler(404, 'MCQ not found'));
    }

    return ResponseHandler.success(mcq, 'MCQ fetched successfully').send(res);
  }
);

export const updateMCQController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const data = req.body as Partial<IMCQ>;

    const mcq = await updateMCQService(id, data);

    if (!mcq) {
      return next(new ErrorHandler(404, 'MCQ not found'));
    }

    return ResponseHandler.success(mcq, 'MCQ updated successfully').send(res);
  }
);

export const deleteMCQController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const currentUser = req.user as IUser;

    if (!currentUser) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const mcq = await deleteMCQService(id);

    if (!mcq) {
      return next(new ErrorHandler(404, 'MCQ not found'));
    }

    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: 'delete',
      entityId: String(mcq._id),
      entityType: 'mcq',
      description: 'MCQ deleted',
      details: {
        mcqId: mcq._id as string,
        question: mcq.question,
        options: mcq.options
      },
      req
    });

    return ResponseHandler.success(mcq, 'MCQ deleted successfully').send(res);
  }
);
