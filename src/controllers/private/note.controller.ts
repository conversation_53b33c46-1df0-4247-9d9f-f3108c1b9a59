import { NextFunction, Request, Response } from 'express';

import <PERSON><PERSON>and<PERSON> from '../../utils/response-handler';
import asyncHandler from '../../utils/async-handler';

import {
  createNoteService,
  updateNoteService,
  deleteNoteService
} from '../../services/private/note.service';
import { INote } from '../../interfaces/note.interface';
import { IUser } from '../../interfaces/user.interface';
import ErrorHandler from '../../utils/error-handler';
import { ActionTypes } from '../../interfaces/activity-log.interface';
import Logger from '../../services/logger.service';

export const createNoteController = asyncHandler(
  async (req: Request, res: Response) => {
    const data = req.body as Partial<INote>;
    const currentUser = req.user as IUser;

    const createdNote = await createNoteService(data);
    if (!createdNote) {
      return new ErrorHandler(400, 'Note creation failed');
    }
    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.NOTE.CREATE,
      entityId: String(createdNote._id),
      entityType: 'note',
      description: 'Note created',
      details: {
        noteId: createdNote._id as string,
        title: createdNote.title,
        content: createdNote.content
      }
    });

    return ResponseHandler.success(
      createdNote,
      'Note created successfully'
    ).send(res);
  }
);

export const updateNoteController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const data = req.body as Partial<INote>;

    const updatedNote = await updateNoteService(id, data);
    if (!updatedNote) {
      return next(new ErrorHandler(400, 'Note update failed'));
    }

    return ResponseHandler.success(
      updatedNote,
      'Note updated successfully'
    ).send(res);
  }
);

export const deleteNoteController = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const currentUser = req.user as IUser;

    const deletedNote = await deleteNoteService(id);
    if (!deletedNote) {
      return new ErrorHandler(400, 'Note deletion failed');
    }
    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.NOTE.DELETE,
      entityId: String(deletedNote._id),
      entityType: 'note',
      description: 'Note deleted',
      details: {
        noteId: deletedNote._id as string,
        title: deletedNote.title
      }
    });

    return ResponseHandler.success(
      deletedNote,
      'Note deleted successfully'
    ).send(res);
  }
);
