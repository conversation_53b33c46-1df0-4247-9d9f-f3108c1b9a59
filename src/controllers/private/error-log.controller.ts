import { NextFunction, Request, Response } from 'express';

import <PERSON>Handler from '../../utils/response-handler';
import <PERSON>rror<PERSON>andler from '../../utils/error-handler';
import {
  getErrorLogsService,
  getErrorLogByIdService
} from '../../services/private/error-log.service';
import asyncHandler from '../../utils/async-handler';

export const getErrorLogsController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const {
      pagination = { page: 1, limit: 10, skip: 0 },
      dbQuery = { filter: {}, sort: { createdAt: -1 } }
    } = req;
    const result = await getErrorLogsService(pagination, dbQuery);

    if (!result.errorLogs.length) {
      return next(new ErrorHandler(404, 'No error logs found'));
    }

    return ResponseHandler.success(
      result,
      'Error logs fetched successfully'
    ).send(res);
  }
);

export const getErrorLogByIdController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const errorLog = await getErrorLogByIdService(id);

    if (!errorLog) {
      return next(new ErrorHandler(404, 'Error log not found'));
    }

    return ResponseHandler.success(
      errorLog,
      'Error log fetched successfully'
    ).send(res);
  }
);
