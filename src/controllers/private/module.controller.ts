import { Request, Response, NextFunction } from 'express';
import async<PERSON>and<PERSON> from '../../utils/async-handler';
import ResponseHandler from '../../utils/response-handler';
import <PERSON>rror<PERSON>andler from '../../utils/error-handler';
import { IModule } from '../../interfaces/module.interface';
import {
  createModuleService,
  updateModuleService
} from '../../services/private/module.service';
import Logger from '../../services/logger.service';
import { IUser } from '../../interfaces/user.interface';
import { simulateModuleReorder } from '../../utils/module.utils';

export const createModuleController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    // Extract module data from request body
    const data = req.body as IModule;
    const currentUser = req.user as IUser;

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const createdModule = await createModuleService(data);

    if (!createdModule) {
      return next(
        new ErrorHandler(
          400,
          'Module creation failed. Please reorder the existing module first.'
        )
      );
    }

    if (createdModule) {
      await Logger.log({
        userId: currentUser._id,
        userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
        action: 'create',
        entityId: String(createdModule?._id),
        entityType: 'module',
        description: 'Module created',
        details: {
          moduleId: createdModule?._id as string,
          moduleName: createdModule?.title,
          moduleDescription: createdModule?.description
        },
        req
      });
    }

    const message = 'Module created successfully';

    return ResponseHandler.created(
      {
        module: createdModule
      },
      message
    ).send(res);
  }
);

export const reorderModuleController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const { order } = req.body as { order: number };

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const result = await simulateModuleReorder(id, order);

    if (!result.success) {
      return next(new ErrorHandler(400, result.message));
    }

    return ResponseHandler.success(null, result.message).send(res);
  }
);

export const updateModuleController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const data = req.body as IModule;
    const currentUser = req.user as IUser;

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const updatedModule = await updateModuleService(id, data);

    if (updatedModule) {
      await Logger.log({
        userId: currentUser._id,
        userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
        action: 'update',
        entityId: String(updatedModule?._id),
        entityType: 'module',
        description: 'Module updated',
        details: {
          moduleId: updatedModule?._id as string,
          moduleName: updatedModule?.title,
          moduleDescription: updatedModule?.description
        },
        req
      });
    }

    const message = updatedModule
      ? 'Module updated successfully'
      : 'Module update failed';

    if (!updatedModule) {
      return next(new ErrorHandler(400, message));
    }

    return ResponseHandler.success(
      {
        module: updatedModule
      },
      message
    ).send(res);
  }
);
