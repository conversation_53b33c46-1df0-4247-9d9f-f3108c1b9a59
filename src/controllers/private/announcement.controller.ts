import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON>and<PERSON> from '../../utils/response-handler';
import <PERSON>rror<PERSON>andler from '../../utils/error-handler';
import {
  createAnnouncementService,
  deleteAnnouncementService,
  getAnnouncementService,
  updateAnnouncementService
} from '../../services/private/announcement.service';
import { IAnnouncement } from '../../interfaces/announcement.interface';
import { ActionTypes } from '../../interfaces/activity-log.interface';
import { IUser } from '../../interfaces/user.interface';
import Logger from '../../services/logger.service';
import asyncHandler from '../../utils/async-handler';

export const createAnnouncementController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const currentUser = req.user as IUser;
    const announcementData = {
      ...req.body,
      author: currentUser._id,
      authorName: `${currentUser.name.firstName} ${currentUser.name.lastName}`
    } as Partial<IAnnouncement>;

    const announcement = await createAnnouncementService(announcementData);

    if (!announcement) {
      return next(new ErrorHandler(500, 'Failed to create announcement'));
    }

    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.ANNOUNCEMENT.CREATE,
      entityId: String(announcement._id),
      entityType: 'announcement',
      description: 'Announcement created',
      details: {
        announcementId: announcement._id as string,
        title: announcement.title,
        content: announcement.content
      }
    });

    return ResponseHandler.success(
      announcement,
      'Announcement created successfully'
    ).send(res);
  }
);

export const getAnnouncementController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { batchId } = req.params;
    const announcement = await getAnnouncementService(batchId);

    if (!announcement) {
      return next(new ErrorHandler(404, 'Announcement not found'));
    }

    return ResponseHandler.success(announcement).send(res);
  }
);

export const deleteAnnouncementController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const currentUser = req.user as IUser;
    // Assuming a deleteAnnouncementService exists
    const announcement = await deleteAnnouncementService(id);
    if (!announcement) {
      return next(new ErrorHandler(404, 'Announcement not found'));
    }

    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.ANNOUNCEMENT.DELETE,
      entityId: String(announcement._id),
      entityType: 'announcement',
      description: 'Announcement deleted',
      details: {
        announcementId: announcement._id as string,
        title: announcement.title
      }
    });

    return ResponseHandler.success(
      null,
      'Announcement deleted successfully'
    ).send(res);
  }
);

export const updateAnnouncementController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const currentUser = req.user as IUser;
    const announcementData = {
      ...req.body,
      author: currentUser._id,
      authorName: `${currentUser.name.firstName} ${currentUser.name.lastName}`
    } as Partial<IAnnouncement>;

    const announcement = await updateAnnouncementService(id, announcementData);

    if (!announcement) {
      return next(new ErrorHandler(404, 'Announcement not found'));
    }

    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.ANNOUNCEMENT.UPDATE,
      entityId: String(announcement._id),
      entityType: 'announcement',
      description: 'Announcement updated',
      details: {
        announcementId: announcement._id as string,
        title: announcement.title,
        content: announcement.content
      }
    });

    return ResponseHandler.success(
      announcement,
      'Announcement updated successfully'
    ).send(res);
  }
);
