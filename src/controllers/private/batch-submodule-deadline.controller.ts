import { NextFunction, Request, Response } from 'express';
import <PERSON>Handler from '../../utils/response-handler';
import <PERSON>rror<PERSON>andler from '../../utils/error-handler';
import asyncHandler from '../../utils/async-handler';
import {
  createBatchSubmoduleDeadlineService,
  getBatchSubmoduleDeadlineService,
  // subModuleDeadlineInitializerService,
  initializeSubmoduleAndCascadeService,
  updateBatchSubmoduleDeadlineService
  // updateBatchSubmoduleDeadlineService
} from '../../services/private/batch-submodule-deadline.service';
import { IBatchSubmoduleDeadline } from '../../interfaces/batch-submodule-deadline.interface';
import Logger from '../../services/logger.service';
import { IUser } from '../../interfaces/user.interface';
import { ActionTypes } from '../../interfaces/activity-log.interface';
import { subModuleDeadlineInitializer } from '../../utils/submodule-deadline.util';
import moment from 'moment';

export const createBatchSubmoduleDeadlineController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const data = req.body as IBatchSubmoduleDeadline;
    const currentUser = req.user as IUser;

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const isValidDate = moment(data.startDate, moment.ISO_8601, true).isValid();
    if (!isValidDate) {
      return next(new ErrorHandler(400, 'Invalid start date'));
    }

    const startDate = await subModuleDeadlineInitializer(
      data.submodule,
      data.startDate,
      data.dependsOnPreviousSubmodule,
      data.batch
    );

    if (!startDate.success) {
      return next(new ErrorHandler(400, startDate.message));
    }

    data.startDate = moment(startDate.data).toDate();
    data.deadline = moment(startDate.data).add(data.days, 'days').toDate();

    const batchSubmoduleDeadline =
      await createBatchSubmoduleDeadlineService(data);

    if (!batchSubmoduleDeadline) {
      return next(new ErrorHandler(404, 'Batch submodule deadline not found'));
    }

    // Add logging for batch submodule deadline creation
    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.BATCH_SUBMODULE_DEADLINE.CREATE,
      entityId: String(batchSubmoduleDeadline._id),
      entityType: 'batch-submodule-deadline',
      description: 'Batch submodule deadline created',
      details: {
        deadlineId: batchSubmoduleDeadline._id as string,
        batchId: batchSubmoduleDeadline.batch,
        submoduleId: batchSubmoduleDeadline.submodule,
        startDate: batchSubmoduleDeadline.startDate
      },
      req
    });

    return ResponseHandler.success(
      batchSubmoduleDeadline,
      'Batch submodule deadline created successfully'
    ).send(res);
  }
);

export const getBatchSubmoduleDeadlineController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const {
      pagination = { page: 1, limit: 10, skip: 0 },
      dbQuery = { filter: {}, sort: { createdAt: -1 } }
    } = req;
    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const batchSubmoduleDeadline = await getBatchSubmoduleDeadlineService(
      pagination,
      dbQuery
    );

    if (!batchSubmoduleDeadline) {
      return next(new ErrorHandler(404, 'Batch submodule deadline not found'));
    }

    return ResponseHandler.success(
      batchSubmoduleDeadline,
      'Batch submodule deadline retrieved successfully'
    ).send(res);
  }
);

export const updateBatchSubmoduleDeadlineController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { batchId } = req.params;
    const data = req.body as IBatchSubmoduleDeadline;
    const currentUser = req.user as IUser;

    if (!currentUser) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const batchSubmoduleDeadline = await updateBatchSubmoduleDeadlineService(
      batchId,
      data
    );

    if (!batchSubmoduleDeadline) {
      return next(new ErrorHandler(404, 'Batch submodule deadline not found'));
    }

    // Add logging for batch submodule deadline creation
    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.BATCH_SUBMODULE_DEADLINE.UPDATE,
      entityId: String(batchSubmoduleDeadline._id),
      entityType: 'batch-submodule-deadline',
      description: 'Batch submodule deadline updated',
      details: {
        deadlineId: batchSubmoduleDeadline._id as string,
        batchId: batchSubmoduleDeadline.batch,
        submoduleId: batchSubmoduleDeadline.submodule,
        startDate: batchSubmoduleDeadline.startDate
      },
      req
    });

    return ResponseHandler.success(
      batchSubmoduleDeadline,
      'Batch submodule deadline created successfully'
    ).send(res);
  }
);

export const initializeAllDeadlineController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const data = req.body as IBatchSubmoduleDeadline;
    const currentUser = req.user as IUser;

    if (!currentUser) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    try {
      // First, initialize the specified submodule and cascade to subsequent submodules
      const initializedDeadlines = await initializeSubmoduleAndCascadeService({
        batchId: data?.batch,
        submoduleId: data?.submodule,
        startDate: new Date(data?.startDate),
        days: data?.days,
        dependsOnPreviousSubmodule: data?.dependsOnPreviousSubmodule || false,
        userId: currentUser._id,
        penaltyRules: data?.penaltyRules || []
      });

      if (!initializedDeadlines || initializedDeadlines.length === 0) {
        return next(new ErrorHandler(404, 'No deadlines initialized'));
      }

      // Add logging for batch submodule deadlines initialization
      await Logger.log({
        userId: currentUser._id,
        userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
        action: ActionTypes.BATCH_SUBMODULE_DEADLINE.CREATE,
        entityId: String(data.batch),
        entityType: 'batch-submodule-deadlines',
        description: 'All batch submodule deadlines initialized',
        details: {
          batchId: data.batch,
          sourceSubmoduleId: data.submodule,
          startDate: data.startDate,
          days: data.days,
          dependsOnPreviousSubmodule: data.dependsOnPreviousSubmodule
        },
        req
      });

      return ResponseHandler.success(
        initializedDeadlines,
        'Batch submodule deadlines initialized successfully'
      ).send(res);
    } catch (error) {
      return next(error);
    }
  }
);
