import { NextFunction, Request, Response } from 'express';

import <PERSON>Handler from '../../utils/response-handler';
import <PERSON>rror<PERSON>andler from '../../utils/error-handler';
import asyncHandler from '../../utils/async-handler';

import {
  createDoubtService,
  getDoubtByIdService,
  resolveDoubtService
} from '../../services/private/doubt.service';
import { IDoubt } from '../../interfaces/doubt.interface';
import mongoose from 'mongoose';

export const createDoubtController = asyncHandler(
  async (req: Request, res: Response) => {
    const data = req.body as Partial<IDoubt>;

    // Add user information from auth middleware
    if (req.user) {
      data.user = new mongoose.Types.ObjectId(req.user._id);
      data.userName = req.user.name as unknown as string;
      data.userAvatar = req.user.avatar;
    }

    const doubt = await createDoubtService(data);

    return ResponseHandler.success(doubt, 'Doubt created successfully').send(
      res
    );
  }
);

export const getDoubtByIdController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const doubt = await getDoubtByIdService(id);

    if (!doubt) {
      return next(new ErrorHandler(404, 'Doubt not found'));
    }

    return ResponseHandler.success(doubt, 'Doubt fetched successfully').send(
      res
    );
  }
);

export const resolveDoubtController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const resolvedBy = req.user?._id;

    if (!resolvedBy) {
      return next(new ErrorHandler(401, 'Unauthorized'));
    }

    const doubt = await resolveDoubtService(id, resolvedBy.toString());

    if (!doubt) {
      return next(new ErrorHandler(404, 'Doubt not found'));
    }

    return ResponseHandler.success(doubt, 'Doubt resolved successfully').send(
      res
    );
  }
);
