import { IVideo } from '../../interfaces/video.interface';
import asyncHandler from '../../utils/async-handler';
import {
  createVideoService,
  updateVideoService,
  deleteVideoService
} from '../../services/private/video.service';
import ResponseHandler from '../../utils/response-handler';
import <PERSON>rror<PERSON>andler from '../../utils/error-handler';
import { Request, Response, NextFunction } from 'express';
import Logger from '../../services/logger.service';
import { IUser } from '../../interfaces/user.interface';
import { ActionTypes } from '../../interfaces/activity-log.interface';

export const createVideoController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const data = req.body as IVideo;
    const currentUser = req.user as IUser;

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const createdVideo = await createVideoService(data);
    if (!createdVideo) {
      return next(new <PERSON>rror<PERSON>andler(400, 'Failed to create video'));
    }

    // Add logging for video creation
    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.VIDEO.CREATE,
      entityId: String(createdVideo._id),
      entityType: 'video',
      description: 'Video created',
      details: {
        videoId: createdVideo._id as string,
        videoTitle: createdVideo.title,
        duration: createdVideo.duration
      },
      req
    });

    const message = createdVideo
      ? 'Video created successfully'
      : 'Video creation failed';
    return ResponseHandler.created(
      {
        video: createdVideo
      },
      message
    ).send(res);
  }
);

export const updateVideoController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const data = req.body as Partial<IVideo>;
    const currentUser = req.user as IUser;

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const updatedVideo = await updateVideoService(id, data);
    if (!updatedVideo) {
      return next(new ErrorHandler(400, 'Failed to update video'));
    }

    // Add logging for video update
    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.VIDEO.UPDATE,
      entityId: String(updatedVideo._id),
      entityType: 'video',
      description: 'Video updated',
      details: {
        videoId: updatedVideo._id as string,
        videoTitle: updatedVideo.title,
        duration: updatedVideo.duration
      },
      req
    });

    return ResponseHandler.success(
      {
        video: updatedVideo
      },
      'Video updated successfully'
    ).send(res);
  }
);

export const deleteVideoController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const currentUser = req.user as IUser;

    if (!req.user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    // Assuming deleteVideoService is implemented
    const deletedVideo = await deleteVideoService(id);
    if (!deletedVideo) {
      return next(new ErrorHandler(400, 'Failed to delete video'));
    }

    // Add logging for video deletion
    await Logger.log({
      userId: currentUser._id,
      userName: `${currentUser.name.firstName} ${currentUser.name.lastName}`,
      action: ActionTypes.VIDEO.DELETE,
      entityId: String(deletedVideo._id),
      entityType: 'video',
      description: 'Video deleted',
      details: {
        videoId: deletedVideo._id as string,
        videoTitle: deletedVideo.title
      },
      req
    });

    return ResponseHandler.success(
      {
        video: deletedVideo
      },
      'Video deleted successfully'
    ).send(res);
  }
);
