import { Request, Response, NextFunction } from 'express';
import asyncHand<PERSON> from '../../utils/async-handler';
import {
  getNoteService,
  markNoteCompletedService
} from '../../services/public/note.service';
import { IUser } from '../../interfaces/user.interface';
import <PERSON><PERSON><PERSON><PERSON>and<PERSON> from '../../utils/error-handler';
import ResponseHandler from '../../utils/response-handler';

export const getNoteController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const user = req.user as IUser;
    const { subModuleId } = req.body as {
      subModuleId: string;
    };

    const noteData = await getNoteService(id, user._id.toString(), subModuleId);

    if (!noteData) {
      return next(new ErrorHandler(404, 'Note not found'));
    }

    return ResponseHandler.success(noteData, 'Note fetched successfully').send(
      res
    );
  }
);

export const markNoteCompletedController = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const user = req.user as IUser;
    const { subModuleId, batchId } = req.body as {
      subModuleId: string;
      batchId: string;
    };

    const { userProgress, message } = await markNoteCompletedService(
      id,
      user._id.toString(),
      subModuleId,
      batchId
    );

    return ResponseHandler.success(userProgress, message).send(res);
  }
);
