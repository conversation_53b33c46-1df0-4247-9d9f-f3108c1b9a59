import { Request, Response, NextFunction } from 'express';
import {
  getAllUserBookmarkService,
  getUserBookmarksByBatchService,
  toggleBookmarkService
} from '../../services/public/bookmark.service';
import mongoose from 'mongoose';
import { IUser } from '../../interfaces/user.interface';
import ResponseHandler from '../../utils/response-handler';
import asyncHandler from '../../utils/async-handler';
import { IBookmark } from '../../interfaces/bookmark.interface';
import ErrorHandler from '../../utils/error-handler';

export const listBookmarks = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const currentUser = req.user as IUser;
    if (!currentUser) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const { batchId } = req.params;
    if (!batchId) {
      return next(new ErrorHandler(400, 'Batch ID is required'));
    }

    const userId = new mongoose.Types.ObjectId(currentUser._id);
    const data = await getUserBookmarksByBatchService(userId, batchId);

    if (!data) {
      return next(new ErrorHandler(404, 'No bookmarks found for this batch'));
    }

    return ResponseHandler.success(
      data,
      'Bookmarks retrieved successfully'
    ).send(res);
  }
);

export const toggleBookmarkController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const currentUser = req.user as IUser;
    if (!currentUser) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const { batch, submodule, contentType, contentId } =
      req.body as Partial<IBookmark>;
    if (!batch || !submodule || !contentType || !contentId) {
      return next(new ErrorHandler(400, 'Missing required bookmark fields'));
    }

    const bookmark = await toggleBookmarkService(
      currentUser._id,
      batch,
      submodule,
      contentType,
      contentId
    );
    if (!bookmark) {
      return next(new ErrorHandler(500, 'Failed to toggle bookmark'));
    }
    return ResponseHandler.success(
      bookmark,
      'Bookmark toggled successfully'
    ).send(res);
  }
);

export const getMyBookmarksController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const currentUser = req.user as IUser;
    if (!currentUser) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const bookmarks = await getAllUserBookmarkService(currentUser._id);
    if (!bookmarks) {
      return next(new ErrorHandler(500, 'Failed to retrieve bookmarks'));
    }
    return ResponseHandler.success(
      bookmarks,
      'Bookmarks retrieved successfully'
    ).send(res);
  }
);
