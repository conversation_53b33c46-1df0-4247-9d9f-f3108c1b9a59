import { Request, Response, NextFunction } from 'express';
import {
  getDownloadLinkOfCertificate,
  verifyCredentialService
} from '../../services/public/credential.service';
import ResponseHandler from '../../utils/response-handler';
import asyncHandler from '../../utils/async-handler';
import ErrorHandler from '../../utils/error-handler';
import { IUser } from '../../interfaces/user.interface';
import certificateDao from '../../dao/certificate.dao';

export const verifyCredentialController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { credentialId } = req.params;

    if (!credentialId) {
      return next(new ErrorHandler(400, 'Credential ID is required'));
    }

    // Verify against the database
    const credentialData = await verifyCredentialService(credentialId);

    if (!credentialData) {
      return next(new ErrorHandler(404, 'Credential not found or invalid'));
    }

    // Render the credential verification page
    // This can be an HTML response with professional styling to match LinkedIn's verification page
    return ResponseHandler.success({
      data: credentialData,
      message: 'Credential verified successfully'
    }).send(res);
  }
);

export const getSocialShareLink = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const currentUser = req.user as IUser;
      const socialHandle = req.params.social;
      if (!currentUser) {
        return next(new ErrorHandler(401, 'User not authenticated'));
      }

      const { certificateId } = req.params;

      if (!certificateId) {
        return next(new ErrorHandler(400, 'Certificate ID is required'));
      }

      // get the certificate data from the database

      const certificateUrl = await getDownloadLinkOfCertificate(certificateId);
      if (!certificateUrl) {
        return next(new ErrorHandler(404, 'Certificate not found or invalid'));
      }

      const certName = encodeURIComponent('Data Structures in C++');
      const certId = encodeURIComponent(certificateId);
      const certUrl = encodeURIComponent(
        'https://sheryians.com/credential/' + certificateId
      );
      const organizationId = '76112249';

      const link = `https://www.linkedin.com/profile/add?startTask=CERTIFICATION_NAME&name=${certName}&organizationId=${organizationId}&certUrl=${certUrl}&certId=${certId}`;

      if (socialHandle === 'linkedin') {
        // Generate LinkedIn share link
        return ResponseHandler.success({
          data: link,
          message: 'LinkedIn certificate link generated successfully'
        }).send(res);
      }

      if (socialHandle === 'whatsapp') {
        const whatsappLink = `https://api.whatsapp.com/send?text=Check%20this%20PDF%20link:${certificateUrl}`;
        return ResponseHandler.success({
          data: whatsappLink,
          message: 'WhatsApp certificate link generated successfully'
        }).send(res);
      }

      if (socialHandle === 'download') {
        const downloadLink = `${certificateUrl}`;
        return ResponseHandler.success({
          data: downloadLink,
          message: 'Download certificate link generated successfully'
        }).send(res);
      }

      return ResponseHandler.success({
        data: link,
        message: 'LinkedIn certificate link generated successfully'
      }).send(res);
    } catch (err) {
      console.error('Error generating LinkedIn certificate link:', err);
      return new ErrorHandler(
        500,
        'An error occurred while generating the LinkedIn certificate link'
      );
    }
  }
);

export const verifyCertificateController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { certificateId } = req.params;

    if (!certificateId) {
      return next(new ErrorHandler(400, 'Certificate ID is required'));
    }

    // Find certificate in database
    const certificate = await certificateDao.findByCertificateId(certificateId);

    if (!certificate) {
      return next(new ErrorHandler(404, 'Certificate not found or invalid'));
    }

    // Return verification response
    return ResponseHandler.success(
      {
        isValid: true,
        certificate: {
          id: certificate.certificateId,
          holder: certificate.metadata?.userName || 'Student',
          course: certificate.metadata?.courseName || 'Course',
          issueDate: certificate.issueDate,
          certificateUrl: certificate.certificateUrl,
          organization: 'Sheryians Coding School'
        }
      },
      'Certificate verified successfully'
    ).send(res);
  }
);
