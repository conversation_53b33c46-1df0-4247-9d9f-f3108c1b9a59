import { Request, Response, NextFunction } from 'express';
import asyncHand<PERSON> from '../../utils/async-handler';
import { IUser } from '../../interfaces/user.interface';
import {
  getCodingProblemService,
  runCodingProblemService,
  submitCodingProblemSolutionService,
  getSolutionService
} from '../../services/public/coding-problem.service';
import <PERSON>rrorHandler from '../../utils/error-handler';
import ResponseHandler from '../../utils/response-handler';

// Define interface for custom test cases
interface CustomTestCase {
  input: string;
  expectedOutput: string;
}

export const getCodingProblemController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const user = req.user as IUser;
    const { subModuleId } = req.body as {
      subModuleId: string;
    };

    const codingProblemData = await getCodingProblemService(
      id,
      user._id.toString(),
      subModuleId
    );

    if (!codingProblemData) {
      return next(new ErrorHandler(404, 'Coding problem not found'));
    }

    return ResponseHandler.success(
      codingProblemData,
      'Coding problem fetched successfully'
    ).send(res);
  }
);

export const runCodeController = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { code, language, customTestCases } = req.body as {
      code: string;
      language: string;
      compilerId: string;
      customTestCases?: CustomTestCase[];
    };
    const user = req.user as IUser;

    const runResults = await runCodingProblemService(
      id,
      user._id.toString(),
      code,
      language,
      customTestCases
    );

    return ResponseHandler.success(
      runResults,
      'Code executed successfully'
    ).send(res);
  }
);

export const submitCodeController = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { code, language, subModuleId } = req.body as {
      code: string;
      language: string;
      subModuleId: string;
    };
    const user = req.user as IUser;

    const submissionResult = await submitCodingProblemSolutionService(
      id,
      user._id.toString(),
      code,
      language,
      subModuleId
    );

    return ResponseHandler.success(
      submissionResult,
      'Code submitted successfully'
    ).send(res);
  }
);

export const getSolutionController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const { language } = req.query;
    const user = req.user as IUser;

    const solutionData = await getSolutionService(
      id,
      user._id.toString(),
      language as string
    );

    if (!solutionData) {
      return next(new ErrorHandler(404, 'Solution not found'));
    }

    return ResponseHandler.success(
      solutionData,
      'Solution fetched successfully'
    ).send(res);
  }
);
