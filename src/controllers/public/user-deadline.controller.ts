import { Request, Response, NextFunction } from 'express';

import {
  pauseUserDeadlineAdjustment,
  resumePauseEarlyAdjustment
} from '../../services/public/user-deadline.service';
import asyncHandler from '../../utils/async-handler';
import { IUser } from '../../interfaces/user.interface';
import ResponseHandler from '../../utils/response-handler';
import <PERSON>rror<PERSON>andler from '../../utils/error-handler';

/**
 * Adjust user deadlines when a batch is paused
 */
export const adjustUserDeadlinesOnPause = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { batchId, pauseDays, pauseStartDate } = req.body as {
      batchId: string;
      pauseDays: number;
      pauseStartDate: Date;
    };
    const user = req.user as IUser;
    if (!user) {
      return next(new ErrorHandler(401, 'Unauthorized'));
    }
    const response = await pauseUserDeadlineAdjustment(
      user._id,
      batchId,
      pauseDays,
      pauseStartDate
    );
    if (response === null) {
      return next(
        new ErrorHandler(
          400,
          'Failed to adjust user deadline. Please check the user ID and batch ID.'
        )
      );
    }

    return ResponseHandler.success(
      { message: 'User deadline adjusted successfully' },
      'User deadline adjusted successfully'
    ).send(res);
  }
);

export const adjustUserDeadlinesOnResume = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { batchId, daysToAdjustBack } = req.body as {
      batchId: string;
      daysToAdjustBack: number;
    };
    const user = req.user as IUser;
    if (!user) {
      return next(new ErrorHandler(401, 'Unauthorized'));
    }
    const response = await resumePauseEarlyAdjustment(
      user._id,
      batchId,
      daysToAdjustBack
    );

    if (response === null) {
      return next(
        new ErrorHandler(
          400,
          'Failed to adjust user deadline. Please check the user ID and batch ID.'
        )
      );
    }

    return ResponseHandler.success(
      { message: 'User deadline adjusted successfully' },
      'User deadline adjusted successfully'
    ).send(res);
  }
);
