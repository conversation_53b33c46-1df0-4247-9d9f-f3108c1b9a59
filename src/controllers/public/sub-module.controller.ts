import { NextFunction, Request, Response } from 'express';
import <PERSON>rrorHand<PERSON> from '../../utils/error-handler';
import asyncHandler from '../../utils/async-handler';
import { getSubmoduleContentServicee } from '../../services/public/sub-module.service';
import mongoose, { Types } from 'mongoose';
import userProgressDao from '../../dao/user-progress.dao';
import { isSubmoduleUnlocked } from '../../utils/submodule-access.util';
import { IUser } from '../../interfaces/user.interface';
import SubModule from '../../models/submodule.model';
import ResponseHandler from '../../utils/response-handler';
import { DeadlineCalculator } from '../../utils/deadline-calculator.util';

interface EnrollmentDetails {
  batchId: string;
  courseId: string;
}

interface RequestWithEnrollment extends Request {
  enrollmentDetails?: EnrollmentDetails;
  user?: IUser;
}

export const getSubmoduleContentController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { enrollmentDetails } = req as RequestWithEnrollment;
    if (!enrollmentDetails) {
      return next(new ErrorHandler(400, 'Enrollment details not found'));
    }

    const { submoduleId } = req.params;
    const { user } = req;

    if (!user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    // Get the calculated progress data
    const batchId = new mongoose.Types.ObjectId(enrollmentDetails.batchId);
    const userId = new mongoose.Types.ObjectId(user._id);
    const courseId = new mongoose.Types.ObjectId(enrollmentDetails.courseId);

    const isModuleUnlocked = await isSubmoduleUnlocked(
      userId,
      batchId,
      courseId,
      new mongoose.Types.ObjectId(submoduleId)
    );

    if (!isModuleUnlocked.unlocked) {
      return ResponseHandler.forbidden('Submodule is locked').send(res);
    }

    // Get submodule details
    const submodule = (await SubModule.findById(submoduleId)
      .populate({
        path: 'module',
        select: 'course'
      })
      .select('title description module order')) as {
      title: string;
      description: string;
      module: {
        course: mongoose.Types.ObjectId;
      };
      order: number;
    };

    if (!submodule) {
      return ResponseHandler.notFound('Submodule not found').send(res);
    }

    // Call service to get content with progress data
    const progressData = await getSubmoduleContentServicee(
      submoduleId,
      user._id.toString()
    );

    // Validate and update stored progress if needed
    const courseIdString = submodule.module.course.toString();
    const userProgress = await userProgressDao.findByUserAndCourse(
      user._id,
      courseIdString
    );

    if (userProgress) {
      // Check if the stored submodule progress matches the calculated progress
      const subModuleProgressIndex = userProgress.subModuleProgress.findIndex(
        progress => progress.subModule.toString() === submoduleId
      );

      if (subModuleProgressIndex >= 0) {
        const storedProgress =
          userProgress.subModuleProgress[subModuleProgressIndex].progress;
        const calculatedProgress = progressData.progress;

        // Get penalty information for this submodule

        // Add penalty information to the progressData

        // Add penalty information for each content item
        if (
          userProgress.contentItemProgress &&
          userProgress.contentItemProgress.length > 0
        ) {
          // Get all content items for this submodule
          const submoduleContentItems = userProgress.contentItemProgress.filter(
            item =>
              item.subModuleId && item.subModuleId.toString() === submoduleId
          );

          // Process lessons content
          if (progressData.content?.lessons) {
            progressData.content.lessons = progressData.content.lessons.map(
              contentItem => {
                const matchingItem = submoduleContentItems.find(
                  item => item.contentId.toString() === contentItem.contentId
                );

                if (matchingItem) {
                  return {
                    ...contentItem,
                    originalPoints: matchingItem.originalPoints || 0,
                    penaltyApplied: matchingItem.penaltyApplied || 0, // Renamed for clarity
                    daysLate: matchingItem.daysLate || 0
                  };
                }
                return contentItem;
              }
            );
          }

          // Process practice content
          if (progressData.content && progressData.content.practice) {
            progressData.content.practice = progressData.content.practice.map(
              contentItem => {
                const matchingItem = submoduleContentItems.find(
                  item => item.contentId.toString() === contentItem.contentId
                );

                if (matchingItem) {
                  return {
                    ...contentItem,
                    originalPoints: matchingItem.originalPoints || 0,
                    penaltyPercentage: matchingItem.penaltyApplied || 0, // Renamed for clarity
                    daysLate: matchingItem.daysLate || 0
                  };
                }
                return contentItem;
              }
            );
          }
        }

        // If the calculated progress doesn't match the stored progress, update it
        if (storedProgress !== calculatedProgress) {
          await userProgressDao.findOneAndUpdate(
            {
              user: user._id,
              'subModuleProgress.subModule': new Types.ObjectId(submoduleId)
            },
            {
              $set: {
                'subModuleProgress.$.progress': calculatedProgress,
                'subModuleProgress.$.pointsEarned':
                  progressData.progressDetails.overallScore.userPoints
              }
            }
          );
        }
      }
    }

    return ResponseHandler.success(
      progressData,
      'Submodule progress and content fetched successfully'
    ).send(res);
  }
);

export const getSubmoduleDeadlinesController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { enrollmentDetails } = req as RequestWithEnrollment;
    if (!enrollmentDetails) {
      return next(new ErrorHandler(400, 'Enrollment details not found'));
    }

    const { submoduleId } = req.params;
    const { user } = req;

    if (!user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    // Get the calculated deadlines for the submodule
    const batchId = new mongoose.Types.ObjectId(enrollmentDetails.batchId);
    const userId = new mongoose.Types.ObjectId(user._id);

    // Call service to get deadlines
    const deadlines = await DeadlineCalculator.getUserSubmoduleDeadline(
      userId,
      batchId,
      new mongoose.Types.ObjectId(submoduleId)
    );

    if (!deadlines) {
      return ResponseHandler.notFound('Deadlines not found').send(res);
    }

    return ResponseHandler.success(
      deadlines,
      'Submodule deadlines fetched successfully'
    ).send(res);
  }
);

export const getUserBatchDeadlinesController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { user } = req;

    if (!user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    // Get the calculated deadlines for all submodules in the batch
    const batchId = new mongoose.Types.ObjectId(req.params.batchId);
    const userId = new mongoose.Types.ObjectId(user._id);

    // Call service to get deadlines
    const deadlines = await DeadlineCalculator.getUserBatchDeadlines(
      userId,
      batchId
    );

    if (!deadlines || deadlines.length === 0) {
      return ResponseHandler.notFound('Deadlines not found').send(res);
    }

    return ResponseHandler.success(
      deadlines,
      'Batch deadlines fetched successfully'
    ).send(res);
  }
);
