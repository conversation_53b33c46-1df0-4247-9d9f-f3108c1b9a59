import { Request, Response, NextFunction } from 'express';
import <PERSON>rro<PERSON><PERSON>and<PERSON> from '../../utils/error-handler';
import { getCourseModulesService } from '../../services/public/course-modules.service';
import ResponseHandler from '../../utils/response-handler';
import asyncHandler from '../../utils/async-handler';

export const getCourseModulesController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { courseId } = req.params;
    const { user } = req;

    if (!user) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    // Check if batch ID is provided in query parameters
    const batchId = req.query.batchId as string;

    // Get course modules data
    const moduleData = await getCourseModulesService(
      courseId,
      user._id.toString(),
      batchId
    );

    return ResponseHandler.success(
      moduleData,
      'Course modules retrieved successfully'
    ).send(res);
  }
);
