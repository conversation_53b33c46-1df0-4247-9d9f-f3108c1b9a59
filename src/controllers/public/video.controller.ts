import { Request, Response, NextFunction } from 'express';
import asyncHand<PERSON> from '../../utils/async-handler';
import { IUser } from '../../interfaces/user.interface';
import ErrorHandler from '../../utils/error-handler';
import {
  getVideoService,
  getVideoWithIframeSrcService,
  updateVideoProgressService
} from '../../services/public/video.service';
import ResponseHandler from '../../utils/response-handler';

export const getVideoController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const user = req.user as IUser;
    const { subModuleId } = req.body as {
      subModuleId: string;
    };

    const videoData = await getVideoService(
      id,
      user._id.toString(),
      subModuleId
    );

    if (!videoData) {
      return next(new ErrorHandler(404, 'Video not found'));
    }

    return ResponseHandler.success(
      videoData,
      'Video fetched successfully'
    ).send(res);
  }
);

export const updateVideoProgressController = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { watchedDuration, completed, subModuleId } = req.body as {
      watchedDuration: number;
      completed: boolean;
      subModuleId: string;
    };
    const user = req.user as IUser;
    const { progress, message } = await updateVideoProgressService(
      id,
      user._id.toString(),
      watchedDuration,
      subModuleId,
      completed
    );

    return ResponseHandler.success(progress, message).send(res);
  }
);

export const getVideoWithIframeSrcController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const user = req.user as IUser;
    const { subModuleId } = req.body as {
      subModuleId: string;
    };

    const videoData = await getVideoWithIframeSrcService(
      id,
      user._id.toString(),
      subModuleId
    );

    if (!videoData) {
      return next(new ErrorHandler(404, 'Video not found'));
    }

    return ResponseHandler.success(
      videoData,
      'Video fetched successfully'
    ).send(res);
  }
);
