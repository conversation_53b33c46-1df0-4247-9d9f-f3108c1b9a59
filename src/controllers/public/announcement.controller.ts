import { NextFunction, Request, Response } from 'express';
import asyncHandler from '../../utils/async-handler';
import {
  getAllAnnouncementService,
  getAnnouncementByIdService
} from '../../services/public/announcement.service';
import ResponseHandler from '../../utils/response-handler';
import ErrorHandler from '../../utils/error-handler';

export const getAllAnnouncementController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { batchId } = req.params;
    const announcements = await getAllAnnouncementService({
      batch: batchId,
      isActive: true
    });
    if (!announcements) {
      return next(new Error('No announcements found'));
    }
    return ResponseHandler.success(
      announcements,
      'Announcements retrieved successfully'
    ).send(res);
  }
);

export const getAnnouncementByIdController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const announcement = await getAnnouncementByIdService(id);
    if (!announcement) {
      return next(new ErrorHandler(404, 'Announcement not found'));
    }
    return ResponseHandler.success(
      announcement,
      'Announcement retrieved successfully'
    ).send(res);
  }
);
