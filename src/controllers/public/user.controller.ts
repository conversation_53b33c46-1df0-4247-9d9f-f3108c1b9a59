import asyncHandler from '../../utils/async-handler';
import { NextFunction, Request, Response } from 'express';
import { getAllEnrolledCoursesLinksService } from '../../services/private/user.service';
import ResponseHandler from '../../utils/response-handler';
import <PERSON>rrorHandler from '../../utils/error-handler';
import { IUser } from '../../interfaces/user.interface';

export const getEnrolledLinkCoursesController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const user = req.user as IUser;
    if (!user) {
      return next(new ErrorHandler(401, 'Unauthorized'));
    }

    const userEnrolledCourses = await getAllEnrolledCoursesLinksService(
      user._id
    );

    if (!userEnrolledCourses || userEnrolledCourses.length === 0) {
      return next(
        new ErrorHandler(404, 'No enrolled courses found for this user')
      );
    }

    return ResponseHandler.success(
      { userEnrolledCourses },
      'Enrolled courses fetched successfully'
    ).send(res);
  }
);
