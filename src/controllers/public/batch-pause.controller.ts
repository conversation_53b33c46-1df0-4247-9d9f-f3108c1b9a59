import { NextFunction, Request, Response } from 'express';
import batchPauseService from '../../services/public/batch-pause.service';
import asyncHandler from '../../utils/async-handler';
import ResponseHandler from '../../utils/response-handler';
import ErrorHandler from '../../utils/error-handler';
import moment from 'moment';
import { pauseUserDeadlineAdjustment } from '../../services/public/user-deadline.service';

/**
 * Pause a batch for a user
 */
export const pauseBatch = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { batchId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const { startDate, endDate, reason, comments } = req.body as {
      startDate: string;
      endDate: string;
      reason: string;
      comments?: string;
    };

    const start = moment(startDate, 'YYYY-MM-DD');
    const end = moment(endDate, 'YYYY-MM-DD');
    const today = moment().startOf('day');

    if (!start.isValid() || !end.isValid()) {
      return next(new ErrorHandler(400, 'Invalid start date or end date'));
    }

    if (start.isBefore(today)) {
      return next(new ErrorHandler(400, 'Start date cannot be in the past'));
    }

    if (end.isBefore(start)) {
      return next(
        new ErrorHandler(400, 'End date cannot be before start date')
      );
    }

    const daysBetween = end.diff(start, 'days');

    if (daysBetween <= 0) {
      return next(new ErrorHandler(400, 'End date must be after start date'));
    }

    if (daysBetween > 60) {
      return next(
        new ErrorHandler(400, 'Pause duration cannot exceed 60 days')
      );
    }

    const result = await batchPauseService.pauseBatch({
      userId: userId.toString(),
      batchId,
      startDate,
      endDate,
      reason,
      comments
    });

    if (!result) {
      return next(new ErrorHandler(400, 'Failed to pause batch'));
    }

    // Adjust user deadline
    await pauseUserDeadlineAdjustment(
      userId,
      batchId,
      daysBetween,
      start.toDate()
    ).catch(error => {
      console.error('Error adjusting user deadline:', error);
    });

    return ResponseHandler.success(
      { batchPause: result },
      'Batch paused successfully'
    ).send(res);
  }
);

/**
 * Resume a previously paused batch
 */
export const resumeBatch = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { pauseId } = req.params;
    const result = await batchPauseService.resumeBatch(pauseId);

    if (!result) {
      return next(
        new ErrorHandler(404, 'Pause record not found or already completed')
      );
    }

    return ResponseHandler.success(
      { result },
      'Batch resumed successfully'
    ).send(res);
  }
);

/**
 * Get pause history for a batch
 */
export const getPauseHistory = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { batchId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const pauseHistory = await batchPauseService.getPauseHistory(
      userId.toString(),
      batchId
    );

    return ResponseHandler.success(
      { pauseHistory },
      'Pause history fetched successfully'
    ).send(res);
  }
);

/**
 * Get remaining pause days for a user in a batch
 */
export const getRemainingPauseDays = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { batchId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return next(new ErrorHandler(401, 'User not authenticated'));
    }

    const remainingDays = await batchPauseService.getRemainingPauseDays(
      userId.toString(),
      batchId
    );

    return ResponseHandler.success(
      { remainingDays },
      'Remaining pause days fetched successfully'
    ).send(res);
  }
);
