import { Request, Response } from 'express';
import { fetchLeaderboardDetails } from '../../services/public/leaderboard.service';
import { Period } from '../../interfaces/leaderboard.interface';
import ResponseHandler from '../../utils/response-handler';
import asyncHandler from '../../utils/async-handler';

export const getLeaderboardControllerByBatchId = asyncHandler(
  async (req: Request, res: Response) => {
    const { period = 'overall' } = req.query;
    const { batchId } = req.params;
    const leaderboard = await fetchLeaderboardDetails(
      batchId,
      period as Period
    );
    return ResponseHandler.success(
      leaderboard,
      'Leaderboard fetched successfully'
    ).send(res);
  }
);
