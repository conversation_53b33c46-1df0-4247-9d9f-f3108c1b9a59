// import { Request, Response, NextFunction } from 'express';
// import asyncHandler from '../../../utils/async-handler';
// import ErrorHandler from '../../../utils/error-handler';
// import { getCodingLabService, submitCodingLabSolutionService } from '../../../services/public/content/coding-lab.service';
// import { IUser } from '../../../interfaces/user.interface';

// export const getCodingLabController = asyncHandler(
//   async (req: Request, res: Response, next: NextFunction) => {
//     const { id } = req.params;
//     const user = req.user as IUser;

//     const codingLabData = await getCodingLabService(id, user._id.toString());

//     if (!codingLabData) {
//       return next(new ErrorHandler(404, 'Coding lab not found'));
//     }

//     return res.status(200).json({
//       success: true,
//       message: 'Coding lab fetched successfully',
//       data: codingLabData
//     });
//   }
// );

// export const submitCodingLabSolutionController = asyncHandler(
//   async (req: Request, res: Response, next: NextFunction) => {
//     const { id } = req.params;
//     const { code, language, compilerId } = req.body;
//     const user = req.user as IUser;

//     const submissionResult = await submitCodingLabSolutionService(
//       id,
//       user._id.toString(),
//       code,
//       language,
//       compilerId
//     );

//     return res.status(200).json({
//       success: true,
//       message: 'Coding lab solution submitted successfully',
//       data: submissionResult
//     });
//   }
// );
