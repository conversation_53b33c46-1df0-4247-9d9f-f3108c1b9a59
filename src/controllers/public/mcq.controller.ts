import { Request, Response, NextFunction } from 'express';
import asyncHand<PERSON> from '../../utils/async-handler';
import { IUser } from '../../interfaces/user.interface';
import {
  getMCQService,
  submitMCQAnswerService
} from '../../services/public/mcq.service';
import <PERSON>rror<PERSON>and<PERSON> from '../../utils/error-handler';
import ResponseHandler from '../../utils/response-handler';

export const getMCQController = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const user = req.user as IUser;
    const { subModuleId } = req.body as {
      subModuleId: string;
    };

    const mcqData = await getMCQService(id, user._id.toString(), subModuleId);

    if (!mcqData) {
      return next(new ErrorHandler(404, 'MCQ not found'));
    }

    return ResponseHandler.success(mcqData, 'MCQ fetched successfully').send(
      res
    );
  }
);

export const submitMCQAnswerController = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { selectedOptions, subModuleId } = req.body as {
      selectedOptions: string[];
      subModuleId: string;
    };
    const user = req.user as IUser;
    const { submissionResult, message } = await submitMCQAnswerService(
      id,
      user._id.toString(),
      subModuleId,
      selectedOptions
    );

    return ResponseHandler.success(submissionResult, message).send(res);
  }
);
