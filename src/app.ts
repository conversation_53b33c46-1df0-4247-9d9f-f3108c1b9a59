import express, { Request, Response } from 'express';
// import './queues/worker';
// import './schedulers/scheduler.leaderboard';
import Router from './routes';
import { config } from './config/config';
import errorMiddleware from './middlewares/error.middleware';
import compression from 'compression';
import ResponseHandler from './utils/response-handler';
import cors from 'cors';
import cookieParser from 'cookie-parser';
// import { defaultLimiter } from './middlewares/rateLimit.middleware';
import { configureAppSecurity } from './config/security';
// import UserDeadline from './models/user-deadline.model';
// import mongoose from 'mongoose';

const app = express();

// Apply rate limiting globally
// app.use(defaultLimiter);

// body parser middleware to handle raw json and form data with limit of 10mb and 10mb respectively
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// cookie parser middleware to handle cookies in request
app.use(cookieParser());

// cors middleware to handle cross-origin requests`
app.use(
  cors({
    origin(requestOrigin: string | undefined, callback) {
      if (!requestOrigin || config.corsOrigin.indexOf(requestOrigin) !== -1) {
        callback(null, true);
      } else {
        callback(new Error(`Origin ${requestOrigin} not allowed by CORS`));
      }
    }
  })
);
// const func = async ()=>{
//   const session = await UserDeadline.startSession();
//   session.startTransaction();
//   await UserDeadline.initializeUserDeadlines(
//       new mongoose.Types.ObjectId("68010e57b051e20180fa4042"),
//       new mongoose.Types.ObjectId("6825e2e495126cbb2bc384a6"),
//       session
//   );
//   await session.commitTransaction();
//   await session.endSession();
// }

// func().then(() => {
//   console.warn('User deadlines initialized successfully');
// }).catch((error) => {
//   console.error('Error initializing user deadlines:', error);
// });

// Add compression middleware here, before routes
app.use(
  compression({
    level: 7,
    filter: (req, res) => {
      if (req.headers['x-no-compression']) {
        return false; // Bypass compression for specific requests
      }
      return compression.filter(req, res);
    }
  })
);

// Apply security configuration including CSRF
configureAppSecurity(app);

// API routes
app.use('/', Router);

// 404 middleware
app.use((req: Request, res: Response) => {
  ResponseHandler.notFound(
    `Route not found: ${req.method} ${req.originalUrl}`
  ).send(res);
});

// Error middleware
app.use(errorMiddleware);

export default app;
