import { config as dotenvConfig } from 'dotenv';
import { z } from 'zod';

dotenvConfig();

const configSchema = z.object({
  port: z.coerce.number().default(8001),
  databaseUrl: z.string().min(1, 'MONGO_CONNECTION_STRING is required'),
  environment: z
    .enum(['development', 'production', 'test'])
    .default('development'),
  corsOrigin: z.string().min(1, 'CORS_ORIGIN is required'),
  accessTokenSecret: z.string().min(1, 'JWT_ACCESS_SECRET is required'),
  accessTokenExpiration: z.string().min(1, 'JWT_ACCESS_EXPIRATION is required'),
  refreshTokenSecret: z.string().min(1, 'JWT_REFRESH_SECRET is required'),
  refreshTokenExpiration: z
    .string()
    .min(1, 'JWT_REFRESH_EXPIRATION is required'),
  cookieAccessMaxAge: z.string().min(1, 'COOKIE_ACCESS_MAX_AGE is required'), // Consider z.coerce.number() if it should be a number
  cookieRefreshMaxAge: z.string().min(1, 'COOKIE_REFRESH_MAX_AGE is required'), // Consider z.coerce.number() if it should be a number
  mcqAttemptsLimit: z.coerce.number().default(3), // Default 3 attempts if not specified
  imagekit: z.object({
    publicKey: z.string().min(1, 'IMAGEKIT_PUBLIC_KEY is required'),
    privateKey: z.string().min(1, 'IMAGEKIT_PRIVATE_KEY is required'),
    urlEndpoint: z.string().url('IMAGEKIT_URL_ENDPOINT must be a valid URL')
  }),
  email: z.object({
    clientId: z.string().min(1, 'EMAIL_CLIENT_ID is required'),
    clientSecret: z.string().min(1, 'EMAIL_CLIENT_SECRET is required'),
    refreshToken: z.string().min(1, 'EMAIL_REFRESH_TOKEN is required'),
    user: z.string().email('EMAIL_USER must be a valid email')
  }),
  redisUrl: z.string().url('REDIS_URL must be a valid URL'),
  msg91AuthKey: z.string().min(1, 'MSG91_AUTH_KEY is required'),
  msg91OtpUrl: z.string().url('MSG91_OTP_URL must be a valid URL'),
  msg91OtpTemplateId: z.string().min(1, 'MSG91_OTP_TEMPLATE_ID is required'),
  defaultThumbnail: z.string().url('DEFAULT_THUMBNAIL must be a valid URL'),
  razorpayKeyId: z.string().min(1, 'RAZORPAY_KEY_ID is required'),
  razorpayKeySecret: z.string().min(1, 'RAZORPAY_KEY_SECRET is required'),
  razorpayWebhookSecret: z
    .string()
    .min(1, 'RAZORPAY_WEBHOOK_SECRET is required'),
  serverApiKey: z.string().min(1, 'SERVER_API_KEY is required'),
  prePurchaseUrl: z.string().url('PRE_PURCHASE_URL must be a valid URL'),
  vdoCipherApiSecret: z.string().min(1, 'VDOCIPHER_API_SECRET is required'),
  judge0ApiKey: z.string().min(1, 'JUDGE0_API_KEY is required'),
  judge0ApiUrl: z.string().url('JUDGE0_API_URL must be a valid URL'),
  certificateCompletion: z
    .string()
    .min(1, 'CERTIFICATE_COMPLETION is required'),
  certificateCompletionPercentage: z
    .string()
    .min(1, 'CERTIFICATE_COMPLETION_PERCENTAGE is required'),
  certificateExcellence: z
    .string()
    .min(1, 'CERTIFICATE_EXCELLENCE is required'),
  certificateExcellencePercentage: z
    .string()
    .min(1, 'CERTIFICATE_EXCELLENCE_PERCENTAGE is required'),
  penaltyPercentage: z.string().min(1, 'PENALTY_PERCENTAGE is required'),
  subModuleCompletionPercentage: z
    .string()
    .min(1, 'SUBMODULE_COMPLETION_PERCENTAGE is required')
    .default('70')
  // Add other environment variables as needed
});

const parsedConfig = configSchema.safeParse({
  port: process.env.PORT,
  databaseUrl: process.env.MONGO_CONNECTION_STRING,
  environment: process.env.NODE_ENV,
  corsOrigin: process.env.CORS_ORIGIN,
  accessTokenSecret: process.env.JWT_ACCESS_SECRET,
  accessTokenExpiration: process.env.JWT_ACCESS_EXPIRATION,
  refreshTokenSecret: process.env.JWT_REFRESH_SECRET,
  refreshTokenExpiration: process.env.JWT_REFRESH_EXPIRATION,
  cookieAccessMaxAge: process.env.COOKIE_ACCESS_MAX_AGE,
  cookieRefreshMaxAge: process.env.COOKIE_REFRESH_MAX_AGE,
  mcqAttemptsLimit: process.env.MCQ_ATTEMPTS_LIMIT,
  imagekit: {
    publicKey: process.env.IMAGEKIT_PUBLIC_KEY,
    privateKey: process.env.IMAGEKIT_PRIVATE_KEY,
    urlEndpoint: process.env.IMAGEKIT_URL_ENDPOINT
  },
  email: {
    clientId: process.env.EMAIL_CLIENT_ID,
    clientSecret: process.env.EMAIL_CLIENT_SECRET,
    refreshToken: process.env.EMAIL_REFRESH_TOKEN,
    user: process.env.EMAIL_USER
  },
  redisUrl: process.env.REDIS_URL,
  msg91AuthKey: process.env.MSG91_AUTH_KEY,
  msg91OtpUrl: process.env.MSG91_OTP_URL,
  msg91OtpTemplateId: process.env.MSG91_OTP_TEMPLATE_ID,
  defaultThumbnail: process.env.DEFAULT_THUMBNAIL,
  razorpayKeyId: process.env.RAZORPAY_KEY_ID,
  razorpayKeySecret: process.env.RAZORPAY_KEY_SECRET,
  razorpayWebhookSecret: process.env.RAZORPAY_WEBHOOK_SECRET,
  serverApiKey: process.env.SERVER_API_KEY,
  prePurchaseUrl: process.env.PRE_PURCHASE_URL,
  vdoCipherApiSecret: process.env.VDOCIPHER_API_SECRET,
  judge0ApiKey: process.env.JUDGE0_API_KEY,
  judge0ApiUrl: process.env.JUDGE0_API_URL,
  certificateCompletion: process.env.CERTIFICATE_COMPLETION,
  certificateCompletionPercentage:
    process.env.CERTIFICATE_COMPLETION_PERCENTAGE,
  certificateExcellence: process.env.CERTIFICATE_EXCELLENCE,
  certificateExcellencePercentage:
    process.env.CERTIFICATE_EXCELLENCE_PERCENTAGE,
  penaltyPercentage: process.env.PENALTY_PERCENTAGE,
  subModuleCompletionPercentage: process.env.SUBMODULE_COMPLETION_PERCENTAGE
});

if (!parsedConfig.success) {
  console.error('Invalid environment variables:', {
    errors: parsedConfig.error.flatten().fieldErrors
  });
  throw new Error('Invalid environment variables');
}

const _config = parsedConfig.data;

export const config = Object.freeze(_config);

// Optional: Define a type for better intellisense
export type ConfigType = z.infer<typeof configSchema>;
