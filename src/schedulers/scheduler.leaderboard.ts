import cron from 'node-cron';
import LeaderboardDao from '../dao/leaderboard.dao';
import { getPeriodDates } from '../services/public/leaderboard.service';

cron.schedule('*/2 * * * *', async () => {
  console.warn('Running daily leaderboard initialization at midnight...');
  // Find all overall leaderboards
  const overallLeaderboards = await LeaderboardDao.findAll({
    period: 'overall'
  });
  const { startDate, endDate } = getPeriodDates('daily');

  for (const overall of overallLeaderboards) {
    const { course, batch, rankings } = overall;
    // Prepare daily leaderboard rankings from overall users
    const dailyRankings = (rankings || []).map((r, idx) => ({
      user: r.user,
      userName: r.userName,
      userAvatar: r.userAvatar,
      rank: idx + 1,
      points: 0,
      progress: 0,
      streak: 0
    }));
    // Check if daily leaderboard already exists for this batch/course/today
    const existingDaily = await LeaderboardDao.findOne({
      course,
      batch,
      period: 'daily',
      startDate
    });
    if (!existingDaily) {
      // Create new daily leaderboard
      await LeaderboardDao.create({
        course,
        batch,
        period: 'daily',
        startDate,
        endDate,
        rankings: dailyRankings
      });
      console.warn(
        `Created daily leaderboard for batch ${String(batch)} course ${String(course)}`
      );
    } else {
      // Add any missing users from overall to existing daily leaderboard
      const existingUserIds = new Set(
        existingDaily.rankings.map(r => String(r.user))
      );
      let changed = false;
      for (const r of dailyRankings) {
        if (!existingUserIds.has(String(r.user))) {
          existingDaily.rankings.push({
            ...r,
            rank: existingDaily.rankings.length + 1
          });
          changed = true;
        }
      }
      if (changed) {
        await LeaderboardDao.updateRankings(
          String(existingDaily._id),
          existingDaily.rankings
        );
        console.warn(
          `Updated daily leaderboard for batch ${String(batch)} course ${String(course)} with missing users.`
        );
      }
    }
  }
});
