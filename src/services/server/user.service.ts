import axios from 'axios';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../utils/error-handler';
import { IUser } from '../../interfaces/user.interface';

export const getUserById = async (userId: string): Promise<IUser | null> => {
  try {
    const response = await axios.get<{ data: { user: IUser } }>(
      `${process.env.PRE_PURCHASE_URL}/api/v3/server/users/${userId}`,
      {
        headers: {
          'server-api-key': process.env.SERVER_API_KEY,
          'Content-Type': 'application/json',
          Accept: 'application/json'
        }
      }
    );
    if (!response || !response.data || !response.data.data.user) {
      throw new ErrorHandler(404, 'User not found');
    }
    return response.data.data.user;
  } catch (error) {
    console.error('Error fetching user:', error);
    throw new ErrorHandler(500, 'Internal server error');
  }
};
