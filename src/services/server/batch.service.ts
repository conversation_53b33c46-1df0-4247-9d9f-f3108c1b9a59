import axios from 'axios';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../utils/error-handler';
import { IBatch } from '../../interfaces/batch.interface';

export const getBatchDetails = async (
  batchId: string
): Promise<IBatch | null> => {
  try {
    const response = await axios.get<{ data: { batch: IBatch } }>(
      `${process.env.PRE_PURCHASE_URL || 'http://localhost:8080'}/api/v3/server/batches/${batchId}`,
      {
        headers: {
          'server-api-key': process.env.SERVER_API_KEY,
          'Content-Type': 'application/json'
        }
      }
    );
    if (!response || !response.data || !response.data.data.batch) {
      throw new ErrorHandler(404, 'Batch not found');
    }
    return response.data.data.batch;
  } catch (error) {
    console.error('Error fetching batch:', error);
    throw new ErrorHandler(500, 'Internal server error');
  }
};
