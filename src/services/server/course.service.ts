import axios from 'axios';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../utils/error-handler';
import { ICourse } from '../../interfaces/course.interface';

export const getCourseDetails = async (
  courseId: string
): Promise<ICourse | null> => {
  try {
    const response = await axios.get<{ data: { course: ICourse } }>(
      `${process.env.PRE_PURCHASE_URL}/api/v3/server/courses/${courseId}`,
      {
        headers: {
          'server-api-key': process.env.SERVER_API_KEY,
          'Content-Type': 'application/json'
        }
      }
    );
    if (!response || !response.data || !response.data.data.course) {
      throw new <PERSON>rrorHandler(404, 'Course not found');
    }
    return response.data.data.course;
  } catch (error) {
    console.error('Error fetching course:', error);
    throw new ErrorHandler(500, 'Internal server error');
  }
};
