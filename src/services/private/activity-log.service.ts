import activityLogDao from '../../dao/activity-log.dao';

import { SortOrder } from 'mongoose';
import { IActivityLog } from '../../interfaces/activity-log.interface';
type activityLogFilter = Record<
  string,
  string | number | boolean | Date | object
>;
type activityLogSort = Record<string, SortOrder>;

export const getAllActivityLogsService = async (
  pagination: { page: number; limit: number; skip: number },
  dbQuery: { filter: activityLogFilter; sort: activityLogSort } = {
    filter: {},
    sort: { createdAt: -1 }
  }
) => {
  const { limit, skip } = pagination;
  const { filter, sort } = dbQuery;

  // Run both queries in parallel for better performance
  const [activityLogs, totalCount] = await Promise.all([
    activityLogDao.findAll(filter, sort, skip, limit),
    activityLogDao.count(filter)
  ]);

  return {
    activityLogs,
    pagination: {
      currentPage: pagination.page,
      itemsPerPage: pagination.limit,
      totalItems: totalCount,
      totalPages: Math.ceil(totalCount / pagination.limit)
    }
  };
};

export const getActivityLogsByLogId = async (
  logID: string
): Promise<IActivityLog | null> => {
  return await activityLogDao.findById(logID);
};
