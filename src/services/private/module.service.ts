import mongoose from 'mongoose';
import modulesDao from '../../dao/module.dao';
import { IModule } from '../../interfaces/module.interface';
import ErrorHandler from '../../utils/error-handler';

export const createModuleService = async (
  data: IModule
): Promise<IModule | null> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();

    const existingModules = await modulesDao.findAll(
      { course: data.course, order: data.order },
      { order: 1 }
    );

    if (existingModules.length > 0) {
      return null;
    }

    const newModule = await modulesDao.create(data, session);
    await session.commitTransaction();
    return newModule;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const updateModuleService = async (
  id: string,
  data: Partial<IModule>
): Promise<IModule | null> => {
  const session = await mongoose.startSession();
  try {
    const { order, ...allowedUpdates } = data;
    // check if the module exists
    const module = await modulesDao.findById(id);
    if (!module) {
      new ErrorHandler(404, 'Module not found');
      return null;
    }
    // If client tried to update order, log warning
    if (order) {
      new ErrorHandler(
        400,
        'Reordering modules is not allowed. Please use the reorderModuleService instead.'
      );
      return null;
    }
    session.startTransaction();
    const updatedModule = await modulesDao.update(id, allowedUpdates, session);
    await session.commitTransaction();
    return updatedModule;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};
