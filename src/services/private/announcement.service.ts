import mongoose from 'mongoose';
import announcementDao from '../../dao/announcement.dao';
import { IAnnouncement } from '../../interfaces/announcement.interface';

export const createAnnouncementService = async (
  data: Partial<IAnnouncement>,
  session?: mongoose.ClientSession
): Promise<IAnnouncement> => {
  return await announcementDao.create(data, session);
};

export const getAnnouncementService = async (
  batchId: string
): Promise<IAnnouncement | null> => {
  const allAnnouncements = await announcementDao.findAll({
    batch: batchId,
    isActive: true
  });
  if (allAnnouncements.length === 0) {
    return null;
  }
  return allAnnouncements[0];
};

export const deleteAnnouncementService = async (
  announcementId: string
): Promise<IAnnouncement | null> => {
  const session = await mongoose.startSession();
  session.startTransaction();
  try {
    const announcement = await announcementDao.update(
      { _id: announcementId },
      { isActive: false },
      session
    );
    await session.commitTransaction();
    return announcement;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const updateAnnouncementService = async (
  announcementId: string,
  data: Partial<IAnnouncement>
): Promise<IAnnouncement | null> => {
  const session = await mongoose.startSession();
  session.startTransaction();
  try {
    const updatedAnnouncement = await announcementDao.update(
      { _id: announcementId },
      data,
      session
    );
    await session.commitTransaction();
    return updatedAnnouncement;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};
