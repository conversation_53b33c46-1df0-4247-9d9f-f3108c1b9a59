import { ICodingProblem } from '../../interfaces/coding-problem.interface';
import codingProblemDao from '../../dao/coding-problem.dao';
import mongoose from 'mongoose';
import submoduleContentDao from '../../dao/submodule-content.dao';
import { submodulePointsRefactor } from '../../utils/submodule.utils';
import { canDeleteContent } from '../../utils/content.utils';
import ErrorHandler from '../../utils/error-handler';
import { uploadBase64File } from '../../utils/imagekit-uploader';

const calculatepoints = (testCases: { points?: number }[]): number => {
  return testCases.reduce((sum, testCase) => sum + (testCase.points || 0), 0);
};

export const createCodingProblemService = async (
  data: Partial<ICodingProblem>
): Promise<ICodingProblem> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();

    // Check if problemId already exists
    if (data.problemId) {
      const existing = await codingProblemDao.findByProblemId(data.problemId);
      if (existing) {
        throw new Error(`Problem with ID ${data.problemId} already exists`);
      }
    }

    // Upload images in hints if provided
    if (data.hints && data.hints.length > 0) {
      for (const hint of data.hints) {
        if (hint.image && hint.image.startsWith('data:image')) {
          const uploadedImageUrl = await uploadBase64File(
            hint.image,
            `hint_${data.problemId}`
          );
          hint.image = uploadedImageUrl || hint.image; // Replace with URL or keep original if upload fails
        }
      }
    }

    // Recalculate points based on testCases
    if (data.testCases && data.testCases.length > 0) {
      data.points = calculatepoints(data.testCases);
    }

    const newCodingProblem = await codingProblemDao.create(data, session);
    await session.commitTransaction();
    return newCodingProblem;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const getCodingProblemByIdService = async (
  id: string
): Promise<ICodingProblem | null> => {
  return await codingProblemDao.findById(id);
};

export const getCodingProblemByProblemIdService = async (
  problemId: string
): Promise<ICodingProblem | null> => {
  return await codingProblemDao.findByProblemId(problemId);
};

export const getCodingProblemsByLanguageService = async (
  language: string
): Promise<ICodingProblem[]> => {
  return await codingProblemDao.findByLanguage(language);
};

// while updating we need to be careful about afterwards the test cases can

export const updateCodingProblemService = async (
  id: string,
  data: Partial<ICodingProblem>
): Promise<ICodingProblem | null> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();

    const existing = await codingProblemDao.findById(id);
    if (!existing) {
      throw new Error(`Problem with ID ${id} does not exist`);
    }

    // Upload images in hints if provided
    if (data.hints && data.hints.length > 0) {
      for (const hint of data.hints) {
        if (hint.image && hint.image.startsWith('data:image')) {
          const uploadedImageUrl = await uploadBase64File(
            hint.image,
            `hint_${id}`
          );
          hint.image = uploadedImageUrl || hint.image; // Replace with URL or keep original if upload fails
        }
      }
    }

    // Recalculate points based on testCases
    if (data.testCases && data.testCases.length > 0) {
      data.points = calculatepoints(data.testCases);
    }

    if (data.points && data.points !== existing.points) {
      const subModuleContents = await submoduleContentDao.findAll({
        contentId: existing._id
      });
      if (subModuleContents.length > 0) {
        const pointsDifference = data.points - existing.points;
        for (const subModuleContent of subModuleContents) {
          await submodulePointsRefactor(
            subModuleContent.submodule as string,
            pointsDifference
          );
        }
      }
    }

    const updatedCodingProblem = await codingProblemDao.update(id, data);
    await session.commitTransaction();
    return updatedCodingProblem;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const deleteCodingProblemService = async (
  id: string
): Promise<ICodingProblem | null> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();
    // Check if the coding problem exists
    const existing = await codingProblemDao.findById(id);
    if (!existing) {
      throw new Error(`Problem with ID ${id} does not exist`);
    }
    // Check if the coding problem is in content-module
    const checkCanDelete = await canDeleteContent('coding_problem', id);
    // if the mcq is in content-module, then do not delete the mcq
    if (!checkCanDelete.canDelete || checkCanDelete.attached) {
      throw new ErrorHandler(403, 'Coding Problem is attached to a submodule');
    }

    const deletedCodingProblem = await codingProblemDao.delete(id, session);
    await session.commitTransaction();
    return deletedCodingProblem;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};
