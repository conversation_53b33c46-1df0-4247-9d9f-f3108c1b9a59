import { ISubmoduleContent } from '../../interfaces/submodule-content.interface';
import submoduleContentDao from '../../dao/submodule-content.dao';
import mongoose from 'mongoose';
import ErrorHandler from '../../utils/error-handler';
import mcqDao from '../../dao/mcq.dao';
import submoduleDao from '../../dao/submodule.dao';
import videosDao from '../../dao/videos.dao';
import codingProblemDao from '../../dao/coding-problem.dao';
import moduleDao from '../../dao/module.dao';
import noteDao from '../../dao/note.dao';
import { submodulePointsRefactor } from '../../utils/submodule.utils';

export const createSubmoduleContentService = async (
  data: Partial<ISubmoduleContent>
): Promise<ISubmoduleContent | null> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();

    // Validate order uniqueness
    const existingSubmoduleContent = await submoduleContentDao.findAll(
      { submodule: data.submodule },
      { order: 1 }
    );
    // Ensure the order is unique within the submodule
    const existingOrder = existingSubmoduleContent.some(content => {
      if (content.section === 'practice' && data.section === 'practice') {
        return Number(content.order) === Number(data.order);
      }
      if (content.section === 'lesson' && data.section === 'lesson') {
        return Number(content.order) === Number(data.order);
      }
      return false;
    });
    //  Ensure data.order is defined and matches the type
    if (existingOrder) {
      throw new ErrorHandler(
        400,
        'Content order must be unique within the submodule.'
      );
    }

    const existingContent = existingSubmoduleContent.find(
      content =>
        JSON.stringify(content.contentId) === JSON.stringify(data.contentId)
    );
    if (existingContent) {
      throw new ErrorHandler(
        400,
        'Content ID already exists in the submodule. Please use a different content ID.'
      );
    }

    // Validate submodule existence
    if (!data.submodule) {
      throw new ErrorHandler(400, 'Submodule ID is required.');
    }
    const existingSubmodule = await submoduleDao.findById(
      data.submodule as string
    );
    if (!existingSubmodule) {
      throw new ErrorHandler(400, 'Submodule not found.');
    }

    // Validate content ID
    if (!data.contentId) {
      throw new ErrorHandler(400, 'Content ID is required.');
    }

    // Calculate points to add based on content type - but don't apply yet
    let pointsToAdd = 0;
    const contentId = data.contentId as string;

    if (data.contentType === 'mcq') {
      const existingMcqContent = await mcqDao.findById(contentId);
      if (!existingMcqContent) {
        throw new ErrorHandler(400, 'MCQ content not found.');
      }

      pointsToAdd = existingMcqContent.points;
    } else if (data.contentType === 'video') {
      const existingVideoContent = await videosDao.findById(contentId);
      if (!existingVideoContent) {
        throw new ErrorHandler(400, 'Video content not found.');
      }
      pointsToAdd = existingVideoContent.points;
    } else if (data.contentType === 'coding_problem') {
      const existingCodingProblemContent =
        await codingProblemDao.findById(contentId);
      if (!existingCodingProblemContent) {
        throw new ErrorHandler(400, 'Coding problem content not found.');
      }
      pointsToAdd = existingCodingProblemContent.points;
    } else if (data.contentType === 'note') {
      const existingNoteContent = await noteDao.findById(contentId);
      if (!existingNoteContent) {
        throw new ErrorHandler(400, 'Note content not found.');
      }
      pointsToAdd = existingNoteContent.points;
    } else {
      throw new ErrorHandler(400, 'Unsupported content type provided.');
    }

    // Create the submodule content first
    const newSubmoduleContent = await submoduleContentDao.create(data, session);

    // Update submodule points
    const updatedSubmodule = await submoduleDao.update(
      existingSubmodule._id as string,
      {
        points: existingSubmodule.points + pointsToAdd,
        sections: {
          lesson:
            existingSubmodule.sections.lesson || data.section === 'lesson'
              ? true
              : false,
          practice:
            existingSubmodule.sections.practice || data.section === 'practice'
              ? true
              : false
        }
      },
      session
    );

    // Find and update parent module's total points
    if (updatedSubmodule && updatedSubmodule.module) {
      const moduleId = updatedSubmodule.module as string;
      const existingModule = await moduleDao.findById(moduleId);
      if (existingModule) {
        await moduleDao.update(
          moduleId,
          {
            totalPoints: (existingModule.totalPoints || 0) + pointsToAdd
          },
          session
        );
      }
    }

    await session.commitTransaction();
    return newSubmoduleContent;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const getAllSubmoduleContentById = async (
  submoduleId: string
): Promise<ISubmoduleContent | null> => {
  const subModuleContent = await submoduleContentDao.findById(submoduleId);
  if (!subModuleContent) {
    return null;
  }
  return subModuleContent;
};

const VALID_TYPE_MODEL_MAP: Record<string, string> = {
  video: 'Video',
  note: 'Note',
  mcq: 'MCQ',
  coding_problem: 'CodingProblem',
  coding_lab: 'CodingLab'
};

export const updateSubmoduleContentService = async (
  id: string,
  data: Partial<ISubmoduleContent>
): Promise<ISubmoduleContent | null> => {
  if ('order' in data) {
    throw new ErrorHandler(
      400,
      'Reordering submodules-content is not allowed. Please use the reorder endpoint.'
    );
  }

  const { contentType, contentModel, section } = data;

  if ((contentType && !contentModel) || (!contentType && contentModel)) {
    throw new ErrorHandler(
      400,
      'Both contentType and contentModel must be provided together.'
    );
  }

  if (contentType && contentModel) {
    const expectedModel = VALID_TYPE_MODEL_MAP[contentType];
    if (expectedModel !== contentModel) {
      throw new ErrorHandler(
        400,
        `Invalid combination: contentType "${contentType}" must have contentModel "${expectedModel}".`
      );
    }

    if (contentType === 'video' && section === 'practice') {
      throw new ErrorHandler(
        400,
        'Video content cannot be in the "practice" section.'
      );
    }
  }

  const session = await mongoose.startSession();

  try {
    session.startTransaction();

    // No spread, update with raw input directly — Mongo will ignore undefined fields
    const updated = await submoduleContentDao.update(id, data, session);

    await session.commitTransaction();
    return updated;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const removeContentFromSubmoduleService = async (
  submoduleId: string | mongoose.Types.ObjectId,
  contentId: string | mongoose.Types.ObjectId
): Promise<{ success: boolean; message: string }> => {
  const existingSubmodule = await submoduleDao.findById(submoduleId);
  if (!existingSubmodule) {
    throw new ErrorHandler(400, 'Submodule not found');
  }

  const existingSubmoduleContent = await submoduleContentDao.findOne({
    submodule: submoduleId,
    contentId: contentId
  });

  if (!existingSubmoduleContent) {
    throw new ErrorHandler(400, 'Content not found in the submodule');
  }

  let contentDoc = null;
  let pointsToRemove = 0;

  if (
    existingSubmoduleContent.contentType === 'note' &&
    existingSubmoduleContent.contentModel === 'Note'
  ) {
    contentDoc = await noteDao.findById(contentId);
    if (!contentDoc) {
      throw new ErrorHandler(400, 'Note content not found');
    }
    pointsToRemove = -Math.abs(contentDoc.points);
  }

  if (
    existingSubmoduleContent.contentType === 'mcq' &&
    existingSubmoduleContent.contentModel === 'MCQ'
  ) {
    contentDoc = await mcqDao.findById(contentId as string);
    if (!contentDoc) {
      throw new ErrorHandler(400, 'MCQ content not found');
    }
    pointsToRemove = -Math.abs(contentDoc.points);
  }

  if (
    existingSubmoduleContent.contentType === 'video' &&
    existingSubmoduleContent.contentModel === 'Video'
  ) {
    contentDoc = await videosDao.findById(contentId);
    if (!contentDoc) {
      throw new ErrorHandler(400, 'Video content not found');
    }
    pointsToRemove = -Math.abs(contentDoc.points);
  }

  if (
    existingSubmoduleContent.contentType === 'coding_problem' &&
    existingSubmoduleContent.contentModel === 'CodingProblem'
  ) {
    contentDoc = await codingProblemDao.findById(contentId as string);
    if (!contentDoc) {
      throw new ErrorHandler(400, 'CodingProblem content not found');
    }
    pointsToRemove = -Math.abs(contentDoc.points);
  }

  if (!contentDoc) {
    throw new ErrorHandler(400, 'Unsupported content type or model');
  }

  await submoduleContentDao.delete(existingSubmoduleContent._id as string);

  await submodulePointsRefactor(
    existingSubmoduleContent.submodule as string,
    pointsToRemove
  );

  return {
    success: true,
    message: 'Content removed from submodule successfully'
  };
};
