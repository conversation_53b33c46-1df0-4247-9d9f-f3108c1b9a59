import mongoose from 'mongoose';
import { IVideo } from '../../interfaces/video.interface';
import videosDao from '../../dao/videos.dao';
import submoduleContentDao from '../../dao/submodule-content.dao';
import { submodulePointsRefactor } from '../../utils/submodule.utils';
import ErrorHandler from '../../utils/error-handler';
import { canDeleteContent } from '../../utils/content.utils';

export const createVideoService = async (
  data: IVideo
): Promise<IVideo | null> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();
    const newVideo = await videosDao.create(data, session);
    await session.commitTransaction();
    return newVideo;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const updateVideoService = async (
  id: string,
  data: Partial<IVideo>
): Promise<IVideo | null> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();
    const existingVideo = await videosDao.findById(id);
    if (!existingVideo) {
      throw new ErrorHandler(404, 'Video not found');
    }

    // check if the video is already attached to a submodule-content
    // if it is attached to the submodule-content, then we need to update the submodule points
    // calculate the points difference in this
    // the video can be attached to multiple submodule-contents
    // so we need to check if the video is attached to any submodule-content
    // if it is attached to the submodule-content, then we need to update the points in the submodule
    // and also the points will be affected in the module
    // if it is not attached to the submodule-content, then we can update the video directly

    if (data.points && data.points !== existingVideo.points) {
      const submoduleContents = await submoduleContentDao.findAll({
        contentId: existingVideo._id
      });
      if (submoduleContents.length > 0) {
        // calculate the points difference
        const pointsDifference = data.points - existingVideo.points;
        for (const submoduleContent of submoduleContents) {
          await submodulePointsRefactor(
            submoduleContent.submodule as string,
            pointsDifference
          );
        }
      }
    }

    const updatedVideo = await videosDao.update(
      existingVideo._id as string,
      data,
      session
    );
    await session.commitTransaction();
    return updatedVideo;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const deleteVideoService = async (
  id: string
): Promise<IVideo | null> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();
    //check if the video exists

    const existingVideo = await videosDao.findById(id);
    if (!existingVideo) {
      throw new ErrorHandler(404, 'Video not found');
    }

    // check if the video is already attached to a submodule-content
    const checkCanDelete = await canDeleteContent('video', id);

    // if it is attached to the submodule-content, then do not delete the video
    if (!checkCanDelete.canDelete || checkCanDelete.attached) {
      throw new ErrorHandler(403, 'Video is attached to a submodule');
    }

    // if it is not attached to the submodule-content, then delete the video
    const deletedVideo = await videosDao.delete(id, session);

    if (!deletedVideo) {
      throw new ErrorHandler(404, 'Video not found');
    }

    await session.commitTransaction();
    return deletedVideo;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};
