import mongoose, { Types } from 'mongoose';
import { SnapshotService } from './snapshot.service';
import UserProgress from '../../models/user-progress.model';
import { IUserProgress } from '../../interfaces/user-progress.interface';

export class ProgressUpdateWithSnapshotsService {
  /**
   * Update content progress with snapshot creation for data consistency
   * This replaces the existing penalty calculation logic
   */
  static async updateContentProgress(
    userId: string | Types.ObjectId,
    batchId: string | Types.ObjectId,
    subModuleId: string | Types.ObjectId,
    contentId: string | Types.ObjectId,
    contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
    progressData: {
      completed: boolean;
      pointsEarned: number;
      originalPoints: number;
      submissionDate?: Date;
      // Additional content-specific data
      watchedDuration?: number;
      selectedOptions?: string[];
      attempts?: number;
      submissions?: any[];
    }
  ): Promise<{
    success: boolean;
    penaltyApplied: number;
    pointsAfterPenalty: number;
    daysLate: number;
    wasOnTime: boolean;
  }> {
    const userObjId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
    const batchObjId = typeof batchId === 'string' ? new mongoose.Types.ObjectId(batchId) : batchId;
    const subModuleObjId = typeof subModuleId === 'string' ? new mongoose.Types.ObjectId(subModuleId) : subModuleId;
    const contentObjId = typeof contentId === 'string' ? new mongoose.Types.ObjectId(contentId) : contentId;

    const submissionDate = progressData.submissionDate || new Date();

    try {
      // 1. Create submission snapshot (freezes penalty calculation)
      await SnapshotService.createSubmissionSnapshot(
        userObjId,
        batchObjId,
        subModuleObjId,
        contentObjId,
        contentType,
        submissionDate,
        progressData.originalPoints,
        progressData.pointsEarned
      );

      // 2. Get penalty from snapshot (immutable)
      const penaltyData = await SnapshotService.getPenaltyFromSnapshot(
        userObjId,
        contentObjId,
        subModuleObjId
      );

      if (!penaltyData) {
        throw new Error('Failed to get penalty data from snapshot');
      }

      // 3. Update user progress with snapshot-based penalty
      await this.updateUserProgressWithSnapshot(
        userObjId,
        batchObjId,
        subModuleObjId,
        contentObjId,
        contentType,
        {
          ...progressData,
          penaltyApplied: penaltyData.penaltyApplied,
          pointsAfterPenalty: penaltyData.pointsAfterPenalty,
          daysLate: penaltyData.daysLate
        }
      );

      return {
        success: true,
        penaltyApplied: penaltyData.penaltyApplied,
        pointsAfterPenalty: penaltyData.pointsAfterPenalty,
        daysLate: penaltyData.daysLate,
        wasOnTime: penaltyData.wasOnTime
      };
    } catch (error) {
      console.error('Error updating content progress with snapshots:', error);
      return {
        success: false,
        penaltyApplied: 0,
        pointsAfterPenalty: progressData.pointsEarned,
        daysLate: 0,
        wasOnTime: true
      };
    }
  }

  /**
   * Update user progress document with snapshot-based penalty data
   */
  private static async updateUserProgressWithSnapshot(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    subModuleId: mongoose.Types.ObjectId,
    contentId: mongoose.Types.ObjectId,
    contentType: string,
    progressData: {
      completed: boolean;
      pointsEarned: number;
      originalPoints: number;
      penaltyApplied: number;
      pointsAfterPenalty: number;
      daysLate: number;
      watchedDuration?: number;
      selectedOptions?: string[];
      attempts?: number;
      submissions?: any[];
    }
  ): Promise<void> {
    // Find user progress document
    const userProgress = await UserProgress.findOne({
      user: userId,
      batch: batchId
    });

    if (!userProgress) {
      throw new Error('User progress not found');
    }

    // Update content item progress
    const contentItemIndex = userProgress.contentItemProgress.findIndex(
      item => 
        item.contentId.toString() === contentId.toString() &&
        item.subModuleId.toString() === subModuleId.toString()
    );

    const contentItemData = {
      contentType: contentType as any,
      contentId: contentId,
      subModuleId: subModuleId,
      completed: progressData.completed,
      pointsEarned: progressData.pointsAfterPenalty, // Use penalty-adjusted points
      completedAt: progressData.completed ? new Date() : undefined,
      originalPoints: progressData.originalPoints,
      penaltyApplied: progressData.penaltyApplied,
      daysLate: progressData.daysLate,
      // Content-specific fields
      watchedDuration: progressData.watchedDuration,
      selectedOptions: progressData.selectedOptions,
      attempts: progressData.attempts,
      submissions: progressData.submissions
    };

    if (contentItemIndex >= 0) {
      // Update existing content item
      userProgress.contentItemProgress[contentItemIndex] = {
        ...userProgress.contentItemProgress[contentItemIndex],
        ...contentItemData
      };
    } else {
      // Add new content item
      userProgress.contentItemProgress.push(contentItemData as any);
    }

    // Update submodule progress
    await this.updateSubmoduleProgressWithSnapshot(userProgress, subModuleId.toString());

    // Save the document
    await userProgress.save();
  }

  /**
   * Update submodule progress using snapshot-based penalties
   */
  private static async updateSubmoduleProgressWithSnapshot(
    userProgress: IUserProgress,
    subModuleId: string
  ): Promise<void> {
    // Get all content items for this submodule
    const submoduleContentItems = userProgress.contentItemProgress.filter(
      item => item.subModuleId.toString() === subModuleId
    );

    if (submoduleContentItems.length === 0) {
      return;
    }

    // Calculate progress and points (using snapshot-based penalties)
    const completedItems = submoduleContentItems.filter(item => item.completed);
    const progressPercentage = (completedItems.length / submoduleContentItems.length) * 100;
    
    // Sum points after penalties (from snapshots)
    const totalPointsEarned = submoduleContentItems.reduce(
      (sum, item) => sum + (item.pointsEarned || 0),
      0
    );

    // Get maximum penalty applied and days late (from snapshots)
    const maxPenaltyApplied = submoduleContentItems.reduce(
      (max, item) => Math.max(max, item.penaltyApplied || 0),
      0
    );

    const maxDaysLate = submoduleContentItems.reduce(
      (max, item) => Math.max(max, item.daysLate || 0),
      0
    );

    // Check if submodule is completed (80% threshold)
    const isCompleted = progressPercentage >= 80;

    // Update submodule progress
    const submoduleProgressIndex = userProgress.subModuleProgress.findIndex(
      item => item.subModule.toString() === subModuleId
    );

    const submoduleProgressData = {
      subModule: new Types.ObjectId(subModuleId),
      progress: Math.round(progressPercentage),
      completed: isCompleted,
      pointsEarned: totalPointsEarned,
      penaltyApplied: maxPenaltyApplied,
      daysLate: maxDaysLate,
      completedAt: isCompleted ? new Date() : undefined
    };

    if (submoduleProgressIndex >= 0) {
      // Update existing submodule progress
      userProgress.subModuleProgress[submoduleProgressIndex] = {
        ...userProgress.subModuleProgress[submoduleProgressIndex],
        ...submoduleProgressData
      };
    } else {
      // Add new submodule progress
      userProgress.subModuleProgress.push(submoduleProgressData as any);
    }
  }

  /**
   * Validate submodule unlock using snapshot-based position data
   * This ensures unlock logic remains consistent even after reordering
   */
  static async validateSubmoduleUnlockWithSnapshot(
    userId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId
  ): Promise<{
    isUnlocked: boolean;
    reason: string;
    basedOnSnapshot: boolean;
  }> {
    try {
      // First check if we have snapshot data for this user-submodule
      const snapshotUnlock = await SnapshotService.validateUnlockFromSnapshot(
        userId,
        submoduleId
      );

      if (snapshotUnlock) {
        return {
          isUnlocked: snapshotUnlock.wasUnlocked,
          reason: snapshotUnlock.unlockReason,
          basedOnSnapshot: true
        };
      }

      // Fallback to current unlock logic if no snapshot exists
      // This would use the existing isSubmoduleUnlocked function
      return {
        isUnlocked: false,
        reason: 'No snapshot data available, using current structure',
        basedOnSnapshot: false
      };
    } catch (error) {
      console.error('Error validating submodule unlock with snapshot:', error);
      return {
        isUnlocked: false,
        reason: 'Error validating unlock status',
        basedOnSnapshot: false
      };
    }
  }

  /**
   * Get penalty data for existing submissions (immutable from snapshots)
   */
  static async getSubmissionPenalty(
    userId: mongoose.Types.ObjectId,
    contentId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId
  ) {
    return await SnapshotService.getPenaltyFromSnapshot(userId, contentId, submoduleId);
  }

  /**
   * Recalculate leaderboard using snapshot-based penalties
   * This ensures leaderboard consistency even after deadline changes
   */
  static async recalculateLeaderboardWithSnapshots(batchId: mongoose.Types.ObjectId) {
    try {
      // Get all user progress for this batch
      const userProgressList = await UserProgress.find({ batch: batchId });

      const leaderboardData = [];

      for (const userProgress of userProgressList) {
        let totalSnapshotPoints = 0;

        // Calculate total points using snapshot data
        for (const contentItem of userProgress.contentItemProgress) {
          const snapshotPenalty = await SnapshotService.getPenaltyFromSnapshot(
            userProgress.user,
            contentItem.contentId,
            contentItem.subModuleId as mongoose.Types.ObjectId
          );

          if (snapshotPenalty) {
            totalSnapshotPoints += snapshotPenalty.pointsAfterPenalty;
          } else {
            // Fallback to current points if no snapshot
            totalSnapshotPoints += contentItem.pointsEarned || 0;
          }
        }

        leaderboardData.push({
          userId: userProgress.user,
          totalPoints: totalSnapshotPoints,
          basedOnSnapshots: true
        });
      }

      // Sort by points (descending)
      leaderboardData.sort((a, b) => b.totalPoints - a.totalPoints);

      return leaderboardData;
    } catch (error) {
      console.error('Error recalculating leaderboard with snapshots:', error);
      return [];
    }
  }
}
