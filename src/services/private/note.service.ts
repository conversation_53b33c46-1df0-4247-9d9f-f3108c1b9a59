import { INote } from '../../interfaces/note.interface';
import noteDao from '../../dao/note.dao';
import mongoose from 'mongoose';
import { canDeleteContent, updatePoints } from '../../utils/content.utils';
import <PERSON>rror<PERSON>and<PERSON> from '../../utils/error-handler';
import { uploadBase64File } from '../../utils/imagekit-uploader';

export const createNoteService = async (
  data: Partial<INote>
): Promise<INote> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();

    // Check if the note contains an images array
    if (data.images && Array.isArray(data.images)) {
      const uploadedImageUrls = await Promise.all(
        data.images.map(async image => {
          if (typeof image === 'string' && image.startsWith('data:image')) {
            // If the image is in base64 format, upload it to ImageKit
            const fileName = `note_${Date.now()}`; // Generate a unique file name
            const uploadedUrl = await uploadBase64File(image, fileName);

            if (!uploadedUrl) {
              throw new ErrorHandler(400, 'Image upload failed');
            }
            return uploadedUrl;
          }
          // If the image is not in base64 format, assume it's a valid URL
          return image;
        })
      );

      // Replace the images array with the uploaded URLs
      data.images = uploadedImageUrls;
    }

    const newNote = await noteDao.create(data, session);
    await session.commitTransaction();
    return newNote;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const updateNoteService = async (
  id: string,
  data: Partial<INote>
): Promise<INote | null> => {
  const session = await mongoose.startSession();
  session.startTransaction();
  try {
    const existingNote = await noteDao.findById(id);
    if (!existingNote) {
      throw new ErrorHandler(404, 'Note not found');
    }
    if (data.points) {
      await updatePoints('note', id, data.points, existingNote.points);
    }
    const updatedNote = await noteDao.update(id, data, session);
    await session.commitTransaction();
    return updatedNote;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const deleteNoteService = async (id: string): Promise<INote | null> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();
    const checkCanDelete = await canDeleteContent('note', id);
    if (!checkCanDelete) {
      throw new ErrorHandler(403, 'This note cannot be deleted');
    }

    if (!checkCanDelete.canDelete || checkCanDelete.attached) {
      throw new ErrorHandler(403, 'Note is attached to a submodule');
    }
    const deletedNote = await noteDao.delete(id);
    if (!deletedNote) {
      throw new ErrorHandler(404, 'Note not found');
    }
    await session.commitTransaction();
    return deletedNote;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};
