import mongoose from 'mongoose';
import SubmissionSnapshot from '../../models/submission-snapshot.model';
import {
  CourseStructureVersion,
  BatchDeadlineVersion,
  UserAdjustmentVersion
} from '../../models/course-structure-version.model';
import { DeadlineCalculator } from '../../utils/deadline-calculator.util';
import BatchSubmoduleDeadline from '../../models/batch-submodule-deadline.model';
import UserDeadlineAdjustment from '../../models/user-deadline-adjustment.model';
import { getOrderedSubmodulesFromPoint } from '../../utils/submodule-deadline.util';
import { isSubmoduleUnlocked } from '../../utils/submodule-access.util';
import moduleDao from '../../dao/module.dao';
import submoduleDao from '../../dao/submodule.dao';

export class SnapshotService {
  /**
   * Create a submission snapshot that freezes the state at submission time
   * This ensures penalty calculations remain consistent even if deadlines change later
   */
  static async createSubmissionSnapshot(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId,
    contentId: mongoose.Types.ObjectId,
    contentType: string,
    submissionDate: Date,
    originalPoints: number,
    earnedPoints: number
  ): Promise<void> {
    try {
      // 1. Get current deadline state
      const deadlineSnapshot = await this.captureDeadlineSnapshot(
        userId,
        batchId,
        submoduleId,
        submissionDate
      );

      // 2. Get current course structure state
      const positionSnapshot = await this.capturePositionSnapshot(
        batchId,
        submoduleId
      );

      // 3. Calculate penalty based on current state (frozen)
      const penaltyCalculation = await this.calculatePenaltySnapshot(
        userId,
        batchId,
        submoduleId,
        submissionDate,
        originalPoints,
        earnedPoints
      );

      // 4. Validate unlock status based on current state (frozen)
      const unlockValidation = await this.validateUnlockSnapshot(
        userId,
        batchId,
        submoduleId,
        positionSnapshot.courseStructureVersion
      );

      // 5. Create the snapshot
      await SubmissionSnapshot.createSnapshot(
        userId,
        batchId,
        submoduleId,
        contentId,
        contentType,
        {
          submissionDate,
          originalPoints,
          earnedPoints,
          deadlineSnapshot,
          positionSnapshot,
          penaltyCalculation,
          unlockValidation
        }
      );

      console.log(
        `Submission snapshot created for user ${userId}, content ${contentId}`
      );
    } catch (error) {
      console.error('Error creating submission snapshot:', error);
      throw error;
    }
  }

  /**
   * Capture the current deadline state for a user-batch-submodule combination
   */
  private static async captureDeadlineSnapshot(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId,
    submissionDate: Date
  ) {
    // Get computed deadline using the new system
    const computedDeadline = await DeadlineCalculator.getUserSubmoduleDeadline(
      userId,
      batchId,
      submoduleId
    );

    if (!computedDeadline) {
      throw new Error('No deadline found for user-batch-submodule combination');
    }

    // Get batch deadline for penalty rules
    const batchDeadline = await BatchSubmoduleDeadline.findOne({
      batch: batchId,
      submodule: submoduleId,
      isActive: true
    }).lean();

    // Get current version numbers
    const batchDeadlineVersion = await this.getCurrentBatchDeadlineVersion(
      batchId,
      submoduleId
    );
    const userAdjustmentVersion = await this.getCurrentUserAdjustmentVersion(
      userId,
      batchId
    );

    return {
      originalDeadline: computedDeadline.originalDeadline,
      adjustedDeadline: computedDeadline.deadline,
      startDate: computedDeadline.startDate,
      penaltyRules: batchDeadline?.penaltyRules || [],
      batchDeadlineVersion,
      userAdjustmentVersion
    };
  }

  /**
   * Capture the current course structure position for a submodule
   */
  private static async capturePositionSnapshot(
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId
  ) {
    // Get submodule details
    const submodule = await submoduleDao.findById(
      submoduleId.toString(),
      'module'
    );
    if (!submodule) {
      throw new Error('Submodule not found');
    }

    // Get module details
    const module = await moduleDao.findById(submodule.module.toString());
    if (!module) {
      throw new Error('Module not found');
    }

    // Get ordered submodules to determine global position
    const orderedSubmodules = await getOrderedSubmodulesFromPoint(
      module.course
    );
    const globalPosition =
      orderedSubmodules.findIndex(
        sm => sm._id.toString() === submoduleId.toString()
      ) + 1;

    // Find previous and next submodules
    const currentIndex = globalPosition - 1;
    const previousSubmoduleId =
      currentIndex > 0 ? orderedSubmodules[currentIndex - 1]._id : undefined;
    const nextSubmoduleId =
      currentIndex < orderedSubmodules.length - 1
        ? orderedSubmodules[currentIndex + 1]._id
        : undefined;

    // Get current course structure version
    const courseStructureVersion = await this.getCurrentCourseStructureVersion(
      module.course,
      batchId
    );

    return {
      moduleId: submodule.module,
      moduleOrder: module.order,
      submoduleOrder: submodule.order,
      globalPosition,
      previousSubmoduleId,
      nextSubmoduleId,
      courseStructureVersion
    };
  }

  /**
   * Calculate penalty based on current deadline state
   */
  private static async calculatePenaltySnapshot(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId,
    submissionDate: Date,
    originalPoints: number,
    earnedPoints: number
  ) {
    // Get computed deadline
    const computedDeadline = await DeadlineCalculator.getUserSubmoduleDeadline(
      userId,
      batchId,
      submoduleId
    );

    if (!computedDeadline) {
      return {
        daysLate: 0,
        penaltyPercentage: 0,
        penaltyApplied: 0,
        pointsAfterPenalty: earnedPoints,
        wasOnTime: true
      };
    }

    // Calculate days late
    const deadline = new Date(computedDeadline.deadline);
    deadline.setHours(23, 59, 59, 999); // Set to end of day

    const msPerDay = 1000 * 60 * 60 * 24;
    const daysLate = Math.max(
      0,
      Math.ceil((submissionDate.getTime() - deadline.getTime()) / msPerDay)
    );

    const wasOnTime = daysLate <= 0;

    if (wasOnTime) {
      return {
        daysLate: 0,
        penaltyPercentage: 0,
        penaltyApplied: 0,
        pointsAfterPenalty: earnedPoints,
        wasOnTime: true
      };
    }

    // Get penalty rules from batch deadline
    const batchDeadline = await BatchSubmoduleDeadline.findOne({
      batch: batchId,
      submodule: submoduleId,
      isActive: true
    }).lean();

    if (!batchDeadline?.penaltyRules?.length) {
      return {
        daysLate,
        penaltyPercentage: 0,
        penaltyApplied: 0,
        pointsAfterPenalty: earnedPoints,
        wasOnTime: false
      };
    }

    // Find applicable penalty rule
    const applicableRule = batchDeadline.penaltyRules
      .filter(rule => daysLate >= rule.daysLate)
      .sort((a, b) => b.daysLate - a.daysLate)[0];

    if (!applicableRule) {
      return {
        daysLate,
        penaltyPercentage: 0,
        penaltyApplied: 0,
        pointsAfterPenalty: earnedPoints,
        wasOnTime: false
      };
    }

    const penaltyPercentage = applicableRule.penaltyPercentage;
    const penaltyApplied = (originalPoints * penaltyPercentage) / 100;
    const pointsAfterPenalty = Math.max(0, earnedPoints - penaltyApplied);

    return {
      daysLate,
      penaltyPercentage,
      penaltyApplied,
      pointsAfterPenalty,
      wasOnTime: false
    };
  }

  /**
   * Validate unlock status based on current course structure
   */
  private static async validateUnlockSnapshot(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId,
    courseStructureVersion: number
  ) {
    try {
      // Get submodule details to find course
      const submodule = await submoduleDao.findById(
        submoduleId.toString(),
        'module'
      );
      if (!submodule) {
        return {
          wasUnlocked: false,
          unlockReason: 'Submodule not found',
          prerequisitesMet: false
        };
      }

      const module = await moduleDao.findById(submodule.module.toString());
      if (!module) {
        return {
          wasUnlocked: false,
          unlockReason: 'Module not found',
          prerequisitesMet: false
        };
      }

      // Check unlock status using existing utility
      const unlockResult = await isSubmoduleUnlocked(
        userId,
        batchId,
        module.course,
        submoduleId
      );

      return {
        wasUnlocked: unlockResult.unlocked,
        unlockReason: unlockResult.reason || 'Unlocked',
        prerequisitesMet: unlockResult.unlocked,
        previousSubmoduleCompleted: unlockResult.unlocked, // Simplified for now
        dateRequirementMet: unlockResult.unlocked // Simplified for now
      };
    } catch (error) {
      console.error('Error validating unlock status:', error);
      return {
        wasUnlocked: false,
        unlockReason: 'Error validating unlock status',
        prerequisitesMet: false
      };
    }
  }

  /**
   * Get current batch deadline version number
   */
  private static async getCurrentBatchDeadlineVersion(
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId
  ): Promise<number> {
    const latestVersion = await BatchDeadlineVersion.findOne({
      batch: batchId,
      submodule: submoduleId
    })
      .sort({ version: -1 })
      .lean();

    return latestVersion ? latestVersion.version : 1;
  }

  /**
   * Get current user adjustment version number
   */
  private static async getCurrentUserAdjustmentVersion(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId
  ): Promise<number> {
    const latestVersion = await UserAdjustmentVersion.findOne({
      user: userId,
      batch: batchId
    })
      .sort({ version: -1 })
      .lean();

    return latestVersion ? latestVersion.version : 1;
  }

  /**
   * Get current course structure version number
   */
  private static async getCurrentCourseStructureVersion(
    courseId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId
  ): Promise<number> {
    const latestVersion = await CourseStructureVersion.findOne({
      course: courseId,
      batch: batchId
    })
      .sort({ version: -1 })
      .lean();

    return latestVersion ? latestVersion.version : 1;
  }

  /**
   * Get penalty from snapshot (immutable - never changes after submission)
   */
  static async getPenaltyFromSnapshot(
    userId: mongoose.Types.ObjectId,
    contentId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId
  ) {
    return await SubmissionSnapshot.getSnapshotPenalty(
      userId,
      contentId,
      submoduleId
    );
  }

  /**
   * Validate unlock from snapshot (immutable - based on structure at submission time)
   */
  static async validateUnlockFromSnapshot(
    userId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId
  ) {
    return await SubmissionSnapshot.validateUnlockFromSnapshot(
      userId,
      submoduleId
    );
  }

  /**
   * Create version when course structure changes
   */
  static async createCourseStructureVersion(
    courseId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    changeType: string,
    changedBy: mongoose.Types.ObjectId,
    changeDetails: any,
    changeReason?: string
  ) {
    try {
      // Capture current course structure
      const structure = await this.captureCourseStructure(courseId);

      await CourseStructureVersion.createVersion(
        courseId,
        batchId,
        structure,
        changeType,
        changedBy,
        changeDetails,
        changeReason
      );

      console.log(
        `Course structure version created for course ${courseId}, batch ${batchId}`
      );
    } catch (error) {
      console.error('Error creating course structure version:', error);
      throw error;
    }
  }

  /**
   * Create version when batch deadline changes
   */
  static async createBatchDeadlineVersion(
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId,
    changeType: string,
    changedBy: mongoose.Types.ObjectId,
    changeReason?: string,
    previousDeadline?: Date,
    newDeadline?: Date
  ) {
    try {
      // Capture current deadline configuration
      const deadlineConfig = await this.captureBatchDeadlineConfig(
        batchId,
        submoduleId
      );

      await BatchDeadlineVersion.createVersion(
        batchId,
        submoduleId,
        deadlineConfig,
        changeType,
        changedBy,
        changeReason,
        previousDeadline,
        newDeadline
      );

      console.log(
        `Batch deadline version created for batch ${batchId}, submodule ${submoduleId}`
      );
    } catch (error) {
      console.error('Error creating batch deadline version:', error);
      throw error;
    }
  }

  /**
   * Create version when user adjustments change
   */
  static async createUserAdjustmentVersion(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    changeType: string,
    changeReason?: string
  ) {
    try {
      // Capture current user adjustment configuration
      const adjustmentConfig = await this.captureUserAdjustmentConfig(
        userId,
        batchId
      );

      await UserAdjustmentVersion.createVersion(
        userId,
        batchId,
        adjustmentConfig,
        changeType,
        changeReason
      );

      console.log(
        `User adjustment version created for user ${userId}, batch ${batchId}`
      );
    } catch (error) {
      console.error('Error creating user adjustment version:', error);
      throw error;
    }
  }

  /**
   * Capture current course structure
   */
  private static async captureCourseStructure(
    courseId: mongoose.Types.ObjectId
  ) {
    const modules = await moduleDao.findAll({ course: courseId }, { order: 1 });

    const structure = {
      modules: [],
      totalSubmodules: 0
    };

    let globalPosition = 1;

    for (const module of modules) {
      const submodules = await submoduleDao.findAllByModule(
        module._id.toString()
      );

      const moduleStructure = {
        moduleId: module._id,
        order: module.order,
        title: module.title,
        submodules: submodules.map(submodule => ({
          submoduleId: submodule._id,
          order: submodule.order,
          title: submodule.title,
          globalPosition: globalPosition++,
          dependsOnPrevious: true // Default value, can be customized
        }))
      };

      structure.modules.push(moduleStructure);
      structure.totalSubmodules += submodules.length;
    }

    return structure;
  }

  /**
   * Capture current batch deadline configuration
   */
  private static async captureBatchDeadlineConfig(
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId
  ) {
    const batchDeadline = await BatchSubmoduleDeadline.findOne({
      batch: batchId,
      submodule: submoduleId,
      isActive: true
    }).lean();

    if (!batchDeadline) {
      throw new Error('Batch deadline not found');
    }

    return {
      startDate: batchDeadline.startDate,
      deadline: batchDeadline.deadline,
      dependsOnPreviousSubmodule: batchDeadline.dependsOnPreviousSubmodule,
      penaltyRules: batchDeadline.penaltyRules || []
    };
  }

  /**
   * Capture current user adjustment configuration
   */
  private static async captureUserAdjustmentConfig(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId
  ) {
    const userAdjustment = await UserDeadlineAdjustment.findOne({
      user: userId,
      batch: batchId,
      isActive: true
    }).lean();

    if (!userAdjustment) {
      return {
        totalPauseDays: 0,
        pauseHistory: [],
        submoduleAdjustments: []
      };
    }

    return {
      totalPauseDays: userAdjustment.totalPauseDays,
      pauseHistory: userAdjustment.pauseHistory,
      submoduleAdjustments: userAdjustment.submoduleAdjustments
    };
  }
}
