import { IErrorLog } from '../../interfaces/error-log.interface';
import { ErrorLogDao } from '../../dao/error-log.dao';

const errorLogDao = new ErrorLogDao();

export const getErrorLogsService = async (
  pagination: { page: number; limit: number; skip: number },
  dbQuery: {
    filter: Record<string, string | number | boolean | object>;
    sort: Record<string, 1 | -1>;
  } = {
    filter: {},
    sort: { createdAt: -1 }
  }
) => {
  const { limit, skip } = pagination;
  const { filter, sort } = dbQuery;

  // Run both queries in parallel for better performance
  const [errorLogs, totalCount] = await Promise.all([
    errorLogDao.findAll(filter, sort, skip, limit),
    errorLogDao.count(filter)
  ]);

  return {
    errorLogs,
    pagination: {
      currentPage: pagination.page,
      itemsPerPage: pagination.limit,
      totalItems: totalCount,
      totalPages: Math.ceil(totalCount / pagination.limit)
    }
  };
};

export const getErrorLogByIdService = async (
  id: string
): Promise<IErrorLog | null> => {
  return await errorLogDao.findById(id);
};
