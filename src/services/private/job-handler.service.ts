import { Job } from 'bullmq';
import { IJobData } from '../../interfaces/job-data.interface';
import EnrolledCourseLinkDao from '../../dao/enrolled-course.dao';
import mongoose from 'mongoose';
import logger from '../../config/logger';
import { IEnrolledCourseLinkStatus } from '../../interfaces/enrolled-course-link.interface';
import UserDeadline from '../../models/user-deadline.model';
import UserProgress from '../../models/user-progress.model';
import leaderboardDao from '../../dao/leaderboard.dao';
import { getPeriodDates } from '../public/leaderboard.service';
import <PERSON>rrorHandler from '../../utils/error-handler';
import { config } from '../../config/config';
import axios from 'axios';
import { ILeaderboard } from '../../interfaces/leaderboard.interface';
import userProgressDao from '../../dao/user-progress.dao';
/**
 * Initializes user deadlines for a newly enrolled user
 */
export async function initializeUserDeadlines(
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  session: mongoose.ClientSession,
  jobId: string,
  enrollmentId: string
): Promise<boolean> {
  try {
    // Check if deadlines already exist for this user and batch
    const existing = await UserDeadline.findOne({
      user: userId,
      batch: batchId
    }).session(session);
    if (existing) {
      logger.info('User deadlines already initialized', {
        jobId,
        enrollmentId,
        userId: userId.toString(),
        batchId: batchId.toString()
      });
      return true;
    }
    const deadlinesCount = await UserDeadline.initializeUserDeadlines(
      userId,
      batchId,
      session
    );
    logger.info('Initialized user deadlines', {
      jobId,
      enrollmentId,
      deadlinesCount
    });
    return true;
  } catch (error) {
    logger.error('Failed to initialize user deadlines', {
      jobId,
      enrollmentId,
      error
    });
    // Do not throw, just skip
    return true;
  }
}

/**
 * Initializes user progress tracking for a newly enrolled user
 */
async function initializeUserProgress(
  userId: mongoose.Types.ObjectId,
  courseId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  session: mongoose.ClientSession,
  jobId: string,
  enrollmentId: string
): Promise<boolean> {
  try {
    // Check if progress record already exists
    const existingProgress = await UserProgress.findOne({
      user: userId,
      course: courseId,
      batch: batchId
    }).session(session);
    if (existingProgress) {
      logger.info('User progress record already exists', {
        jobId,
        enrollmentId,
        progressId: existingProgress._id
      });
      return true;
    }
    // Create new progress record
    const userProgress = await UserProgress.create(
      [
        {
          user: userId,
          course: courseId,
          batch: batchId,
          overallProgress: 0,
          totalPointsEarned: 0,
          moduleProgress: [],
          subModuleProgress: [],
          contentProgress: [],
          assessmentProgress: [],
          lastActivityAt: new Date()
        }
      ],
      { session }
    );
    logger.info('Created user progress record', {
      jobId,
      enrollmentId,
      progressId: userProgress[0]._id
    });
    return true;
  } catch (error) {
    logger.error('Failed to initialize user progress', {
      jobId,
      enrollmentId,
      error
    });
    // Do not throw, just skip
    return true;
  }
}

// Helper to add a user to a leaderboard if not already present
async function addUserToLeaderboardIfNeeded(
  leaderboard: ILeaderboard,
  userId: mongoose.Types.ObjectId,
  userName: string,
  userAvatar: string,
  userStreak: number = 0
): Promise<void> {
  const alreadyRanked =
    Array.isArray(leaderboard.rankings) &&
    leaderboard.rankings.some(r => String(r.user) === String(userId));
  const userProgress = await userProgressDao.findOne(userId);
  if (!alreadyRanked) {
    leaderboard.rankings.push({
      user: userId,
      userName,
      userAvatar,
      rank: leaderboard.rankings.length + 1,
      points: userProgress?.totalPointsEarned || 0,
      progress: userProgress?.overallProgress || 0,
      streak: userStreak
    });
    await leaderboardDao.updateRankings(
      String(leaderboard._id),
      leaderboard.rankings
    );
  }
}

/**
 * Initializes or updates leaderboard for a newly enrolled user
 */

export async function initializeUserLeaderboard(
  userId: mongoose.Types.ObjectId,
  courseId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  session: mongoose.ClientSession | undefined,
  jobId: string,
  enrollmentId: string,
  userStreak: number = 0
): Promise<boolean> {
  try {
    let response;
    try {
      response = await axios.get(
        `${config.prePurchaseUrl}/api/v3/server/users/${String(userId)}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'server-api-key': config.serverApiKey
          },
          timeout: 5000
        }
      );
    } catch (error) {
      console.warn('Error fetching user details from external API', error);
    }

    const userData = response?.data as unknown;
    const user =
      userData &&
      typeof userData === 'object' &&
      'data' in userData &&
      userData.data &&
      typeof userData.data === 'object' &&
      'user' in userData.data
        ? ((userData.data as { user: unknown }).user as Record<string, unknown>)
        : undefined;
    if (!user) {
      logger.error('User not found for leaderboard initialization', {
        jobId,
        enrollmentId,
        userId: userId.toString()
      });
      throw new ErrorHandler(404, 'User not found');
    }
    const userName =
      typeof user.name === 'object' &&
      user.name &&
      'firstName' in user.name &&
      'lastName' in user.name
        ? `${(user.name as Record<string, string>).firstName} ${(user.name as Record<string, string>).lastName}`
        : '';
    const userAvatar = typeof user.avatar === 'string' ? user.avatar : '';

    // --- OVERALL LEADERBOARD ---
    let overallLeaderboard;
    const userProgress = await userProgressDao.findOne(userId);
    try {
      overallLeaderboard = await leaderboardDao.findOne({
        course: courseId,
        batch: batchId,
        period: 'overall'
      });
      if (!overallLeaderboard) {
        const { startDate, endDate } = getPeriodDates('overall');
        overallLeaderboard = await leaderboardDao.create(
          {
            course: courseId,
            batch: batchId,
            period: 'overall',
            startDate,
            endDate,
            rankings: [
              {
                user: userId,
                userName,
                userAvatar,
                rank: 1,
                points: userProgress?.totalPointsEarned || 0,
                progress: userProgress?.overallProgress || 0,
                streak: userStreak
              }
            ]
          },
          session
        );
      }
    } catch (error) {
      // Type guard for MongoError with code property
      function isDuplicateKeyError(err: unknown): err is { code: number } {
        return (
          typeof err === 'object' &&
          err !== null &&
          'code' in err &&
          typeof (err as { code: unknown }).code === 'number' &&
          (err as { code: number }).code === 11000
        );
      }
      if (isDuplicateKeyError(error)) {
        // Duplicate key error: fetch the existing leaderboard
        overallLeaderboard = await leaderboardDao.findOne({
          course: courseId,
          batch: batchId,
          period: 'overall'
        });
      } else {
        throw error;
      }
    }
    if (overallLeaderboard) {
      await addUserToLeaderboardIfNeeded(
        overallLeaderboard,
        userId,
        userName,
        userAvatar
      );
    }

    // --- DAILY LEADERBOARD ---
    {
      const period = 'daily';
      const { startDate, endDate } = getPeriodDates(period);
      // Always get all users from the overall leaderboard
      const overallUsers =
        overallLeaderboard && Array.isArray(overallLeaderboard.rankings)
          ? overallLeaderboard.rankings.map(r => ({
              user: r.user,
              userName: r.userName,
              userAvatar: r.userAvatar || '',
              streak: r.streak || 0,
              points: 0,
              progress: 0
            }))
          : [];
      let leaderboard = await leaderboardDao.findOne({
        course: courseId,
        batch: batchId,
        period,
        startDate
      });
      if (!leaderboard) {
        leaderboard = await leaderboardDao.create(
          {
            course: courseId,
            batch: batchId,
            period,
            startDate,
            endDate,
            rankings: overallUsers.map(
              (
                u: {
                  user: mongoose.Types.ObjectId;
                  userName: string | undefined;
                  userAvatar: string;
                  points: number;
                  progress: number;
                  streak: number;
                },
                idx: number
              ) => ({
                user: u.user,
                userName: u.userName ?? '',
                userAvatar: u.userAvatar,
                rank: idx + 1,
                points: u.points || 0,
                progress: u.progress || 0,
                streak: u.streak || 0
              })
            )
          },
          session
        );
      } else if (leaderboard && Array.isArray(leaderboard.rankings)) {
        // Add any missing users from overall leaderboard to daily leaderboard
        const existingUserIds = new Set(
          leaderboard.rankings.map((r: { user: mongoose.Types.ObjectId }) =>
            String(r.user)
          )
        );
        let changed = false;
        for (const u of overallUsers) {
          if (!existingUserIds.has(String(u.user))) {
            leaderboard.rankings.push({
              user: u.user,
              userName: u.userName,
              userAvatar: u.userAvatar,
              rank: leaderboard.rankings.length + 1,
              points: u.points || 0,
              progress: u.progress || 0,
              streak: u.streak || 0
            });
            changed = true;
          }
        }
        if (changed) {
          await leaderboardDao.updateRankings(
            String(leaderboard._id),
            leaderboard.rankings
          );
        }
      }
    }

    logger.info('Initialized user leaderboard', {
      jobId,
      enrollmentId,
      userId: userId.toString()
    });
    return true;
  } catch (error) {
    logger.error('Failed to initialize user leaderboard', {
      jobId,
      enrollmentId,
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
}

/**
 * Handles post-purchase job processing
 * This function is designed to be idempotent - it can be safely retried
 */
export const handlePostPurchaseJob = async (
  job: Job<IJobData>
): Promise<boolean> => {
  const { userId, courseId, enrollmentId, batchId } = job.data;
  const jobId = job.id || 'unknown';

  // Create ObjectIds once to avoid repetition
  const userObjId = new mongoose.Types.ObjectId(userId);
  const courseObjId = new mongoose.Types.ObjectId(courseId);
  const batchObjId = new mongoose.Types.ObjectId(batchId);
  const enrollmentObjId = new mongoose.Types.ObjectId(enrollmentId);

  logger.info('Starting job processing', { jobId, enrollmentId });

  // Start a session for transaction
  const session = await mongoose.startSession();

  try {
    let result = false;

    // Start transaction
    await session.withTransaction(async () => {
      // First check if this enrollment was already processed (idempotency check)
      const existingEnrollment = await EnrolledCourseLinkDao.findOneWithSession(
        { enrollmentId: enrollmentObjId },
        session
      );

      if (existingEnrollment) {
        logger.info(
          'Enrollment already processed, skipping duplicate creation',
          {
            jobId,
            enrollmentId,
            existingId: existingEnrollment._id
          }
        );
        result = true;
        return; // Exit the transaction callback
      }

      // Create enrollment record
      const enrolledCourseLinkDoc =
        await EnrolledCourseLinkDao.createWithSession(
          {
            userId: userObjId,
            courseId: courseObjId,
            enrollmentId: enrollmentObjId,
            courseTitle: job.data.courseTitle,
            batchId: batchObjId,
            purchaseDate: job.data.purchaseTimestamp,
            status: IEnrolledCourseLinkStatus.ACTIVE,
            discord: job.data.discordAccess
          },
          session
        );

      if (!enrolledCourseLinkDoc || enrolledCourseLinkDoc === null) {
        logger.error('Failed to create EnrolledCourseLink document', {
          jobId,
          enrollmentId,
          jobData: job.data
        });
        result = false;
        return; // Exit the transaction callback
      }

      logger.info('Created enrollment record', {
        jobId,
        enrollmentId,
        enrollmentDocId: enrolledCourseLinkDoc._id
      });

      // Initialize all required user entities - one after another for safety
      await initializeUserDeadlines(
        userObjId,
        batchObjId,
        session,
        jobId,
        enrollmentId
      );
      await initializeUserProgress(
        userObjId,
        courseObjId,
        batchObjId,
        session,
        jobId,
        enrollmentId
      );
      await initializeUserLeaderboard(
        userObjId,
        courseObjId,
        batchObjId,
        session,
        jobId,
        enrollmentId
      );

      logger.info(
        'Successfully completed all enrollment initialization steps',
        {
          jobId,
          enrollmentId
        }
      );

      result = true;
    });

    return result;
  } catch (error) {
    // Capture detailed error information
    logger.error(
      'Error in handlePostPurchaseJob',
      {
        jobId,
        enrollmentId,
        error,
        stack: error instanceof Error ? error.stack : undefined,
        attempt: job.attemptsMade + 1
      },
      {
        // Increase transaction timeout from default 120 seconds
        readPreference: 'primary',
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
        maxCommitTimeMS: 300000 // 5 minutes
      }
    );

    // Handle specific error cases differently
    if (error instanceof mongoose.Error.ValidationError) {
      // Data validation errors should not be retried as they're likely to fail again
      logger.error('Validation error in enrollment creation - will not retry', {
        jobId,
        enrollmentId,
        validationErrors: error.errors
      });
    } else if (
      error instanceof Error &&
      error.name === 'MongoServerError' &&
      (error as unknown as { code: number }).code === 11000
    ) {
      // Duplicate key error - the record was likely already created
      logger.warn('Duplicate record detected, considering job successful', {
        jobId,
        enrollmentId
      });
      return true; // Treat as success for idempotency
    }

    // For other errors, let the worker's retry mechanism handle it
    throw error;
  } finally {
    // Always end the session
    await session.endSession();
  }
};
