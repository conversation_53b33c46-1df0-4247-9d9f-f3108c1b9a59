import enrolledCourseDao from '../../dao/enrolled-course.dao';
import { IEnrolledCourseLink } from '../../interfaces/enrolled-course-link.interface';
import { ActionTypes } from '../logger.service';

export const getAllEnrolledCoursesLinksService = async (
  userId: string
): Promise<IEnrolledCourseLink[] | null> => {
  return await enrolledCourseDao.findAll({ userId });
};

export const getEnrolledCourseLinkByIdService = async (
  userId: string,
  courseId: string
): Promise<IEnrolledCourseLink | null> => {
  return await enrolledCourseDao.findOne({ userId, courseId });
};
export const getEnrolledCourseLinkByIdServiceIsPaid = async (
  userId: string,
  courseId: string,
  batchId: string
): Promise<IEnrolledCourseLink | null> => {
  const activeUserCourse = await enrolledCourseDao.findOne({
    userId,
    courseId,
    batchId,
    status: ActionTypes.USER.STATUS_ACTIVE
  });
  if (!activeUserCourse) {
    return null;
  }

  return activeUserCourse;
};
