import mongoose, { FilterQuery } from 'mongoose';
import { ISubModule } from '../../interfaces/submodule.interface';
import submoduleDao from '../../dao/submodule.dao';
import ErrorHandler from '../../utils/error-handler';

export const createSubmoduleService = async (
  data: Partial<ISubModule>
): Promise<ISubModule> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();

    const existingSubmodules = await submoduleDao.findAll(
      { module: data.module, order: data.order },
      { order: 1 }
    );
    if (existingSubmodules.length > 0) {
      throw new ErrorHandler(
        400,
        'Submodule creation failed. Please reorder the existing submodule first.'
      );
    }

    const newSubmodule = await submoduleDao.create(data, session);
    await session.commitTransaction();
    return newSubmodule;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const getSubmoduleByIdService = async (
  id: string,
  populate?: string | string[]
): Promise<ISubModule | null> => {
  return await submoduleDao.findById(id, populate);
};

export const getAllSubmodulesService = async (
  filter: FilterQuery<ISubModule>,
  sort: { [key: string]: -1 | 1 } = { createdAt: -1 },
  page: number = 1,
  limit: number = 10,
  populate?: string | string[]
): Promise<{ submodules: ISubModule[]; total: number }> => {
  const skip = (page - 1) * limit;
  const submodules = await submoduleDao.findAll(
    filter,
    sort,
    skip,
    limit,
    populate
  );

  // Count total matching documents for pagination
  const countFilter = { ...filter };
  const total = await mongoose.models.SubModule.countDocuments(countFilter);

  return { submodules, total };
};

export const updateSubmoduleService = async (
  id: string,
  data: Partial<ISubModule>
): Promise<ISubModule | null> => {
  const session = await mongoose.startSession();
  try {
    const { order, module, ...allowedUpdates } = data;
    if (order) {
      throw new ErrorHandler(
        400,
        'Updating order is not allowed. Please use the reorderSubmoduleService instead.'
      );
    }
    //check if the submodule exists
    const submodule = await submoduleDao.findById(id);

    if (!submodule) {
      throw new ErrorHandler(
        404,
        'Submodule not found. Please check the ID and try again.'
      );
    }
    // If client tried to update module, log warning
    if (module !== undefined) {
      throw new ErrorHandler(
        400,
        'Updating module is not allowed. Please contact support.'
      );
    }
    // If client tried to update order, log warning

    session.startTransaction();
    const updatedSubmodule = await submoduleDao.update(
      id,
      allowedUpdates,
      session
    );
    await session.commitTransaction();
    return updatedSubmodule;
  } catch (error) {
    console.error('Error in updateSubmoduleService:', error);
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};
