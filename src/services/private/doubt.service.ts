import { IDoubt } from '../../interfaces/doubt.interface';
import doubtDao from '../../dao/doubt.dao';
import mongoose from 'mongoose';

export const createDoubtService = async (
  data: Partial<IDoubt>
): Promise<IDoubt> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();

    // check if the attachment are there with the doubt
    // if the attachment is there, then we need to upload the file
    // the attachment will be in base64 format
    // also check if the attachment is in the correct format
    // if the attachment is not in the correct format, then throw an error
    // if the attachment is in the correct format, then upload the file

    const newDoubt = await doubtDao.create(data, session);
    await session.commitTransaction();
    return newDoubt;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const getDoubtByIdService = async (
  id: string
): Promise<IDoubt | null> => {
  return await doubtDao.findById(id);
};

export const resolveDoubtService = async (
  id: string,
  resolvedBy: string
): Promise<IDoubt | null> => {
  return await doubtDao.markAsResolved(
    id,
    new mongoose.Types.ObjectId(resolvedBy)
  );
};

export const assignDoubtService = async (
  id: string,
  assignedTo: string
): Promise<IDoubt | null> => {
  return await doubtDao.assignDoubt(
    id,
    new mongoose.Types.ObjectId(assignedTo)
  );
};
