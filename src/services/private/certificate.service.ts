import { Types } from 'mongoose';
import certificateDao from '../../dao/certificate.dao';
import { ICertificate } from '../../interfaces/certificate.interface';
import ErrorHandler from '../../utils/error-handler';
import { generateCertificateId } from '../../utils/certificate-id.utils';
import { generateCertificate } from '../../utils/certificate-pdf.utils';
import userProgressDao from '../../dao/user-progress.dao';
import enrolledCourseDao from '../../dao/enrolled-course.dao';
import { IUser } from '../../interfaces/user.interface';
import { ICourse } from '../../interfaces/course.interface';
import { config } from '../../config/config';
import { IBatch } from '../../interfaces/batch.interface';
/**
 * Create a new certificate
 */
export const createCertificateService = async (
  userId: string | Types.ObjectId,
  courseId: string | Types.ObjectId,
  batchId: string | Types.ObjectId,
  userDetails: IUser,
  courseDetails: ICourse,
  batchDetails: IBatch,
  typeOfCertificate: 'progress' | 'excellence' | null
): Promise<ICertificate[]> => {
  // Return an array of certificates now

  if (!typeOfCertificate) {
    return [];
  }

  const userCourseDetails = await enrolledCourseDao.findOne({
    userId: new Types.ObjectId(userId),
    courseId: new Types.ObjectId(courseId),
    batchId: new Types.ObjectId(batchId)
  });

  if (!userCourseDetails) {
    throw new ErrorHandler(400, 'User not enrolled in this course or batch');
  }

  // Get progress from UserProgress model instead of enrolledCourseLink
  const userProgress = await userProgressDao.findOne({
    user: new Types.ObjectId(userId),
    course: new Types.ObjectId(courseId)
  });

  // Calculate progress percentage from module progress
  let progress = 0;
  if (userProgress) {
    progress = userProgress.overallProgress;
  }

  // Determine certificate types based on progress
  const certificateTypes: ('progress' | 'excellence')[] = [];

  if (progress >= Number(config.certificateCompletionPercentage)) {
    certificateTypes.push('excellence');
  }

  if (progress >= Number(config.certificateExcellencePercentage)) {
    certificateTypes.push('progress');
  }

  if (certificateTypes.length === 0) {
    throw new ErrorHandler(
      400,
      'User progress must be at least 65% for any certificate'
    );
  }

  // Create an array to store the certificates
  const certificates: ICertificate[] = [];

  for (const certificateType of certificateTypes) {
    // Check if a certificate of this type already exists
    const existingCertificateOfSameType = await certificateDao.findOne({
      user: new Types.ObjectId(userId),
      course: new Types.ObjectId(courseId),
      batch: new Types.ObjectId(batchId),
      type: { $eq: certificateType }
    });

    if (existingCertificateOfSameType) {
      return certificates; // Skip if already exists
    }

    // Generate certificate ID and verification URL
    const credentialId = generateCertificateId(
      new Types.ObjectId(userId),
      new Types.ObjectId(courseId),
      new Types.ObjectId(batchId)
    );
    const verificationUrl = `${process.env.BASE_URL || 'https://sheryians.com'}/credential/${credentialId}`;

    // Certificate data for PDF generation
    const certificateData = {
      userName: userDetails.name.firstName + ' ' + userDetails.name.lastName,
      courseName: userCourseDetails.courseTitle,
      certificateId: credentialId,
      batchName: batchDetails.name, // Replace with real batch name if available
      instructorName: courseDetails.instructors[0].name, // Replace with real instructor if available
      certificateUrl: verificationUrl,
      type:
        progress >= Number(config.certificateExcellencePercentage)
          ? config.certificateExcellence
          : progress >= Number(config.certificateCompletionPercentage)
            ? config.certificateCompletion
            : ''
    };

    // Generate the certificate PDF
    const generatedCertificate = await generateCertificate(certificateData);
    // Create new certificate
    const newCertificate = await certificateDao.create({
      user: new Types.ObjectId(userId),
      course: new Types.ObjectId(courseId),
      batch: new Types.ObjectId(batchId),
      certificateId: credentialId,
      type: typeOfCertificate,
      progress: progress, // Using our calculated progress value instead of userCourseDetails.progress
      certificateUrl: generatedCertificate.cloudUrl,
      issueDate: new Date(),
      metadata: {
        userName: userDetails.name.firstName + ' ' + userDetails.name.lastName,
        courseName: userCourseDetails.courseTitle,
        batchName: 'Batch 1',
        instructorName: courseDetails.instructors[0].name
      }
    });

    certificates.push(newCertificate);
  }

  // Return the array of certificates
  return certificates;
};

export const getCertificateEligibilityService = async (
  userId: string | Types.ObjectId,
  courseId: string | Types.ObjectId
): Promise<{
  eligible: boolean;
  certificateType: 'progress' | 'excellence' | null;
}> => {
  // Check if the user is enrolled in the course
  const userCourseDetails = await enrolledCourseDao.findOne({
    userId: new Types.ObjectId(userId),
    courseId: new Types.ObjectId(courseId)
  });
  if (!userCourseDetails) {
    throw new ErrorHandler(400, 'User not enrolled in this course');
  }

  // Check the user's progress
  const currentProgress = await userProgressDao.findOne({
    user: new Types.ObjectId(userId),
    course: new Types.ObjectId(courseId)
  });

  if (!currentProgress) {
    throw new ErrorHandler(400, 'User progress not found');
  }

  const overallProgress = currentProgress.overallProgress || 0;

  // Find existing certificate for this user and course
  const existingCertificate = await certificateDao.findOne({
    user: new Types.ObjectId(userId),
    course: new Types.ObjectId(courseId)
  });

  if (existingCertificate) {
    // If the user's progress has not increased since the certificate was issued, return not eligible
    if (
      typeof existingCertificate.progress === 'number' &&
      overallProgress <= existingCertificate.progress
    ) {
      return { eligible: false, certificateType: null };
    }
  }

  // Determine eligibility and certificate type
  if (overallProgress >= 80) {
    return { eligible: true, certificateType: 'excellence' };
  }
  if (overallProgress >= 65) {
    return { eligible: true, certificateType: 'progress' };
  }

  // If the user is not eligible for any certificate
  return { eligible: false, certificateType: null };
};
