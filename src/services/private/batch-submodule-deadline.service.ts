import { IBatchSubmoduleDeadline } from '../../interfaces/batch-submodule-deadline.interface';
import batchSubModuleDeadlineDao from '../../dao/batch-submodule-deadline.dao';
import mongoose, { SortOrder } from 'mongoose';
import moment from 'moment';
import {
  applyUniformDurationFromPoint,
  getOrderedSubmodulesFromPoint,
  shiftSubmoduleDates,
  subModuleDeadlineInitializer
} from '../../utils/submodule-deadline.util';
import submoduleDao from '../../dao/submodule.dao';
import moduleDao from '../../dao/module.dao';
import ErrorHandler from '../../utils/error-handler';

type BatchSubModuleDeadlineFilter = Partial<Record<string, unknown>>;
type BatchSubModuleDeadlineSort = Record<string, SortOrder>;

export const createBatchSubmoduleDeadlineService = async (
  data: IBatchSubmoduleDeadline
): Promise<IBatchSubmoduleDeadline> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();
    const newBatchSubmoduleDeadline = await batchSubModuleDeadlineDao.create(
      data,
      session
    );
    await session.commitTransaction();
    return newBatchSubmoduleDeadline;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const getBatchSubmoduleDeadlineService = async (
  pagination: { page: number; limit: number; skip: number },
  dbQuery: {
    filter: BatchSubModuleDeadlineFilter;
    sort: BatchSubModuleDeadlineSort;
  } = {
    filter: {},
    sort: { createdAt: -1 }
  }
): Promise<IBatchSubmoduleDeadline[] | null> => {
  const { limit, skip } = pagination;
  const { filter, sort } = dbQuery;

  const batchSubmoduleDeadline = await batchSubModuleDeadlineDao.findAll(
    filter,
    sort,
    skip,
    limit
  );
  if (!batchSubmoduleDeadline) {
    throw new ErrorHandler(404, `Batch submodule deadline not found`);
  }

  return batchSubmoduleDeadline;
};

export const updateBatchSubmoduleDeadlineService = async (
  id: string,
  data: Partial<IBatchSubmoduleDeadline>
): Promise<IBatchSubmoduleDeadline | null> => {
  const session = await mongoose.startSession();
  try {
    const existingBatchSubmoduleDeadline =
      await batchSubModuleDeadlineDao.findById(id);
    if (!existingBatchSubmoduleDeadline) {
      throw new ErrorHandler(
        404,
        `Batch submodule deadline with ID ${id} not found`
      );
    }
    if (data.startDate || data.days) {
      if (
        data.startDate &&
        !moment(data.startDate, moment.ISO_8601, true).isValid()
      ) {
        throw new ErrorHandler(400, 'Invalid startDate provided');
      }

      let calculatedStartDate = existingBatchSubmoduleDeadline.startDate;
      let calculatedEndDate = moment(existingBatchSubmoduleDeadline.startDate)
        .add(existingBatchSubmoduleDeadline.days, 'days')
        .toDate();

      if (data.startDate && data.days) {
        const newStartDate = await subModuleDeadlineInitializer(
          existingBatchSubmoduleDeadline.submodule,
          data.startDate,
          data.dependsOnPreviousSubmodule as boolean,
          existingBatchSubmoduleDeadline.batch
        );

        if (!newStartDate.success) {
          throw new ErrorHandler(400, newStartDate.message);
        }

        calculatedStartDate = moment(newStartDate.data).toDate();
        calculatedEndDate = moment(calculatedStartDate)
          .add(existingBatchSubmoduleDeadline.days, 'days')
          .toDate();
      }

      if (data.days) {
        calculatedEndDate = moment(calculatedStartDate)
          .add(data.days, 'days')
          .toDate();
      }

      if (
        moment(calculatedStartDate).isSame(
          existingBatchSubmoduleDeadline.startDate
        ) &&
        moment(calculatedEndDate).isSame(
          moment(existingBatchSubmoduleDeadline.startDate).add(
            existingBatchSubmoduleDeadline.days,
            'days'
          )
        )
      ) {
        // Dates are similar, no need to update
        return existingBatchSubmoduleDeadline;
      }

      data.startDate = calculatedStartDate;
      data.deadline = calculatedEndDate;
    }

    session.startTransaction();

    // Check if the batch submodule deadline exists

    const batchSubmoduleDeadline = await batchSubModuleDeadlineDao.update(
      id,
      data,
      session
    );
    if (!batchSubmoduleDeadline) {
      throw new ErrorHandler(
        400,
        `Batch submodule deadline with ID ${id} not found`
      );
    }
    await session.commitTransaction();
    return batchSubmoduleDeadline;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

// Utility to get previous submodule from an ordered list

export const initializeSubmoduleAndCascadeService = async ({
  batchId,
  submoduleId,
  startDate,
  days,
  dependsOnPreviousSubmodule,
  userId,
  penaltyRules
}: {
  batchId: string | mongoose.Types.ObjectId;
  submoduleId: string | mongoose.Types.ObjectId;
  startDate: string | Date;
  days: number;
  dependsOnPreviousSubmodule?: boolean;
  userId: string | mongoose.Types.ObjectId;
  penaltyRules: {
    daysLate: number;
    penaltyPercentage: number;
  }[];
}): Promise<IBatchSubmoduleDeadline[] | null> => {
  const submodule = await submoduleDao.findById(
    submoduleId.toString(),
    'module'
  );

  if (!submodule || !submodule.module) {
    throw new ErrorHandler(404, 'Submodule or parent module not found');
  }

  const module =
    typeof submodule.module === 'object' && 'course' in submodule.module
      ? submodule.module
      : await moduleDao.findById(
          (submodule.module as mongoose.Types.ObjectId).toString(),
          'course'
        );

  if (!module || !module.course) {
    throw new ErrorHandler(404, 'Module or course not found');
  }

  const courseId = module.course.toString();

  const list = await getOrderedSubmodulesFromPoint(courseId.toString());

  const startIndex = list.findIndex(
    sm => String(sm._id) === String(submoduleId)
  );
  if (startIndex === -1)
    throw new ErrorHandler(404, 'Submodule not found in course');

  let actualStartDate = new Date(startDate);

  // Handle dependsOnPreviousSubmodule logic
  if (dependsOnPreviousSubmodule && startIndex > 0) {
    const previousSubmodule = list[startIndex - 1];
    const prevDeadline = await batchSubModuleDeadlineDao.findOne({
      batch: batchId,
      submodule: previousSubmodule._id
    });

    if (prevDeadline && prevDeadline.deadline) {
      // Use the previous submodule's deadline as the start date for this submodule
      actualStartDate = new Date(prevDeadline.deadline);
    } else {
      throw new ErrorHandler(
        400,
        'Cannot depend on previous submodule because it has no deadline set'
      );
    }
  } else {
    // Validate against previous submodule if not depending on it
    if (startIndex > 0) {
      const previousSubmodule = list[startIndex - 1];
      const prevDeadline = await batchSubModuleDeadlineDao.findOne({
        batch: batchId,
        submodule: previousSubmodule._id
      });

      if (prevDeadline && prevDeadline.deadline) {
        const prevEnd = new Date(prevDeadline.deadline);
        if (actualStartDate <= prevEnd) {
          throw new ErrorHandler(
            400,
            `The new start date overlaps with the previous submodule's deadline. Please choose a later date.`
          );
        }
      }
    }
  }

  // Now shift current and following submodules
  await shiftSubmoduleDates(
    batchId.toString(),
    courseId,
    submoduleId.toString(),
    actualStartDate,
    days,
    userId.toString(),
    penaltyRules
  );

  await applyUniformDurationFromPoint(
    batchId.toString(),
    courseId,
    submoduleId.toString(),
    days,
    userId.toString(),
    penaltyRules
  );

  return batchSubModuleDeadlineDao.findAll(
    { batch: batchId },
    { startDate: 1, deadline: 1 },
    0,
    1000
  );
};
