import { IMCQ } from '../../interfaces/mcq.interface';
import mcqDao from '../../dao/mcq.dao';
import mongoose from 'mongoose';
import submoduleContentDao from '../../dao/submodule-content.dao';
import { submodulePointsRefactor } from '../../utils/submodule.utils';
import <PERSON>rrorHandler from '../../utils/error-handler';
import { canDeleteContent } from '../../utils/content.utils';
import { uploadBase64File } from '../../utils/imagekit-uploader';

export const createMCQService = async (data: Partial<IMCQ>): Promise<IMCQ> => {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();
    if (data.explanation?.image) {
      // If the image is in base64 format, upload it to ImageKit
      const fileName = `mcq_${Date.now()}`; // Generate a unique file name
      const uploadedUrl = await uploadBase64File(
        data.explanation.image,
        fileName
      );

      if (!uploadedUrl) {
        throw new ErrorHandler(400, 'Image upload failed');
      }
      data.explanation.image = uploadedUrl;
    }

    const newMCQ = await mcqDao.create(data, session);
    await session.commitTransaction();
    return newMCQ;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    await session.endSession();
  }
};

export const getMCQByIdService = async (id: string): Promise<IMCQ | null> => {
  return await mcqDao.findById(id);
};

export const updateMCQService = async (
  id: string,
  data: Partial<IMCQ>
): Promise<IMCQ | null> => {
  //first check if the mcq exists
  // check if the mcq is in content-module
  // if the mcq is in content-module, then update the submodule points
  const existingMcq = await mcqDao.findById(id);
  if (!existingMcq) {
    return null;
  }

  if (data.points && data.points !== existingMcq.points) {
    const subModuleContents = await submoduleContentDao.findAll({
      contentId: existingMcq._id
    });
    if (subModuleContents.length > 0) {
      // calculate the points difference
      const pointsDifference = data.points - existingMcq.points;
      for (const subModuleContent of subModuleContents) {
        await submodulePointsRefactor(
          subModuleContent.submodule as string,
          pointsDifference
        );
      }
    }

    // if the mcq is not in content-module, then update the mcq directly
    // if it is not attached to the submodule-content, then we can update the mcq directly
    const updatedMcq = await mcqDao.update(id, data);
    if (!updatedMcq) {
      return null;
    }
    return updatedMcq;
  }

  return await mcqDao.update(id, data);
};

export const deleteMCQService = async (id: string): Promise<IMCQ | null> => {
  // check if the mcq exists

  const existingMcq = await mcqDao.findById(id);
  if (!existingMcq) {
    throw new ErrorHandler(404, 'MCQ not found');
  }

  //check if the mcq is in content-module
  const checkCanDelete = await canDeleteContent('mcq', id);
  // if the mcq is in content-module, then do not delete the mcq
  if (!checkCanDelete.canDelete || checkCanDelete.attached) {
    throw new ErrorHandler(403, 'MCQ is attached to a submodule');
  }

  // if the mcq is not in content-module, then delete the mcq directly
  const deletedMcq = await mcqDao.delete(id);
  if (!deletedMcq) {
    throw new ErrorHandler(404, 'MCQ not found');
  }
  return deletedMcq;
};
