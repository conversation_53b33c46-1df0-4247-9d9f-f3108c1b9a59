import mongoose, { isValidObjectId } from 'mongoose';
import LeaderboardDao from '../../dao/leaderboard.dao';
import userProgressDao from '../../dao/user-progress.dao';
import <PERSON>rrorHandler from '../../utils/error-handler';
import { redis } from '../../utils/redis';
import Redlock from 'redlock';
import { Period, ILeaderboard } from '../../interfaces/leaderboard.interface';
import { IUserStreak } from '../../interfaces/user-streak.interface';
import { IUserProgress } from '../../interfaces/user-progress.interface';
import submoduleContentDao from '../../dao/submodule-content.dao';
import userStreakDao from '../../dao/user-streak.dao';
import { initializeUserLeaderboard } from '../private/job-handler.service';

// Use the shared redis client for Redlock
const redlock = new Redlock([redis], {
  retryCount: 10,
  retryDelay: 200,
  retryJitter: 200
});

/**
 * Fetch leaderboard details for a specific batch and period
 */

export const fetchLeaderboardDetails = async (
  batchId: string,
  period: Period,
  page = 1,
  limit = 20
) => {
  try {
    let leaderboard;
    if (period === 'daily') {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      leaderboard = await LeaderboardDao.findOne({
        batch: batchId,
        period: 'daily',
        startDate: today
      });
    } else {
      leaderboard = await LeaderboardDao.findByBatch(batchId, period);
    }

    if (!leaderboard) {
      throw new ErrorHandler(
        404,
        `Leaderboard not found for batchId: ${batchId} and period: ${period}`
      );
    }

    if (!Array.isArray(leaderboard.rankings)) {
      return;
    }

    // Sort today's rankings by ascending rank
    leaderboard.rankings.sort((a, b) => (a.rank ?? 0) - (b.rank ?? 0));

    // Non-daily: return paginated data without progress trend
    if (period !== 'daily') {
      const startIdx = (page - 1) * limit;
      const endIdx = startIdx + limit;
      return {
        ...leaderboard,
        rankings: leaderboard.rankings.slice(startIdx, endIdx),
        pagination: {
          page,
          limit,
          total: leaderboard.rankings.length,
          totalPages: Math.ceil(leaderboard.rankings.length / limit)
        }
      };
    }

    // const cacheKey = `leaderboard:progressTrend:${batchId}:${period}:${page}:${new Date().toISOString().slice(0, 10)}`;
    // const cachedRankingsRaw = await redis.get(cacheKey);
    // let cachedRankings: unknown;

    // if (cachedRankingsRaw) {
    //   try {
    //     cachedRankings = JSON.parse(cachedRankingsRaw);
    //   } catch {
    //     cachedRankings = undefined;
    //   }
    // }

    // Serve from cache if valid
    // if (
    //   Array.isArray(cachedRankings) &&
    //   cachedRankings.every(
    //     u =>
    //       typeof u === 'object' &&
    //       u !== null &&
    //       'user' in u &&
    //       'userName' in u &&
    //       'rank' in u &&
    //       'points' in u &&
    //       'progress' in u &&
    //       'streak' in u &&
    //       'progressTrend' in u
    //   )
    // ) {
    //   const startIdx = (page - 1) * limit;
    //   const endIdx = startIdx + limit;
    //   return {
    //     ...leaderboard,
    //     rankings: cachedRankings.slice(startIdx, endIdx),
    //     pagination: {
    //       page,
    //       limit,
    //       total: cachedRankings.length,
    //       totalPages: Math.ceil(cachedRankings.length / limit)
    //     }
    //   };
    // }

    // No valid cache: compute progress trend
    const yesterdayUserRankMap: Record<string, number> = {};
    let yesterdayStartDate: Date | null = null;

    if (leaderboard.startDate) {
      const currStart = new Date(leaderboard.startDate);
      yesterdayStartDate = new Date(currStart.getTime() - 24 * 60 * 60 * 1000);
    }

    let yLeaderboardRankings: ILeaderboard['rankings'] = [];
    if (yesterdayStartDate) {
      try {
        const yLeaderboard = await LeaderboardDao.findOne({
          batch: batchId,
          period: 'daily',
          startDate: yesterdayStartDate
        });
        if (yLeaderboard?.rankings && Array.isArray(yLeaderboard.rankings)) {
          yLeaderboardRankings = yLeaderboard.rankings;
        }
      } catch {
        // No-op: treat as no previous rank
        throw new ErrorHandler(500, `Failed to fetch yesterday's leaderboard`);
      }
    }

    if (yLeaderboardRankings.length) {
      yLeaderboardRankings.sort((a, b) => (a.rank ?? 0) - (b.rank ?? 0));
      for (const yUser of yLeaderboardRankings) {
        if (
          typeof yUser === 'object' &&
          yUser !== null &&
          'user' in yUser &&
          'rank' in yUser &&
          yUser.user !== undefined &&
          yUser.rank !== undefined
        ) {
          yesterdayUserRankMap[String(yUser.user)] = yUser.rank;
        }
      }
    }

    const rankingsWithTrend = leaderboard.rankings.map(user => {
      const yesterdayRank = yesterdayUserRankMap[String(user.user)];
      const todayRank = user.rank;

      let progressTrend: 'increasing' | 'decreasing' | 'same' = 'same';
      if (yesterdayRank !== undefined && todayRank !== undefined) {
        if (todayRank < yesterdayRank) progressTrend = 'increasing';
        else if (todayRank > yesterdayRank) progressTrend = 'decreasing';
      }

      return { ...user, progressTrend };
    });

    // Cache the new result
    // await redis.set(
    //   cacheKey,
    //   JSON.stringify(rankingsWithTrend),
    //   'EX',
    //   60 * 60 * 60
    // );

    // Paginate and return
    const startIdx = (page - 1) * limit;
    const endIdx = startIdx + limit;

    return {
      ...leaderboard,
      rankings: rankingsWithTrend.slice(startIdx, endIdx),
      pagination: {
        page,
        limit,
        total: rankingsWithTrend.length,
        totalPages: Math.ceil(rankingsWithTrend.length / limit)
      }
    };
  } catch (e) {
    const message = e instanceof Error ? e.message : String(e);
    const status =
      typeof e === 'object' &&
      e !== null &&
      ('status' in e || 'statusCode' in e)
        ? (e as { status?: number; statusCode?: number }).status ||
          (e as { status?: number; statusCode?: number }).statusCode ||
          500
        : 500;
    throw new ErrorHandler(status, `Failed to fetch leaderboard: ${message}`);
  }
};

/**
 * Update leaderboard rankings for a batch and course based on user progress
 * Optimized: fetch all user progresses in one query for performance
 */
export const updateLeaderboardRankings = async (
  batchId: string,
  courseId: string,
  userStreaks: IUserStreak // Accept user streaks as an additional parameter
) => {
  if (!batchId || !isValidObjectId(batchId))
    throw new ErrorHandler(402, 'Invalid or missing batchId');
  if (!courseId || !isValidObjectId(courseId))
    throw new ErrorHandler(402, 'Invalid or missing courseId');

  // Only update daily and overall for now, but type is always Period
  const periods: Period[] = ['daily', 'overall'];
  const results: Record<Period, unknown> = {
    daily: null,
    weekly: null,
    monthly: null,
    overall: null
  };

  for (const period of periods) {
    // Use a distributed lock per leaderboard to prevent concurrent updates
    const lockKey = `lock:leaderboard:${batchId}:${courseId}:${period}`;
    let lock;
    try {
      lock = await redlock.acquire([lockKey], 5000); // 5 seconds lock
    } catch {
      results[period] = {
        error:
          'Could not acquire lock for leaderboard update. Please try again.'
      };
      continue;
    }
    try {
      const leaderboard = await LeaderboardDao.findOne({
        batch: new mongoose.Types.ObjectId(batchId),
        course: new mongoose.Types.ObjectId(courseId),
        period,
        ...(period === 'daily' && {
          startDate: (() => {
            const now = new Date();
            return new Date(now.getFullYear(), now.getMonth(), now.getDate());
          })()
        })
      });
      if (!leaderboard) {
        results[period] = { error: 'Leaderboard not found' };
        continue;
      }

      if (
        !Array.isArray(leaderboard.rankings) ||
        leaderboard.rankings.length === 0
      ) {
        results[period] = { message: 'No rankings to update' };
        continue;
      }

      // Collect all user IDs in the leaderboard
      const userIds = leaderboard.rankings.map(r => r.user);
      // Fetch all user progresses in one query
      const userProgresses = await userProgressDao.findAll({
        user: { $in: userIds },
        course: new mongoose.Types.ObjectId(courseId),
        batch: new mongoose.Types.ObjectId(batchId)
      });
      // Map userId to totalPointsEarned and overallProgress
      const userPointsMap = new Map<string, number>();
      const userProgressMap = new Map<string, number>();
      for (const up of userProgresses) {
        userPointsMap.set(String(up.user), up.totalPointsEarned ?? 0);
        userProgressMap.set(String(up.user), up.overallProgress ?? 0);
      }
      let rankingsChanged = false;
      for (const ranking of leaderboard.rankings) {
        const newPoints = userPointsMap.get(String(ranking.user)) ?? 0;
        const newProgress = userProgressMap.get(String(ranking.user)) ?? 0;
        const newStreak =
          userStreaks && userStreaks.currentStreak !== undefined
            ? userStreaks.currentStreak
            : ranking.streak || 0; // Default to 0 if streak is undefined

        if (ranking.points !== newPoints) {
          ranking.points = newPoints;
          rankingsChanged = true;
        }
        if (ranking.progress !== newProgress) {
          ranking.progress = newProgress;
          rankingsChanged = true;
        }
        if (ranking.streak !== newStreak) {
          ranking.streak = newStreak;
          rankingsChanged = true;
        }
      }

      // Sort only if changes occurred
      if (rankingsChanged) {
        leaderboard.rankings.sort((a, b) => b.points - a.points);
        leaderboard.rankings.forEach((r, idx) => {
          r.rank = idx + 1;
        });
        await LeaderboardDao.updateRankings(
          String(leaderboard._id),
          leaderboard.rankings
        );
        results[period] = {
          message: 'Updated successfully',
          rankings: leaderboard.rankings
        };
        continue;
      }
      results[period] = { message: 'No changes in rankings' };
    } finally {
      // Always release the lock
      if (lock) {
        try {
          await lock.release();
        } catch {
          // ignore
        }
      }
    }
  }

  return results;
};

// Utility to get period dates
export const getPeriodDates = (period: 'daily' | 'overall') => {
  const now = new Date();
  const start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const end = new Date(start);
  if (period === 'overall') {
    // End date is 4 months after start date
    end.setMonth(end.getMonth() + 4);
  }
  return { startDate: start, endDate: end };
};

export const upsertLeaderboardRankings = async (doc: IUserProgress) => {
  if (!doc?.batch || !doc?.user) return;

  const batchStr = doc.batch.toString();
  const userId = doc.user.toString();

  // --- REDIS CACHE CLEARING ---
  const clearRedisCache = async () => {
    try {
      const patterns = [
        `leaderboard:progressTrend:${batchStr}:daily:*`,
        `leaderboard:progressTrend:${batchStr}:overall:*`
      ];

      for (const pattern of patterns) {
        let cursor = '0';
        do {
          const [newCursor, keys] = await redis.scan(
            cursor,
            'MATCH',
            pattern,
            'COUNT',
            100
          );
          cursor = newCursor;
          if (keys.length) await redis.del(...keys);
        } while (cursor !== '0');
      }
    } catch (err) {
      console.error('Failed to clear leaderboard cache', err);
    }
  };

  // --- STREAK LOGIC ---
  type Activity = {
    type: 'lesson' | 'practice';
    contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab';
    entityId: mongoose.Types.ObjectId;
    entityType: string;
    pointsEarned: number;
    timestamp: Date;
  };
  const getTodayActivities = async (): Promise<{
    activities: Activity[];
    points: number;
  }> => {
    const activities: Activity[] = [];
    let points = 0;

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (!Array.isArray(doc.contentItemProgress)) return { activities, points };

    for (const item of doc.contentItemProgress) {
      if (
        !item.completed ||
        !item.completedAt ||
        !item.contentId ||
        !item.contentType
      ) {
        continue;
      }

      const validTypes = [
        'video',
        'note',
        'mcq',
        'coding_problem',
        'coding_lab'
      ];
      if (!validTypes.includes(item.contentType)) continue;

      const completedAt = new Date(item.completedAt);
      completedAt.setHours(0, 0, 0, 0);

      let type: 'lesson' | 'practice' = 'lesson';
      try {
        const subContent = await submoduleContentDao.findByContentIdAndType(
          item.contentId,
          item.contentType
        );
        if (subContent?.section === 'practice') type = 'practice';
      } catch {
        console.error(
          'Failed to fetch submodule content',
          item.contentId,
          item.contentType
        );
      }

      activities.push({
        type,
        contentType: item.contentType,
        entityId: item.contentId,
        entityType: 'course',
        pointsEarned: item.pointsEarned || 0,
        timestamp: item.completedAt
      });

      points += item.pointsEarned || 0;
    }

    return { activities, points };
  };

  let userStreak: IUserStreak | null = null;
  const updateUserStreak = async (userId: mongoose.Types.ObjectId) => {
    try {
      const { activities, points } = await getTodayActivities(); // Removed userId argument as it is not expected
      if (!activities.length) return;

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);

      userStreak = await userStreakDao.findByUser(String(userId));
      if (!userStreak) {
        userStreak = await userStreakDao.create({
          user: userId,
          currentStreak: 1,
          longestStreak: 1,
          lastActivityDate: today,
          streakHistory: [{ date: today, pointsEarned: points, activities }],
          totalDaysActive: 1
        });
        return;
      }

      const lastActivityDate = new Date(userStreak.lastActivityDate || '');
      lastActivityDate.setHours(0, 0, 0, 0);

      if (lastActivityDate.getTime() === today.getTime()) {
        // Update streak history for the same day
        const todayHistory = userStreak.streakHistory.find(
          entry => new Date(entry.date).getTime() === today.getTime()
        );

        if (todayHistory) {
          todayHistory.pointsEarned += points;
          todayHistory.activities.push(...activities);
        } else {
          userStreak.streakHistory.push({
            date: today,
            pointsEarned: points,
            activities
          });
        }

        await userStreakDao.update(String(userStreak._id), {
          streakHistory: userStreak.streakHistory
        });
        return;
      }

      if (lastActivityDate.getTime() === yesterday.getTime()) {
        userStreak.currentStreak += 1;
        userStreak.longestStreak = Math.max(
          userStreak.longestStreak,
          userStreak.currentStreak
        );
        userStreak.totalDaysActive += 1;
      } else {
        userStreak.currentStreak = 1;
        userStreak.totalDaysActive += 1;
      }

      userStreak.lastActivityDate = today;
      userStreak.streakHistory.push({
        date: today,
        pointsEarned: points,
        activities
      });

      await userStreakDao.update(String(userStreak._id), userStreak);
    } catch (err) {
      console.error('Failed to update streak', err);
    }
  };

  await updateUserStreak(new mongoose.Types.ObjectId(userId));
  await clearRedisCache();

  // --- LEADERBOARD LOGIC ---
  const updateLeaderboard = async () => {
    if (!doc.course) return;

    const courseStr = doc.course.toString();
    try {
      const leaderboard = await LeaderboardDao.findOne({
        batch: doc.batch,
        course: doc.course,
        period: 'daily',
        startDate: getPeriodDates('daily').startDate
      });
      const overallLeaderboard = await LeaderboardDao.findOne({
        batch: doc.batch,
        course: doc.course,
        period: 'overall'
      });

      if (!leaderboard || !overallLeaderboard) {
        // Initialize user in leaderboard if not found
        await initializeUserLeaderboard(
          doc.user,
          doc.course,
          doc.batch,
          undefined, // session is not available here, so pass null
          '', // jobId not available, pass empty string
          '' // enrollmentId not available, pass empty string
        );
      } else {
        // Check if user exists in the leaderboard
        const userExistsInDaily = leaderboard.rankings.some(
          user => user.user.toString() === doc.user.toString()
        );
        const userExistsInOverall = overallLeaderboard.rankings.some(
          user => user.user.toString() === doc.user.toString()
        );

        if (!userExistsInDaily || !userExistsInOverall) {
          await initializeUserLeaderboard(
            doc.user,
            doc.course,
            doc.batch,
            undefined, // session is not available here, so pass null
            '', // jobId not available, pass empty string
            '' // enrollmentId not available, pass empty string
          );
        }
      }

      if (
        userStreak &&
        typeof userStreak === 'object' &&
        'currentStreak' in userStreak
      ) {
        await updateLeaderboardRankings(batchStr, courseStr, userStreak);
      } else {
        console.error('Invalid userStreak object:', userStreak);
      }
    } catch (err) {
      console.error('Failed to update leaderboard', err);
    }
  };

  await updateLeaderboard();
};
