import { Types } from 'mongoose';
import videoDao from '../../dao/videos.dao';
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from '../../utils/error-handler';
import submoduleContentDao from '../../dao/submodule-content.dao';
import vdocipherService from '../vdocipher.service';
import { redis } from '../../utils/redis';
import { ProgressUpdateService } from './progress-update.service';
import userProgressDao from '../../dao/user-progress.dao';

// Define types for better type safety
interface IVideoProgress {
  videoId: string;
  watchedDuration: number;
  completed: boolean;
  pointsEarned: number;
  watchPercentage?: number;
}

interface IVideo {
  _id: Types.ObjectId;
  title: string;
  description: string;
  duration: number;
  points: number;
  [key: string]: unknown;
}

export const updateVideoProgressService = async (
  videoId: string,
  userId: string,
  watchedDuration: number,
  subModuleId: string,
  completed?: boolean
): Promise<{
  progress: IVideoProgress;
  message: string;
}> => {
  try {
    // Fetch user's progress for this video if exists
    const userProgress = await userProgressDao.findOneWithSelection(
      {
        user: new Types.ObjectId(userId),
        'contentItemProgress.contentId': new Types.ObjectId(videoId),
        'contentItemProgress.subModuleId': new Types.ObjectId(subModuleId)
      },
      {
        contentItemProgress: {
          $elemMatch: {
            contentId: new Types.ObjectId(videoId),
            subModuleId: new Types.ObjectId(subModuleId)
          }
        }
      }
    );
    // If user progress exists, check if the video is already completed
    if (userProgress && userProgress.contentItemProgress.length > 0) {
      const progress = userProgress.contentItemProgress[0];
      if (progress.completed) {
        return {
          progress: {
            videoId,
            watchedDuration,
            completed: progress.completed,
            pointsEarned: progress.pointsEarned
          },
          message: 'Video already completed'
        };
      }
    }

    const [video, submoduleContent] = await Promise.all([
      videoDao.findById(videoId),
      submoduleContentDao.findOne({
        contentId: new Types.ObjectId(videoId),
        contentType: 'video',
        submodule: new Types.ObjectId(subModuleId)
      })
    ]);

    if (!video) {
      throw new ErrorHandler(404, 'Video not found');
    }

    if (!submoduleContent) {
      throw new ErrorHandler(404, 'Video is not linked to any submodule');
    }

    // Use ProgressUpdateService to handle all progress tracking
    const result = await ProgressUpdateService.updateVideoProgress({
      userId,
      contentId: videoId,
      contentType: 'video',
      subModuleId,
      watchedDuration,
      videoDuration: video.duration || 0,
      originalPoints: video.points,
      completed
    });

    // Calculate watch percentage for the response
    const totalDuration = Number(video.duration);
    const watchPercentage =
      totalDuration > 0
        ? Math.min((watchedDuration / totalDuration) * 100, 100)
        : 0;

    // Return updated progress info
    return {
      progress: {
        videoId,
        watchedDuration,
        completed: result.completed,
        pointsEarned: result.pointsEarned,
        watchPercentage
      },
      message: 'Video progress updated successfully'
    };
  } catch (error) {
    console.error('Error updating video progress:', error);
    if (error instanceof ErrorHandler) {
      throw error;
    }
    throw new ErrorHandler(
      500,
      `Failed to update progress: ${(error as Error).message || 'Unknown error'}`
    );
  }
};

export const getVideoService = async (
  videoId: string,
  userId: string,
  subModuleId: string
) => {
  // Fetch video data
  const video = await videoDao.findById(videoId);
  if (!video) {
    throw new ErrorHandler(404, 'Video not found');
  }

  // Fetch user's progress for this video if exists
  const userProgress = await userProgressDao.findOneWithSelection(
    {
      user: new Types.ObjectId(userId),
      'contentItemProgress.contentId': new Types.ObjectId(videoId),
      'contentItemProgress.subModuleId': new Types.ObjectId(subModuleId)
    },
    {
      contentItemProgress: {
        $elemMatch: {
          contentId: new Types.ObjectId(videoId),
          subModuleId: new Types.ObjectId(subModuleId)
        }
      }
    }
  );

  return {
    ...video,
    progress: userProgress?.contentItemProgress?.[0] || null
  };
};

const VIDEO_METADATA_TTL = 60 * 60; // 1 hour for basic video info
const VDOCIPHER_URL_TTL = 240; // 4 minutes for iframe URLs (shorter than OTP TTL)

export const getVideoWithIframeSrcService = async (
  videoId: string,
  userId: string,
  subModuleId: string
) => {
  try {
    // Create cache keys
    const videoMetadataCacheKey = `video:metadata:${videoId}`;
    const userIframeCacheKey = `video:iframe:${videoId}:${userId}`;

    // Try to get video metadata from cache
    let videoData = null;
    const cachedVideoMetadata = await redis.get(videoMetadataCacheKey);

    if (cachedVideoMetadata) {
      // Use cached video metadata
      videoData = JSON.parse(cachedVideoMetadata) as IVideo;
    } else {
      // Fetch video data from database
      videoData = await videoDao.findById(videoId);

      if (!videoData) {
        throw new ErrorHandler(404, 'Video not found');
      }

      // Cache the video metadata
      await redis.setex(
        videoMetadataCacheKey,
        VIDEO_METADATA_TTL,
        JSON.stringify(videoData)
      );
    }

    // Try to get user-specific iframe source from cache
    let iframeData = null;
    const cachedIframeSrc = await redis.get(userIframeCacheKey);

    if (cachedIframeSrc) {
      // Use cached iframe source
      iframeData = JSON.parse(cachedIframeSrc) as {
        title: string;
        description: string;
        iframeSrc: string;
      };
    } else {
      // Generate new iframe source
      iframeData = await vdocipherService.getVideoWithIframeSrc(
        videoData.videoId as string,
        {
          userId: userId,
          email: userId,
          ttl: 300
        }
      );

      // Cache the iframe source with a shorter TTL
      await redis.setex(
        userIframeCacheKey,
        VDOCIPHER_URL_TTL,
        JSON.stringify(iframeData)
      );
    }

    // Fetch user's progress for this video if exists
    const userProgress = await userProgressDao.findOneWithSelection(
      {
        user: new Types.ObjectId(userId),
        'contentItemProgress.contentId': new Types.ObjectId(videoId),
        'contentItemProgress.subModuleId': new Types.ObjectId(subModuleId)
      },
      {
        contentItemProgress: {
          $elemMatch: {
            contentId: new Types.ObjectId(videoId),
            subModuleId: new Types.ObjectId(subModuleId)
          }
        }
      }
    );

    // Return combined result
    return {
      ...videoData,
      iframeData,
      progress: userProgress?.contentItemProgress?.[0] || null
    };
  } catch (error) {
    console.error('Error fetching video with iframe source:', error);
    throw error;
  }
};
