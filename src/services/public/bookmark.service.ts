import mongoose from 'mongoose';
import bookmarkDao from '../../dao/bookmark.dao';

export function getAllUserBookmarkService(
  userId: mongoose.Types.ObjectId | string
) {
  return bookmarkDao.findAll({ user: userId }, { createdAt: -1 });
}

export function getUserBookmarksByBatchService(
  userId: string | mongoose.Types.ObjectId,
  batchId: string | mongoose.Types.ObjectId
) {
  return bookmarkDao.getUserBookmarksByBatchId(userId, batchId);
}

export function toggleBookmarkService(
  userId: mongoose.Types.ObjectId | string,
  batch: mongoose.Types.ObjectId | string,
  submodule: mongoose.Types.ObjectId | string,
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
  contentId: mongoose.Types.ObjectId
) {
  return bookmarkDao.toggleBookmark(
    userId as mongoose.Types.ObjectId,
    batch as mongoose.Types.ObjectId,
    submodule as mongoose.Types.ObjectId,
    contentType,
    contentId
  );
}

export function isBookmarked(
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
  contentId: mongoose.Types.ObjectId
) {
  return bookmarkDao.isBookmarked(userId, batchId, contentType, contentId);
}
