// import { Types } from 'mongoose';
// import codingLabDao from '../../dao/coding-lab.dao';
// import ErrorHandler from '../../utils/error-handler';
// import UserProgress from '../../models/user-progress.model';
// import SubmoduleContent from '../../models/submodule-content.model';
// import { executeCode } from '../../../services/code-execution.service';

// export const getCodingLabService = async (labId: string, userId: string) => {
//   // Fetch coding lab data
//   const codingLab = await codingLabDao.findById(labId);

//   if (!codingLab) {
//     throw new ErrorHandler(404, 'Coding lab not found');
//   }

//   // Hide some test cases that are marked as hidden (similar to coding problems)
//   codingLab.testCases = codingLab.testCases.map(testCase => {
//     if (testCase.isHidden) {
//       return {
//         ...testCase,
//         input: testCase.input,
//         expectedOutput: 'hidden',
//         points: testCase.points
//       };
//     }
//     return testCase;
//   });

//   // Get user's progress if any
//   const userProgress = await UserProgress.findOne(
//     {
//       user: new Types.ObjectId(userId),
//       'contentItemProgress.contentId': new Types.ObjectId(labId),
//       'contentItemProgress.contentType': 'coding_lab'
//     },
//     {
//       'contentItemProgress.$': 1
//     }
//   );

//   // Get submodule content info
//   const submoduleContent = await SubmoduleContent.findOne({
//     contentId: new Types.ObjectId(labId),
//     contentType: 'coding_lab'
//   }).populate('submodule');

//   // Hide solutions unless the user has completed the lab
//   let solutionsData = undefined;
//   if (userProgress?.contentItemProgress[0]?.completed) {
//     solutionsData = codingLab.solutions;
//   }

//   return {
//     ...codingLab,
//     solutions: solutionsData,
//     progress: userProgress ? userProgress.contentItemProgress[0] : null,
//     submoduleInfo: submoduleContent
//       ? {
//           submoduleId: submoduleContent.submodule._id,
//           title: submoduleContent.submodule.title,
//           section: submoduleContent.section,
//           order: submoduleContent.order
//         }
//       : null
//   };
// };

// export const submitCodingLabSolutionService = async (
//   labId: string,
//   userId: string,
//   code: string,
//   language: string,
//   compilerId: string
// ) => {
//   // Check if coding lab exists
//   const codingLab = await codingLabDao.findById(labId);

//   if (!codingLab) {
//     throw new ErrorHandler(404, 'Coding lab not found');
//   }

//   // Find the submodule content to get course info
//   const submoduleContent = await SubmoduleContent.findOne({
//     contentId: new Types.ObjectId(labId),
//     contentType: 'coding_lab'
//   }).populate({
//     path: 'submodule',
//     populate: {
//       path: 'module',
//       populate: {
//         path: 'course'
//       }
//     }
//   });

//   if (!submoduleContent) {
//     throw new ErrorHandler(404, 'Coding lab is not linked to any submodule');
//   }

//   const courseId = submoduleContent.submodule.module.course._id;

//   // Find user progress document
//   const userProgress = await UserProgress.findOne({
//     user: new Types.ObjectId(userId),
//     course: courseId
//   });

//   if (!userProgress) {
//     throw new ErrorHandler(404, 'User progress record not found');
//   }

//   // Execute the code against each test case
//   const testResults = [];
//   let totalPoints = 0;
//   let allTestsPassed = true;

//   for (const testCase of codingLab.testCases) {
//     try {
//       const executionResult = await executeCode(
//         code,
//         language,
//         compilerId,
//         testCase.input
//       );

//       // Compare the output with expected output (normalized)
//       const normalizedActualOutput = executionResult.output.trim();
//       const normalizedExpectedOutput = testCase.expectedOutput.trim();
//       const passed = normalizedActualOutput === normalizedExpectedOutput;

//       const pointsEarned = passed ? testCase.points : 0;
//       totalPoints += pointsEarned;

//       if (!passed) {
//         allTestsPassed = false;
//       }

//       testResults.push({
//         testCaseId: testCase._id,
//         passed,
//         input: testCase.input,
//         expectedOutput: testCase.isHidden ? 'hidden' : testCase.expectedOutput,
//         actualOutput: executionResult.output,
//         pointsEarned
//       });
//     } catch (error) {
//       testResults.push({
//         testCaseId: testCase._id,
//         passed: false,
//         input: testCase.input,
//         expectedOutput: testCase.isHidden ? 'hidden' : testCase.expectedOutput,
//         actualOutput: `Error: ${error.message || 'Execution failed'}`,
//         pointsEarned: 0
//       });
//       allTestsPassed = false;
//     }
//   }

//   // Create submission record
//   const submission = {
//     code,
//     language,
//     submittedAt: new Date(),
//     testCaseResults: testResults,
//     totalPoints
//   };

//   // Find or create progress item
//   const contentItemIndex = userProgress.contentItemProgress.findIndex(
//     item =>
//       item.contentId.toString() === labId && item.contentType === 'coding_lab'
//   );

//   if (contentItemIndex >= 0) {
//     // Update existing progress item
//     userProgress.contentItemProgress[contentItemIndex].submissions = [
//       ...(userProgress.contentItemProgress[contentItemIndex].submissions || []),
//       submission
//     ];

//     // Update completion status if all tests passed and not already completed
//     if (
//       allTestsPassed &&
//       !userProgress.contentItemProgress[contentItemIndex].completed
//     ) {
//       userProgress.contentItemProgress[contentItemIndex].completed = true;
//       userProgress.contentItemProgress[contentItemIndex].completedAt =
//         new Date();
//       userProgress.contentItemProgress[contentItemIndex].pointsEarned =
//         Math.max(
//           userProgress.contentItemProgress[contentItemIndex].pointsEarned || 0,
//           totalPoints
//         );
//     } else if (
//       totalPoints >
//       (userProgress.contentItemProgress[contentItemIndex].pointsEarned || 0)
//     ) {
//       // Update points if higher than previous best
//       userProgress.contentItemProgress[contentItemIndex].pointsEarned =
//         totalPoints;
//     }
//   } else {
//     // Create new progress item
//     userProgress.contentItemProgress.push({
//       contentType: 'coding_lab',
//       contentId: new Types.ObjectId(labId),
//       contentModel: 'CodingLab',
//       completed: allTestsPassed,
//       pointsEarned: totalPoints,
//       submissions: [submission],
//       ...(allTestsPassed ? { completedAt: new Date() } : {})
//     });
//   }

//   // Update last activity timestamp
//   userProgress.lastActivityAt = new Date();

//   // Save changes
//   await userProgress.save();

//   // Recalculate progress
//   await recalculateUserProgress(userProgress._id);

//   // Return submission results
//   return {
//     labId,
//     allTestsPassed,
//     totalPoints,
//     maxPoints: codingLab.totalPoints,
//     testResults,
//     // Only reveal solutions if all tests passed
//     solutions: allTestsPassed ? codingLab.solutions : undefined
//   };
// };

// // Helper function to recalculate progress
// const recalculateUserProgress = async (userProgressId: Types.ObjectId) => {
//   // Implementation depends on your business logic
//   return UserProgress.findById(userProgressId);
// };
