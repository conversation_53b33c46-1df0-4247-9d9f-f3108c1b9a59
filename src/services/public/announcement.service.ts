import mongoose from 'mongoose';
import { IAnnouncement } from '../../interfaces/announcement.interface';
import announcementDao from '../../dao/announcement.dao';

export function getAllAnnouncementService(
  filter: mongoose.FilterQuery<IAnnouncement> = {}
) {
  return announcementDao.findAll(filter);
}

export function getAnnouncementByIdService(id: string) {
  return announcementDao.findById(id);
}

export function createAnnouncementService(data: Partial<IAnnouncement>) {
  return announcementDao.create(data);
}

export function updateAnnouncementService(
  filter: mongoose.FilterQuery<IAnnouncement>,
  data: Partial<IAnnouncement>
) {
  return announcementDao.update(filter, data);
}

export function deleteAnnouncementService(
  filter: mongoose.FilterQuery<IAnnouncement>
) {
  return announcementDao.delete(filter);
}

export function getAnnouncementByIdAndUpdateService(
  id: string,
  data: Partial<IAnnouncement>
) {
  return announcementDao.update({ _id: id }, data);
}

export function getAnnouncementByIdAndDeleteService(id: string) {
  return announcementDao.delete({ _id: id });
}
