import mongoose from 'mongoose';
import userDeadlineDao from '../../dao/user-deadline.dao';
import <PERSON>rror<PERSON>and<PERSON> from '../../utils/error-handler';

export const pauseUserDeadlineAdjustment = async (
  userId: string | mongoose.Types.ObjectId,
  batchId: string | mongoose.Types.ObjectId,
  pauseDays: number,
  pauseStartDate: Date
): Promise<number | null> => {
  const deadlineAdjusted = await userDeadlineDao.adjustDeadline(
    userId,
    batchId,
    pauseDays,
    pauseStartDate
  );

  if (deadlineAdjusted <= 0 || deadlineAdjusted === null) {
    throw new ErrorHandler(
      400,
      'Failed to adjust user deadline. Please check the user ID and batch ID.'
    );
  }

  return deadlineAdjusted;
};

export const resumePauseEarlyAdjustment = async (
  userId: string | mongoose.Types.ObjectId,
  batchId: string | mongoose.Types.ObjectId,
  daysToAdjustBack: number
): Promise<number | null> => {
  const deadlineAdjusted = await userDeadlineDao.adjustDeadlinesForEarlyResume(
    userId,
    batchId,
    daysToAdjustBack
  );
  if (deadlineAdjusted <= 0 || deadlineAdjusted === null) {
    throw new ErrorHandler(
      400,
      'Failed to adjust user deadline. Please check the user ID and batch ID.'
    );
  }

  return deadlineAdjusted;
};
