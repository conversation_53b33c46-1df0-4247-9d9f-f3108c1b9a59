import { Types } from 'mongoose';
import ErrorHandler from '../../utils/error-handler';
import enrolledCourseDao from '../../dao/enrolled-course.dao';
// import moduleDao from '../../dao/module.dao';
import userDeadlineDao from '../../dao/user-deadline.dao';
import userProgressDao from '../../dao/user-progress.dao';
import batchPauseDao from '../../dao/batch-pause.dao';
import { getLastViewedContent } from './resume-learning.service';
import { DeadlineCalculator } from '../../utils/deadline-calculator.util';

interface CourseModulesWithSubmodulesAndDeadlines {
  title: string;
  submodules: {
    _id: string;
    title: string;
    description: string;
    order: number;
    duration: number;
    totalPoints: number;
    isActive: boolean;
    sections: {
      lesson: boolean;
      practice: boolean;
    };
    createdAt: Date;
    updatedAt: Date;
    startDate: Date | null;
    deadline: Date | null;
    dependsOnPreviousSubmodule: boolean | null;
    isUnlocked?: boolean;
  }[];
}

export const getCourseModulesService = async (
  courseId: string,
  userId: string,
  batchId?: string
) => {
  // Check enrollment
  const enrollment = await enrolledCourseDao.findOne({
    userId: new Types.ObjectId(userId),
    courseId: new Types.ObjectId(courseId),
    ...(batchId && { batchId: new Types.ObjectId(batchId) })
  });

  if (!enrollment) {
    throw new ErrorHandler(403, 'You are not enrolled in this course');
  }

  // Get modules and user progress
  const [userProgress, userActivePause] = await Promise.all([
    // moduleDao.getModulesWithSubmodulesAndDeadlines(
    //   courseId,
    //   enrollment.batchId.toString(),
    //   userId
    // ),
    userProgressDao.findOne({
      user: new Types.ObjectId(userId),
      course: new Types.ObjectId(courseId)
    }),
    batchPauseDao.findOne({
      userId: new Types.ObjectId(userId),
      batchId: new Types.ObjectId(enrollment.batchId.toString()),
      status: 'active'
    })
  ]);
  // console.warn('modules logging from course-module service old code:');
  // modules.forEach((module, idx) => {
  //   console.warn(`Module ${idx + 1}: ${module.title}`);
  //   console.warn('Submodules:', JSON.stringify(module.submodules, null, 2)); // Pretty print submodules
  // });
  // console.warn(
  //   '------------------------------------------------------------------'
  // );

  const modules = (await DeadlineCalculator.getModulesWithCalculatedDeadlines(
    enrollment.courseId.toString(),
    enrollment.batchId.toString(),
    userId
  )) as CourseModulesWithSubmodulesAndDeadlines[];

  // console.warn('modules logging from course-module service new code:');
  // data.forEach((module, idx) => {
  //   console.warn(`Module ${idx + 1}: ${module.title}`);
  //   console.warn('Submodules:', JSON.stringify(module.submodules, null, 2));
  // });
  // console.warn(
  //   '------------------------------------------------------------------'
  // );

  // Calculate progress
  const overallProgress = userProgress?.overallProgress ?? 0;

  // Create completion map - consider both completed flag and 100% progress
  // Build completion, penalty, and lateness maps
  const completionMap = new Map<string, boolean>();
  const penaltyMap = new Map<string, number>();
  const daysLateMap = new Map<string, number>();

  userProgress?.subModuleProgress?.forEach(progress => {
    const isCompleted =
      progress.completed === true || progress.progress === 100;
    completionMap.set(progress.subModule.toString(), isCompleted);
    penaltyMap.set(progress.subModule.toString(), progress.penaltyApplied ?? 0);
    daysLateMap.set(progress.subModule.toString(), progress.daysLate ?? 0);
  });

  // Helper functions
  const isSubmoduleCompleted = (submoduleId: string): boolean => {
    return completionMap.get(submoduleId) || false;
  };

  const getSubmodulePenalty = (submoduleId: string): number => {
    return penaltyMap.get(submoduleId) || 0;
  };

  const getSubmoduleDaysLate = (submoduleId: string): number => {
    return daysLateMap.get(submoduleId) || 0;
  };

  const currentDate = new Date();

  // Process modules
  for (let moduleIndex = 0; moduleIndex < modules.length; moduleIndex++) {
    const module = modules[moduleIndex];

    for (let subIndex = 0; subIndex < module.submodules.length; subIndex++) {
      const submodule = module.submodules[subIndex];

      // @Abhishek check this for double fetching of deadlines
      // Get deadlines
      const deadlines = await userDeadlineDao.findByUserAndBatch(
        userId,
        enrollment.batchId.toString(),
        submodule._id.toString()
      );

      // Date check
      const adjustedStartDate = deadlines?.[0]?.adjustedStartDate;
      const startDatePassed = adjustedStartDate
        ? new Date(adjustedStartDate) <= currentDate
        : false;

      let isUnlocked = false;

      if (userActivePause) {
        const pauseStartDate = userActivePause.startDate;
        const pauseEndDate = userActivePause.endDate;
        const isPaused =
          pauseStartDate &&
          pauseEndDate &&
          currentDate >= pauseStartDate &&
          currentDate <= pauseEndDate;
        if (isPaused) {
          isUnlocked = false;
        }
      }

      // First submodule of first module - always unlock
      if (moduleIndex === 0 && subIndex === 0) {
        if (startDatePassed) {
          isUnlocked = true;
        } else {
          isUnlocked = false;
        }
      }
      // Other submodules within the same module
      else if (subIndex > 0) {
        const prevSubId = module.submodules[subIndex - 1]._id.toString();
        const prevCompleted = isSubmoduleCompleted(prevSubId);
        isUnlocked = prevCompleted || startDatePassed;
      }
      // First submodule of subsequent modules
      else {
        const prevModule = modules[moduleIndex - 1];
        if (prevModule.submodules && prevModule.submodules.length > 0) {
          const lastSub =
            prevModule.submodules[prevModule.submodules.length - 1];
          const lastSubId = lastSub._id.toString();
          const lastSubCompleted = isSubmoduleCompleted(lastSubId);
          isUnlocked = lastSubCompleted || startDatePassed;
        } else {
          isUnlocked = true;
        }
      }

      // Set additional properties for penalty and daysLate (content-wise)
      submodule.isUnlocked = isUnlocked;

      // Attach daysLate and penalty for the submodule
      (submodule as Record<string, unknown>).daysLate = getSubmoduleDaysLate(
        submodule._id.toString()
      );
      (submodule as Record<string, unknown>).penaltyApplied =
        getSubmodulePenalty(submodule._id.toString());

      // Attach content-wise penalty details for this submodule
    }
  }

  // Get last viewed content
  const lastViewedContent = await getLastViewedContent(userId, courseId);

  return {
    courseId,
    batchId: enrollment.batchId.toString(),
    progress: overallProgress,
    modules,
    resumeLearning: lastViewedContent
  };
};

// modules logging from course-module service old code:
// Module 1: Frontend Development
// Submodules: [
//   {
//     "_id": "6819e56d7eb5eb6cb618edba",
//     "title": "Introduction to JavaScript",
//     "description": "This submodule covers the basics of JavaScript including variables, data types, and functions.",
//     "module": "6819e56d7eb5eb6cb618edba",
//     "order": 1,
//     "duration": 45,
//     "points": 52,
//     "isActive": true,
//     "sections": {
//       "lesson": true,
//       "practice": true
//     },
//     "createdAt": "2025-05-14T12:02:40.379Z",
//     "updatedAt": "2025-05-27T11:23:21.011Z",
//     "__v": 0,
//     "startDate": "2025-05-20T00:00:00.000Z",
//     "deadline": "2025-05-27T00:00:00.000Z",
//     "dependsOnPreviousSubmodule": true
//   },
//   {
//     "_id": "6824868b17a6e3528265500b",
//     "title": "Advanced Functions in JavaScript",
//     "description": "This submodule dives into advanced concepts such as closures, higher-order functions, and callbacks.",
//     "module": "6819e56d7eb5eb6cb618edba",
//     "order": 2,
//     "duration": 45,
//     "points": 47,
//     "isActive": true,
//     "sections": {
//       "lesson": true,
//       "practice": true
//     },
//     "createdAt": "2025-05-14T12:03:23.557Z",
//     "updatedAt": "2025-05-27T11:23:21.011Z",
//     "__v": 0,
//     "startDate": "2025-05-27T00:00:00.000Z",
//     "deadline": "2025-06-03T00:00:00.000Z",
//     "dependsOnPreviousSubmodule": true
//   }
// ]
// Module 2: Backend Development
// Submodules: [
//   {
//     "_id": "682486e717a6e35282655010",
//     "title": "Node.js Event Loop & Asynchronous Patterns",
//     "description": "This submodule explains the Node.js event loop, asynchronous execution, callbacks, and promises.",
//     "module": "6819e6007eb5eb6cb618edbe",
//     "order": 1,
//     "duration": 20,
//     "points": 52,
//     "isActive": true,
//     "sections": {
//       "lesson": true,
//       "practice": true
//     },
//     "createdAt": "2025-05-14T12:04:55.919Z",
//     "updatedAt": "2025-05-22T11:26:32.408Z",
//     "__v": 0,
//     "startDate": "2025-06-03T00:00:00.000Z",
//     "deadline": "2025-06-10T00:00:00.000Z",
//     "dependsOnPreviousSubmodule": true
//   },
//   {
//     "_id": "682486f117a6e35282655015",
//     "title": "Working with File System and Streams in Node.js",
//     "description": "This submodule covers reading/writing files using the fs module and handling data streams in Node.js.",
//     "module": "6819e6007eb5eb6cb618edbe",
//     "order": 2,
//     "duration": 45,
//     "points": 47,
//     "isActive": true,
//     "sections": {
//       "lesson": true,
//       "practice": true
//     },
//     "createdAt": "2025-05-14T12:05:05.084Z",
//     "updatedAt": "2025-05-14T12:52:15.384Z",
//     "__v": 0,
//     "startDate": "2025-06-10T00:00:00.000Z",
//     "deadline": "2025-06-17T00:00:00.000Z",
//     "dependsOnPreviousSubmodule": true
//   }
// ]
// ------------------------------------------------------------------
// modules logging from course-module service new code:
// Module 1: Frontend Development
// Submodules: [
//   {
//     "_id": "6819e56d7eb5eb6cb618edba",
//     "title": "Introduction to JavaScript",
//     "description": "This submodule covers the basics of JavaScript including variables, data types, and functions.",
//     "module": "6819e56d7eb5eb6cb618edba",
//     "order": 1,
//     "duration": 45,
//     "points": 52,
//     "isActive": true,
//     "sections": {
//       "lesson": true,
//       "practice": true
//     },
//     "createdAt": "2025-05-14T12:02:40.379Z",
//     "updatedAt": "2025-05-27T11:23:21.011Z",
//     "__v": 0,
//     "userDeadlineInfo": {
//       "startDate": "2025-05-20T00:00:00.000Z",
//       "deadline": "2025-05-27T00:00:00.000Z"
//     },
//     "isExempt": false,
//     "hasCustomDates": false,
//     "pauseAdjustmentDays": 0,
//     "additionalAdjustmentDays": 0
//   },
//   {
//     "_id": "6824868b17a6e3528265500b",
//     "title": "Advanced Functions in JavaScript",
//     "description": "This submodule dives into advanced concepts such as closures, higher-order functions, and callbacks.",
//     "module": "6819e56d7eb5eb6cb618edba",
//     "order": 2,
//     "duration": 45,
//     "points": 47,
//     "isActive": true,
//     "sections": {
//       "lesson": true,
//       "practice": true
//     },
//     "createdAt": "2025-05-14T12:03:23.557Z",
//     "updatedAt": "2025-05-27T11:23:21.011Z",
//     "__v": 0,
//     "userDeadlineInfo": {
//       "startDate": "2025-05-27T00:00:00.000Z",
//       "deadline": "2025-06-03T00:00:00.000Z"
//     },
//     "isExempt": false,
//     "hasCustomDates": false,
//     "pauseAdjustmentDays": 0,
//     "additionalAdjustmentDays": 0
//   }
// ]
// Module 2: Backend Development
// Submodules: [
//   {
//     "_id": "682486e717a6e35282655010",
//     "title": "Node.js Event Loop & Asynchronous Patterns",
//     "description": "This submodule explains the Node.js event loop, asynchronous execution, callbacks, and promises.",
//     "module": "6819e6007eb5eb6cb618edbe",
//     "order": 1,
//     "duration": 20,
//     "points": 52,
//     "isActive": true,
//     "sections": {
//       "lesson": true,
//       "practice": true
//     },
//     "createdAt": "2025-05-14T12:04:55.919Z",
//     "updatedAt": "2025-05-22T11:26:32.408Z",
//     "__v": 0,
//     "userDeadlineInfo": {
//       "startDate": "2025-06-03T00:00:00.000Z",
//       "deadline": "2025-06-10T00:00:00.000Z"
//     },
//     "isExempt": false,
//     "hasCustomDates": false,
//     "pauseAdjustmentDays": 0,
//     "additionalAdjustmentDays": 0
//   },
//   {
//     "_id": "682486f117a6e35282655015",
//     "title": "Working with File System and Streams in Node.js",
//     "description": "This submodule covers reading/writing files using the fs module and handling data streams in Node.js.",
//     "module": "6819e6007eb5eb6cb618edbe",
//     "order": 2,
//     "duration": 45,
//     "points": 47,
//     "isActive": true,
//     "sections": {
//       "lesson": true,
//       "practice": true
//     },
//     "createdAt": "2025-05-14T12:05:05.084Z",
//     "updatedAt": "2025-05-14T12:52:15.384Z",
//     "__v": 0,
//     "userDeadlineInfo": {
//       "startDate": "2025-06-10T00:00:00.000Z",
//       "deadline": "2025-06-17T00:00:00.000Z"
//     },
//     "isExempt": false,
//     "hasCustomDates": false,
//     "pauseAdjustmentDays": 0,
//     "additionalAdjustmentDays": 0
//   }
// ]
