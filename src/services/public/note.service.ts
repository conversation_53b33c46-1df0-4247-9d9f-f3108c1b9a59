import { Types } from 'mongoose';
import noteDao from '../../dao/note.dao';
import userProgressDao from '../../dao/user-progress.dao';
import submoduleContentDao from '../../dao/submodule-content.dao';
import <PERSON>rrorHandler from '../../utils/error-handler';
import { ProgressUpdateService } from './progress-update.service';
/**
 * Get note with user progress information
 */
export const getNoteService = async (
  noteId: string,
  userId: string,
  subModuleId: string
): Promise<unknown> => {
  // Fetch note data and submodule content in parallel
  const note = await noteDao.findById(noteId);

  if (!note) {
    throw new ErrorHandler(404, 'Note not found');
  }

  // Fetch user's progress for this note if exists
  const userProgress = await userProgressDao.findOneWithSelection(
    {
      user: new Types.ObjectId(userId),
      'contentItemProgress.contentId': new Types.ObjectId(noteId),
      'contentItemProgress.subModuleId': new Types.ObjectId(subModuleId)
    },
    {
      contentItemProgress: {
        $elemMatch: {
          contentId: new Types.ObjectId(noteId),
          subModuleId: new Types.ObjectId(subModuleId)
        }
      }
    }
  );

  return {
    ...note,
    progress: userProgress?.contentItemProgress?.[0] || null
  };
};

/**
 * Mark a note as completed and update user progress
 */
export const markNoteCompletedService = async (
  noteId: string,
  userId: string,
  subModuleId: string,
  batchId: string
): Promise<{
  userProgress: {
    noteId: string;
    completed: boolean;
    pointsEarned: number;
  };
  message: string;
}> => {
  try {
    // Fetch user's progress for this note if exists
    const userProgress = await userProgressDao.findOneWithSelection(
      {
        user: new Types.ObjectId(userId),
        'contentItemProgress.contentId': new Types.ObjectId(noteId),
        'contentItemProgress.subModuleId': new Types.ObjectId(subModuleId)
      },
      {
        contentItemProgress: {
          $elemMatch: {
            contentId: new Types.ObjectId(noteId),
            subModuleId: new Types.ObjectId(subModuleId)
          }
        }
      }
    );
    if (userProgress) {
      // Check if the note is already marked as completed
      const progress = userProgress.contentItemProgress[0];
      if (progress && progress.completed) {
        return {
          userProgress: {
            noteId,
            completed: true,
            pointsEarned: progress.pointsEarned
          },
          message: 'Note already marked as completed'
        };
      }
    }
    // Fetch note data and submodule content in parallel
    const [note, submoduleContent] = await Promise.all([
      noteDao.findById(noteId),
      submoduleContentDao.findOne({
        contentId: new Types.ObjectId(noteId),
        contentType: 'note',
        submodule: new Types.ObjectId(subModuleId)
      })
    ]);

    if (!note) {
      throw new ErrorHandler(404, 'Note not found');
    }

    if (!submoduleContent) {
      throw new ErrorHandler(404, 'Note is not linked to any submodule');
    }
    const result = await ProgressUpdateService.updateNoteProgress({
      userId,
      contentId: noteId,
      contentType: 'note',
      subModuleId,
      batchId,
      originalPoints: note.points,
      completed: true
    });
    if (!result) {
      throw new ErrorHandler(500, 'Failed to update user progress');
    }

    return {
      userProgress: {
        noteId,
        completed: result.completed,
        pointsEarned: result.pointsEarned
      },
      message: 'Note marked as completed'
    };
  } catch (error) {
    console.error('Error marking note as completed:', error);
    if (error instanceof ErrorHandler) {
      throw error;
    }
    throw new ErrorHandler(
      500,
      `Failed to mark note as completed: ${(error as Error).message || 'Unknown error'}`
    );
  }
};
