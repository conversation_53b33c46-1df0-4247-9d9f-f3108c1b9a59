import { Types } from 'mongoose';
import codingProblemDao from '../../dao/coding-problem.dao';
import userProgressDao from '../../dao/user-progress.dao';
import Error<PERSON>andler from '../../utils/error-handler';
import submissionStorageDao from '../../dao/submission-storage.dao';
import { CodeExecutionService } from './code-execution.service';
import {
  ISubmissionResult,
  ITestResult
} from '../../interfaces/code-execution.interface';
import { ISubmissionStorage } from '../../interfaces/submission-storage.interface';
import {
  ICodeSubmission,
  TestCase
} from '../../interfaces/code-submission.interface'; // Added TestCase
import codeSubmissionDao from '../../dao/code-submission.dao';
import { ProgressUpdateService } from './progress-update.service';
import submoduleContentDao from '../../dao/submodule-content.dao';

// Define interface for solution objects
export interface ICodingProblemSolution {
  language: string;
  code: string;
  editorial?: string;
  video?: string;
}

export const getCodingProblemService = async (
  problemId: string,
  userId: string,
  subModuleId: string
) => {
  // Fetch coding problem data
  const codingProblem = await codingProblemDao.findById(problemId);

  if (!codingProblem) {
    throw new ErrorHandler(404, 'Coding problem not found');
  }

  // For security, hide some test cases that are marked as hidden
  codingProblem.testCases = codingProblem.testCases.map(testCase => {
    if (testCase.isHidden) {
      return {
        ...testCase,
        input: testCase.input, // Keep input visible
        expectedOutput: 'hidden', // Hide expected output for hidden test cases
        points: testCase.points
      };
    }
    return testCase;
  });

  codingProblem.solutions = undefined; // Hide solutions for security

  // Get user's progress if any
  const userProgress = await userProgressDao.findOneWithSelection(
    {
      user: new Types.ObjectId(userId),
      'contentItemProgress.contentId': new Types.ObjectId(problemId),
      'contentItemProgress.subModuleId': new Types.ObjectId(subModuleId)
    },
    {
      contentItemProgress: {
        $elemMatch: {
          contentId: new Types.ObjectId(problemId),
          subModuleId: new Types.ObjectId(subModuleId)
        }
      }
    }
  );

  // Get user's last submission if any
  const submissionStorage = await submissionStorageDao.findOne(
    {
      userId: new Types.ObjectId(userId),
      questionId: new Types.ObjectId(problemId)
    },
    ['submissionId'] // Populate submissionId field
  ); // submissionStorage can be null if not found

  let lastSubmissionsToReturn: Array<{
    date: Date;
    language: string;
    code: string;
    testcases: Array<{
      passed: boolean;
      executionTime: number;
      memoryUsage: number;
    }>;
  }> | null = null;

  if (
    submissionStorage &&
    submissionStorage.submissionId &&
    Array.isArray(submissionStorage.submissionId) &&
    submissionStorage.submissionId.length > 0
  ) {
    // After population, submissionStorage.submissionId should be an array of ICodeSubmission objects.
    const populatedSubmissions =
      submissionStorage.submissionId as ICodeSubmission[];

    lastSubmissionsToReturn = populatedSubmissions
      .map((submission: ICodeSubmission) => {
        // Ensure submission is a valid, populated object.
        if (
          typeof submission === 'object' &&
          submission !== null &&
          submission.date !== undefined
        ) {
          return {
            date: submission.date,
            language: submission.language,
            code: submission.code,
            testcases: submission.testcases.map((testcase: TestCase) => ({
              // Explicitly type testcase
              passed: testcase.passed,
              executionTime: testcase.executionTime,
              memoryUsage: testcase.memoryUsage
            }))
          };
        }
        return null; // Should not be reached if populatedSubmissions are all valid ICodeSubmission objects
      })
      .filter(
        (
          s
        ): s is {
          date: Date;
          language: string;
          code: string;
          testcases: Array<{
            passed: boolean;
            executionTime: number;
            memoryUsage: number;
          }>;
        } => s !== null
      ); // Type guard to filter out nulls

    if (lastSubmissionsToReturn.length === 0) {
      lastSubmissionsToReturn = null;
    }
  }

  return {
    ...codingProblem,
    progress: userProgress ? userProgress.contentItemProgress[0] : null,
    lastSubmissions: lastSubmissionsToReturn // Include the processed submissions
  };
};

/**
 * Run code for a coding problem (without saving progress)
 */
export const runCodingProblemService = async (
  problemId: string,
  userId: string,
  code: string,
  language: string,
  customTestCases?: Array<{ input: string; expectedOutput: string }>
): Promise<{
  testResults: ITestResult[];
  customTestResults?: ITestResult[];
}> => {
  // Fetch coding problem data
  const codingProblem = await codingProblemDao.findById(problemId);

  if (!codingProblem) {
    throw new ErrorHandler(404, 'Coding problem not found');
  }

  // Check if the language is supported
  const boilerplate = codingProblem.boilerplate.find(
    boilerplate => boilerplate.language === language
  );
  if (!boilerplate) {
    throw new ErrorHandler(400, 'Language not supported');
  }
  const mainCode = boilerplate.mainCode;

  // Filter for only sample test cases (non-hidden)
  const sampleTestCases = codingProblem.testCases.filter(tc => !tc.isHidden);

  // Execute code against sample test cases
  const testResults: ITestResult[] = [];

  for (const testCase of sampleTestCases) {
    try {
      const executionResult = await CodeExecutionService.executeCode(
        code,
        language,
        mainCode,
        testCase.input,
        testCase.expectedOutput
      );

      // Compare the output with expected output (normalized)
      const normalizedActualOutput = executionResult.output.trim();
      const normalizedExpectedOutput = testCase.expectedOutput.trim();
      const passed = normalizedActualOutput === normalizedExpectedOutput;

      testResults.push({
        passed,
        input: testCase.input,
        expectedOutput: testCase.expectedOutput,
        actualOutput: executionResult.output || executionResult.error || 'null',
        executionTime: executionResult.executionTime,
        memoryUsage: executionResult.memoryUsage
      });
    } catch (error) {
      // Handle execution errors
      testResults.push({
        passed: false,
        input: testCase.input,
        expectedOutput: testCase.expectedOutput,
        actualOutput: `Error: ${error instanceof Error ? error.message : 'Execution failed'}`
      });
    }
  }

  // Process custom test cases if provided
  const customTestResults: ITestResult[] = [];

  if (customTestCases && customTestCases.length > 0) {
    for (const testCase of customTestCases) {
      try {
        const executionResult = await CodeExecutionService.executeCode(
          code,
          language,
          mainCode,
          testCase.input,
          testCase.expectedOutput
        );

        // Compare the output with expected output (normalized)
        const normalizedActualOutput = executionResult.output.trim();
        const normalizedExpectedOutput = testCase.expectedOutput.trim();
        const passed = normalizedActualOutput === normalizedExpectedOutput;

        customTestResults.push({
          passed,
          input: testCase.input,
          expectedOutput: testCase.expectedOutput,
          actualOutput: executionResult.output,
          executionTime: executionResult.executionTime,
          memoryUsage: executionResult.memoryUsage,
          isCustom: true // Mark as custom test case
        });
      } catch (error) {
        // Handle execution errors
        customTestResults.push({
          passed: false,
          input: testCase.input,
          expectedOutput: testCase.expectedOutput,
          actualOutput: `Error: ${error instanceof Error ? error.message : 'Execution failed'}`,
          isCustom: true // Mark as custom test case
        });
      }
    }
  }

  return {
    testResults,
    ...(customTestResults.length > 0 ? { customTestResults } : {})
  };
};

/**
 * Submit code for a coding problem (with progress tracking)
 */
export const submitCodingProblemSolutionService = async (
  problemId: string,
  userId: string,
  code: string,
  language: string,
  subModuleId: string
): Promise<ISubmissionResult> => {
  // Check if coding problem exists
  const [codingProblem, subModuleContent] = await Promise.all([
    codingProblemDao.findById(problemId),
    submoduleContentDao.findOne({
      contentId: new Types.ObjectId(problemId),
      contentType: 'coding_problem',
      submodule: new Types.ObjectId(subModuleId)
    })
  ]);

  if (!codingProblem) {
    throw new ErrorHandler(404, 'Coding problem not found');
  }

  if (!subModuleContent) {
    throw new ErrorHandler(
      404,
      'Coding problem is not linked to any submodule'
    );
  }

  // Check if the language is supported
  const boilerplate = codingProblem.boilerplate.find(
    bp => bp.language === language
  );
  if (!boilerplate) {
    throw new ErrorHandler(400, 'Language not supported');
  }
  const mainCode = boilerplate.mainCode;

  // Check if user has seen the solution
  const submissionStorage = await submissionStorageDao.findOne({
    userId: new Types.ObjectId(userId),
    questionId: new Types.ObjectId(problemId)
  });

  const hasSeenSolution = submissionStorage?.hasSeenSolution === true;

  try {
    // Execute all test cases in a batch
    const testCases = codingProblem.testCases.map(tc => ({
      input: tc.input,
      expectedOutput: tc.expectedOutput,
      isHidden: tc.isHidden,
      points: tc.points
    }));

    const batchResults = await CodeExecutionService.executeBatch(
      code,
      language,
      mainCode,
      testCases
    );

    // Process the results
    const testResults: ITestResult[] = [];
    let totalPoints = 0;
    let allTestsPassed = true;

    batchResults.results.forEach((result, index) => {
      const testCase = codingProblem.testCases[index];
      const normalizedActualOutput = result.output.trim();
      const normalizedExpectedOutput = testCase.expectedOutput.trim();
      const passed = normalizedActualOutput === normalizedExpectedOutput;

      // Only award points if the user hasn't seen the solution
      const pointsEarned = passed && !hasSeenSolution ? testCase.points : 0;
      totalPoints += pointsEarned;

      if (!passed) {
        allTestsPassed = false;
      }

      testResults.push({
        passed,
        input: testCase.input,
        expectedOutput: testCase.isHidden ? 'hidden' : testCase.expectedOutput,
        actualOutput: result.output,
        pointsEarned,
        executionTime: result.executionTime,
        memoryUsage: result.memoryUsage
      });
    });

    // Create or update submission storage
    const status = allTestsPassed ? 'completed' : 'failed';
    const updatedSubmissionStorage = await upsertSubmissionStorage(
      userId,
      problemId,
      code,
      language,
      status
    );

    // Create a code submission record
    const submission = await createCodeSubmission(
      testResults,
      language,
      code,
      updatedSubmissionStorage._id.toString()
    );

    // Update submission storage with the new submission ID
    await submissionStorageDao.update(
      {
        _id: updatedSubmissionStorage._id
      },
      {
        $push: { submissionId: submission._id }
      }
    );

    // Only update progress if user hasn't seen solution
    if (!hasSeenSolution) {
      // Update progress using ProgressUpdateService with submission ID
      await ProgressUpdateService.updateCodingProgress({
        userId,
        contentId: problemId,
        contentType: 'coding_problem',
        subModuleId,
        code,
        language,
        testCaseResults: testResults.map(result => ({
          testCaseId: result.testCaseId || '',
          passed: result.passed,
          output: result.actualOutput,
          pointsEarned: result.pointsEarned || 0
        })),
        originalPoints: codingProblem.points,
        submissionId: submission._id.toString() // Pass the submission ID to store in user progress
      });
    }

    // Return submission results with flag indicating if points were awarded
    return {
      problemId,
      allTestsPassed,
      totalPoints,
      maxPoints: codingProblem.points,
      testResults,
      hasSeenSolution // Include this flag so frontend knows why points might be zero
    };
  } catch (error) {
    // Create a failed submission if code execution errors out completely
    await upsertSubmissionStorage(userId, problemId, code, language, 'failed');

    throw error;
  }
};

/**
 * Get solution for a coding problem and mark user as having seen the solution
 */
export const getSolutionService = async (
  problemId: string,
  userId: string,
  language?: string
): Promise<{ code: string }> => {
  // Check if coding problem exists
  const codingProblem = await codingProblemDao.findById(problemId);

  if (!codingProblem) {
    throw new ErrorHandler(404, 'Coding problem not found');
  }

  if (!codingProblem.solutions || codingProblem.solutions.length === 0) {
    throw new ErrorHandler(404, 'No solutions available for this problem');
  }

  // Mark that the user has seen the solution
  await upsertSubmissionStorage(
    userId,
    problemId,
    undefined,
    language,
    null,
    true
  );

  // Filter solutions by language if specified
  let filteredSolutions = codingProblem.solutions as ICodingProblemSolution[];

  if (language) {
    filteredSolutions = filteredSolutions.filter(
      solution => solution.language === language
    );

    // If no solutions for the requested language
    if (filteredSolutions.length === 0) {
      throw new ErrorHandler(
        404,
        `No solution available for ${language} language`
      );
    }
  }

  // Return the solutions
  return {
    code: filteredSolutions[0].code
  };
};

/**
 * Helper function to create or update submission storage
 */
async function upsertSubmissionStorage(
  userId: string,
  problemId: string,
  code: string = ' ',
  language: string = '',
  status: string | null,
  hasSeenSolution?: boolean
): Promise<ISubmissionStorage> {
  const filter = {
    userId: new Types.ObjectId(userId),
    questionId: new Types.ObjectId(problemId)
  };

  const updateData: Partial<ISubmissionStorage> = {
    date: new Date()
  };

  // Only update code and language if they're provided
  if (code && language) {
    updateData.code = code;
    updateData.language = language;
  }

  if (status) {
    updateData.status = status;
  }

  if (hasSeenSolution !== undefined) {
    updateData.hasSeenSolution = hasSeenSolution;
  }

  // Find existing submission storage
  const existingStorage = await submissionStorageDao.findOne(filter);

  if (existingStorage) {
    // Update existing
    await submissionStorageDao.update({ _id: existingStorage._id }, updateData);
    return {
      ...existingStorage,
      ...updateData
    } as ISubmissionStorage;
  } else {
    // Create new
    return await submissionStorageDao.create({
      ...filter,
      ...updateData,
      code: code,
      language: language,
      hasSeenSolution: hasSeenSolution || false
    });
  }
}

/**
 * Helper function to create a code submission record
 */
async function createCodeSubmission(
  testResults: ITestResult[],
  language: string,
  code: string,
  storageId: string
): Promise<ICodeSubmission> {
  const testcases = testResults.map(result => ({
    stdin: result.input,
    stdout: result.actualOutput,
    expectedOutput: result.expectedOutput,
    passed: result.passed,
    error: null,
    executionTime: result.executionTime || 0,
    memoryUsage: result.memoryUsage || 0
  }));

  return await codeSubmissionDao.create({
    testcases,
    language,
    code,
    date: new Date(),
    submissionStorageId: new Types.ObjectId(storageId)
  });
}
