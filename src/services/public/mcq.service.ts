import { Types } from 'mongoose';
import mcqDao from '../../dao/mcq.dao';
import userProgressDao from '../../dao/user-progress.dao';
import submoduleContentDao from '../../dao/submodule-content.dao';
import <PERSON><PERSON>r<PERSON>and<PERSON> from '../../utils/error-handler';
import { IMCQ } from '../../interfaces/mcq.interface';
import { ProgressUpdateService } from './progress-update.service';
import { config } from '../../config/config';

/**
 * Get MCQ with user progress information
 */
export const getMCQService = async (
  mcqId: string,
  userId: string,
  subModuleId: string
): Promise<unknown> => {
  // Fetch MCQ data
  const mcqData = (await mcqDao.findById(mcqId)) as IMCQ;

  if (!mcqData) {
    throw new ErrorHandler(404, 'MCQ not found');
  }

  // For security, remove correct answers from options if user hasn't completed it
  const userProgress = await userProgressDao.findOneWithSelection(
    {
      user: new Types.ObjectId(userId),
      'contentItemProgress.contentId': new Types.ObjectId(mcqId),
      'contentItemProgress.subModuleId': new Types.ObjectId(subModuleId)
    },
    {
      contentItemProgress: {
        $elemMatch: {
          contentId: new Types.ObjectId(mcqId),
          subModuleId: new Types.ObjectId(subModuleId)
        }
      }
    }
  );
  // Hide correct answers from the user
  mcqData.options = mcqData.options.map(option => ({
    ...option,
    isCorrect: undefined
  }));
  mcqData.explanation = undefined;

  // Get current attempts and attempt limit
  const currentAttempts = userProgress?.contentItemProgress?.[0]?.attempts || 0;
  const maxAttempts = config.mcqAttemptsLimit;
  const attemptsRemaining = Math.max(0, maxAttempts - currentAttempts);

  return {
    ...mcqData,
    progress: userProgress?.contentItemProgress[0] || null,
    attemptsRemaining,
    maxAttempts
  };
};

/**
 * Submit MCQ answer and update user progress
 */
export const submitMCQAnswerService = async (
  mcqId: string,
  userId: string,
  subModuleId: string,
  selectedOptions: string[]
): Promise<{
  submissionResult: {
    mcqId: string;
    isCorrect: boolean;
    pointsEarned: number;
    selectedOptions: string[];
    attemptsRemaining: number;
  };
  message: string;
}> => {
  try {
    const [mcq, submoduleContent] = await Promise.all([
      mcqDao.findById(mcqId),
      submoduleContentDao.findOne({
        contentId: new Types.ObjectId(mcqId),
        contentType: 'mcq',
        submodule: new Types.ObjectId(subModuleId)
      })
    ]);
    if (!mcq) {
      throw new ErrorHandler(404, 'MCQ not found');
    }

    if (!submoduleContent) {
      throw new ErrorHandler(404, 'MCQ is not linked to any submodule');
    }

    // Get correct options
    const correctOptionTexts = mcq.options
      .filter(o => o.isCorrect)
      .map(o => o.text);

    // Check if answer is correct for response formatting
    const allCorrectSelected = correctOptionTexts.every(text =>
      selectedOptions.includes(text)
    );
    const noIncorrectSelected = selectedOptions.every(text =>
      mcq.options.some(opt => opt.text === text && opt.isCorrect)
    );
    const isCorrect = allCorrectSelected && noIncorrectSelected;
    // Get current attempts if user has attempted this MCQ before
    const userProgress = await userProgressDao.findOneWithSelection(
      {
        user: new Types.ObjectId(userId),
        'contentItemProgress.contentId': new Types.ObjectId(mcqId),
        'contentItemProgress.subModuleId': new Types.ObjectId(subModuleId)
      },
      {
        contentItemProgress: {
          $elemMatch: {
            contentId: new Types.ObjectId(mcqId),
            subModuleId: new Types.ObjectId(subModuleId)
          }
        }
      }
    );
    // Check if user has attempted this MCQ before and its completed
    if (userProgress) {
      const progress = userProgress.contentItemProgress[0];
      if (progress && progress.completed) {
        return {
          submissionResult: {
            mcqId,
            isCorrect,
            pointsEarned: progress.pointsEarned,
            selectedOptions,
            attemptsRemaining: progress.attempts || 0
          },
          message: 'MCQ already completed'
        };
      }
    }
    const currentAttempts =
      userProgress?.contentItemProgress?.[0]?.attempts || 0;

    const previousPointsEarned =
      userProgress?.contentItemProgress?.[0]?.pointsEarned || 0;
    // Check if user has exceeded the maximum allowed attempts
    if (currentAttempts >= config.mcqAttemptsLimit) {
      throw new ErrorHandler(
        400,
        `Maximum attempt limit (${config.mcqAttemptsLimit}) reached for this MCQ`
      );
    }

    // Calculate points earned
    let pointsEarned = 0;
    if (isCorrect) {
      // Full points if everything is correct
      pointsEarned = mcq.points;
    } else if (mcq.selectionType === 'multiple') {
      // For multiple-select questions, award partial credit if at least one correct option is selected
      const correctOptionsSelected = selectedOptions.filter(option =>
        correctOptionTexts.includes(option)
      );

      if (correctOptionsSelected.length > 0) {
        // Award half points for partial correct answers
        pointsEarned = Math.floor(mcq.points / 2);
      }
    }
    // Only update progress if new points are higher than previous points
    if (pointsEarned >= previousPointsEarned) {
      // Update progress using centralized service
      await ProgressUpdateService.updateMCQProgress({
        userId,
        contentId: mcqId,
        contentType: 'mcq',
        subModuleId,
        selectedOptions,
        correctOptions: correctOptionTexts,
        originalPoints: mcq.points,
        currentAttempts,
        pointsEarned // Pass the calculated points
      });
    } else {
      // Use previous points since we're not updating
      pointsEarned = previousPointsEarned;
    }

    return {
      submissionResult: {
        mcqId,
        isCorrect,
        pointsEarned,
        selectedOptions,
        attemptsRemaining: config.mcqAttemptsLimit - currentAttempts - 1
      },
      message: 'MCQ answer submitted successfully'
    };
  } catch (error) {
    console.error('Error submitting MCQ answer:', error);
    if (error instanceof ErrorHandler) throw error;
    throw new ErrorHandler(
      500,
      `Failed to submit MCQ answer: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};
