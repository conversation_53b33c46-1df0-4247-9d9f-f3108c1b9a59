import certificateDao from '../../dao/certificate.dao';

/**
 * Verify a credential by ID and return LinkedIn-style credential details
 */
export const verifyCredentialService = async (
  credentialId: string
): Promise<object | string | null> => {
  // Find the certificate by ID
  const certificate = await certificateDao.findByCertificateId(credentialId);

  if (!certificate) {
    return null;
  }

  // Get additional details

  // Return a LinkedIn-style credential object
  return certificate;
};

export const getDownloadLinkOfCertificate = async (
  certificateId: string
): Promise<string | null> => {
  // Find the certificate by ID
  const certificate = await certificateDao.findByCertificateId(certificateId);

  if (!certificate) {
    return null;
  }

  // Get additional details

  // Return a LinkedIn-style credential object
  return certificate.certificateUrl;
};
