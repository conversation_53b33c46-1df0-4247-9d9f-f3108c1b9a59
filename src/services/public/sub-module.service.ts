import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../utils/error-handler';
import SubModule from '../../models/submodule.model';
// interface ContentProgress {
//   contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab';
//   contentId: Types.ObjectId;
//   contentModel: 'Video' | 'Note' | 'MCQ' | 'CodingProblem' | 'CodingLab';
//   subModuleId: Types.ObjectId | string;
//   completed: boolean;
//   pointsEarned: number;
//   completedAt?: Date;

//   // For video content
//   watchedDuration?: number;

//   // For coding labs and problems
//   submissions?: {
//     code: string;
//     language: string;
//     submittedAt: Date;
//     testCaseResults: {
//       testCaseId: string;
//       passed: boolean;
//       output: string;
//       pointsEarned: number;
//     }[];
//     totalPoints: number;
//     originalPoints: number;
//     penaltyApplied: number;
//     daysLate: number;
//   }[];

//   // For MCQs
//   selectedOptions?: string[];
//   attempts?: number;

//   // Common fields for penalty tracking
//   originalPoints: number;
//   penaltyApplied: number;
//   daysLate: number;
// }

// interface ProgressMetrics {
//   videoTotal: number;
//   videoCompleted: number;
//   codingTotal: number;
//   codingAttempted: number;
//   mcqTotal: number;
//   mcqAttempted: number;
//   totalPossiblePoints: number;
//   earnedPoints: number;
//   percentComplete: number;
// }

// interface ContentWithProgress {
//   contentId: string;
//   title: string;
//   type: string;
//   order: number;
//   points: number;
//   section: string;
//   progress: {
//     completed: boolean;
//     pointsEarned: number;
//     completedAt?: Date;
//   } | null;
// }

// interface OrganizedContent {
//   lessons: ContentWithProgress[];
//   practice: ContentWithProgress[];
// }

export const getSubmoduleContentServicee = async (
  submoduleId: string,
  userId: string
) => {
  try {
    // Use the SubModule static method to fetch all data in one query
    return await SubModule.getContentWithProgress(submoduleId, userId);
  } catch (error) {
    if (error instanceof ErrorHandler) {
      throw error;
    }
    throw new ErrorHandler(500, 'Error fetching submodule content');
  }
};

// export const getSubmoduleContentService = async (
//   submoduleId: string,
//   userId: string
// ) => {
//   // Validate submodule exists
//   const submodule = await SubModule.findById(submoduleId).select(
//     'title description module order'
//   );

//   if (!submodule) {
//     throw new ErrorHandler(404, 'Submodule not found');
//   }

//   // 1. Fetch all submodule content with populated content items
//   const submoduleContents: ISubmoduleContent[] =
//     await submoduleContentDao.findAllBySubmodule(submoduleId);

//   // 2. Get user progress for this submodule
//   const userProgress = await userProgressDao.findOne({
//     user: new Types.ObjectId(userId),
//     'subModuleProgress.subModule': new Types.ObjectId(submoduleId)
//   });

//   // 3. Extract content progress information
//   const contentIdsInSubmodule = submoduleContents
//     .map(content => {
//       if (typeof content.contentId === 'object' && '_id' in content.contentId) {
//         return String(content.contentId._id);
//       }
//       return null;
//     })
//     .filter(id => id !== null);

//   // Filter user progress to only include items in this submodule
//   const contentProgress: ContentProgress[] = userProgress
//     ? userProgress.contentItemProgress.filter(item =>
//         contentIdsInSubmodule.includes(item.contentId.toString())
//       )
//     : [];

//   // 4. Calculate progress metrics
//   const progressMetrics = calculateProgressMetrics(
//     submoduleContents,
//     contentProgress,
//     submoduleId
//   );

//   // 5. Organize content by categories (lessons and practice)
//   const organizedContent = organizeContentByCategory(
//     submoduleContents,
//     contentProgress
//   );

//   // 6. Construct the final response
//   return {
//     submoduleId: submodule._id,
//     title: submodule.title,
//     description: submodule.description,
//     moduleId: submodule.module,
//     order: submodule.order,
//     progress: progressMetrics.percentComplete,
//     progressDetails: {
//       videoProgress: {
//         completed: progressMetrics.videoCompleted,
//         total: progressMetrics.videoTotal
//       },
//       codingProblemsProgress: {
//         attempted: progressMetrics.codingAttempted,
//         total: progressMetrics.codingTotal
//       },
//       mcqsProgress: {
//         attempted: progressMetrics.mcqAttempted,
//         total: progressMetrics.mcqTotal
//       },
//       overallScore: {
//         userPoints: progressMetrics.earnedPoints,
//         totalPoints: progressMetrics.totalPossiblePoints
//       }
//     },
//     content: organizedContent
//   };
// };

// const calculateProgressMetrics = (
//   submoduleContents: ISubmoduleContent[],
//   contentProgress: ContentProgress[],
//   subModuleId: string
// ): ProgressMetrics => {
//   let videoTotal = 0;
//   let videoCompleted = 0;
//   let codingTotal = 0;
//   let codingAttempted = 0;
//   let mcqTotal = 0;
//   let mcqAttempted = 0;
//   let totalPossiblePoints = 0;
//   let earnedPoints = 0;

//   // Build a map for faster lookups of user progress
//   const progressMap: Record<string, ContentProgress> = {};
//   contentProgress.forEach(item => {
//     // Only include progress records that match this submodule context
//     if (item.subModuleId?.toString() === subModuleId) {
//       progressMap[item.contentId.toString()] = item;
//     }
//   });

//   // Calculate metrics
//   submoduleContents.forEach(content => {
//     const contentType = content.contentType;
//     const contentId =
//       typeof content.contentId === 'object' && '_id' in content.contentId
//         ? String(content.contentId._id)
//         : '';
//     const points =
//       typeof content.contentId === 'object' && 'points' in content.contentId
//         ? content.contentId.points || 0
//         : 0;
//     totalPossiblePoints += points;

//     // Check if there's progress for this content
//     const progress = progressMap[contentId];
//     if (progress) {
//       earnedPoints += progress.pointsEarned || 0;
//     }

//     // Count by content type
//     if (contentType === 'video') {
//       videoTotal++;
//       if (progress?.completed) videoCompleted++;
//     } else if (contentType === 'coding_problem') {
//       codingTotal++;
//       if (progress) codingAttempted++;
//     } else if (contentType === 'mcq') {
//       mcqTotal++;
//       if (progress) mcqAttempted++;
//     }
//   });

//   // Calculate overall percentage
//   const percentComplete =
//     totalPossiblePoints > 0
//       ? Math.round((earnedPoints / totalPossiblePoints) * 100)
//       : 0;

//   return {
//     videoTotal,
//     videoCompleted,
//     codingTotal,
//     codingAttempted,
//     mcqTotal,
//     mcqAttempted,
//     totalPossiblePoints,
//     earnedPoints,
//     percentComplete
//   };
// };

// // Helper function with proper types
// const organizeContentByCategory = (
//   submoduleContents: ISubmoduleContent[],
//   contentProgress: ContentProgress[]
// ): OrganizedContent => {
//   // Build a progress lookup map
//   const progressMap: Record<string, ContentProgress> = {};
//   contentProgress.forEach(item => {
//     progressMap[item.contentId.toString()] = item;
//   });

//   // Categorize content
//   const lessons: ContentWithProgress[] = [];
//   const practice: ContentWithProgress[] = [];

//   submoduleContents.forEach(content => {
//     const contentId =
//       typeof content.contentId === 'object' && '_id' in content.contentId
//         ? String(content.contentId._id)
//         : '';
//     const progress = progressMap[contentId] || null;

//     // Extract content data with progress
//     const contentWithProgress: ContentWithProgress = {
//       contentId: contentId,
//       title:
//         typeof content.contentId === 'object' && 'title' in content.contentId
//           ? content.contentId.title
//           : '',
//       type: content.contentType,
//       order: content.order,
//       points:
//         typeof content.contentId === 'object' && 'points' in content.contentId
//           ? content.contentId.points || 0
//           : 0,
//       section: content.section || 'main',
//       progress: progress
//         ? {
//             completed: progress.completed || false,
//             pointsEarned: progress.pointsEarned || 0,
//             completedAt: progress.completedAt
//           }
//         : null
//     };

//     // Add to appropriate category
//     if (['video', 'note', 'mcq'].includes(content.contentType)) {
//       lessons.push(contentWithProgress);
//     } else {
//       practice.push(contentWithProgress);
//     }
//   });

//   // Sort by order
//   lessons.sort((a, b) => a.order - b.order);
//   practice.sort((a, b) => a.order - b.order);

//   return { lessons, practice };
// };
