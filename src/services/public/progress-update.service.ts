import { Types } from 'mongoose';
import UserProgress from '../../models/user-progress.model';
import submoduleDao from '../../dao/submodule.dao';
import ErrorHandler from '../../utils/error-handler';
import { IUserProgress } from '../../interfaces/user-progress.interface';
import { ISubModule } from '../../interfaces/submodule.interface';
import submoduleContentDao from '../../dao/submodule-content.dao';
import EnrolledCourseLinkDao from '../../dao/enrolled-course.dao';
import { ISubmoduleContent } from '../../interfaces/submodule-content.interface';
import moduleDao from '../../dao/module.dao';
import {
  createCertificateService,
  getCertificateEligibilityService
} from '../private/certificate.service';
import { getUserById } from '../server/user.service';
import { IUser } from '../../interfaces/user.interface';
import { getCourseDetails } from '../server/course.service';
import { getBatchDetails } from '../server/batch.service';
import { upsertLeaderboardRankings } from './leaderboard.service';
import UserDeadline from '../../models/user-deadline.model';
import BatchSubmoduleDeadline from '../../models/batch-submodule-deadline.model';
import { config } from '../../config/config';

// Define strict types for content types
export type ContentType =
  | 'video'
  | 'note'
  | 'mcq'
  | 'coding_problem'
  | 'coding_lab';

export interface ContentProgressData {
  contentId: string;
  contentType: ContentType;
  subModuleId: string; // Add this field to include submodule context in the response
  completed: boolean;
  pointsEarned: number;
}

// Base update options interface`
export interface ProgressUpdateOptions {
  userId: string;
  contentId: string;
  contentType: ContentType;
  subModuleId: string;
  batchId?: string;
  completed?: boolean;
  points?: number;
  originalPoints?: number;
}

// Type-specific update interfaces
export interface VideoProgressOptions extends ProgressUpdateOptions {
  contentType: 'video';
  watchedDuration: number;
  videoDuration: number;
}

export interface NoteProgressOptions extends ProgressUpdateOptions {
  contentType: 'note';
}

export interface MCQProgressOptions extends ProgressUpdateOptions {
  contentType: 'mcq';
  selectedOptions: string[];
  attempts: number;
  pointsEarned?: number; // Add optional pointsEarned for partial credit
}

export interface CodingSubmissionOptions extends ProgressUpdateOptions {
  contentType: 'coding_problem' | 'coding_lab';
  code: string;
  language: string;
  testCaseResults: Array<{
    testCaseId: string;
    passed: boolean;
    output: string;
    pointsEarned: number;
  }>;
  submissionId?: string; // Add submissionId parameter
}

export class ProgressUpdateService {
  /**
   * Update progress for video content
   */
  public static async updateVideoProgress(
    options: VideoProgressOptions
  ): Promise<ContentProgressData> {
    const {
      userId,
      contentId,
      subModuleId,
      watchedDuration,
      videoDuration,
      originalPoints = 0,
      completed
    } = options;

    // Calculate completion status
    const completionThreshold = 0.9; // 90% watched is considered complete
    const watchPercentage =
      videoDuration > 0 ? Math.min(watchedDuration / videoDuration, 1) : 0;
    const thresholdMet = watchPercentage >= completionThreshold;
    const isCompleted = thresholdMet ? true : (completed ?? false);

    // Calculate points
    const earnedPoints = isCompleted
      ? originalPoints
      : Math.round(watchPercentage * originalPoints);

    return this.updateProgress({
      userId,
      contentId,
      contentType: 'video',
      subModuleId,
      completed: isCompleted,
      points: earnedPoints,
      originalPoints,
      additionalData: {
        watchedDuration
      }
    });
  }

  /**
   * Update progress for MCQ content
   */
  public static async updateMCQProgress(
    options: Omit<MCQProgressOptions, 'selectedOptions' | 'attempts'> & {
      selectedOptions: string[];
      correctOptions: string[];
      currentAttempts?: number;
      pointsEarned?: number; // Add optional explicit points earned parameter
    }
  ): Promise<ContentProgressData> {
    const {
      userId,
      contentId,
      subModuleId,
      selectedOptions,
      correctOptions,
      originalPoints = 0,
      pointsEarned // Get explicit points earned if provided
    } = options;

    // Determine if answer is correct
    const allCorrectSelected = correctOptions.every(opt =>
      selectedOptions.includes(opt)
    );
    const noIncorrectSelected = selectedOptions.every(opt =>
      correctOptions.includes(opt)
    );
    const isCorrect = allCorrectSelected && noIncorrectSelected;

    // Use explicitly provided points or fallback to automatic calculation
    const points =
      pointsEarned !== undefined
        ? pointsEarned
        : isCorrect
          ? originalPoints
          : 0;

    // Note: We're not incrementing attempts here anymore since we do it directly in the mcq.service.ts
    return this.updateProgress({
      userId,
      contentId,
      contentType: 'mcq',
      subModuleId,
      completed: isCorrect,
      points,
      originalPoints,
      additionalData: {
        selectedOptions
      }
    });
  }

  /**
   * Update progress for coding problem/lab
   */
  public static async updateCodingProgress(
    options: CodingSubmissionOptions
  ): Promise<ContentProgressData> {
    const {
      userId,
      contentId,
      contentType,
      subModuleId,
      code,
      language,
      testCaseResults,
      originalPoints = 0,
      submissionId // Get the submission ID
    } = options;

    // Calculate total points earned and completion status
    const earnedPoints = testCaseResults.reduce(
      (sum, result) => sum + (result.passed ? result.pointsEarned : 0),
      0
    );

    // Determine if all required tests passed (could add threshold here)
    const allPassed = testCaseResults.every(result => result.passed);

    // Instead of pushing the whole submission object, we'll now store just the submissionId
    // if provided, otherwise we'll maintain backward compatibility
    const additionalData: Record<string, unknown> = {};

    if (submissionId) {
      // When submissionId is provided, we'll use it
      additionalData.submissionId = submissionId;
    } else {
      // For backward compatibility, keep the existing behavior
      additionalData.$push = {
        submissions: {
          code,
          language,
          submittedAt: new Date(),
          testCaseResults,
          totalPoints: earnedPoints
        }
      };
    }

    return this.updateProgress({
      userId,
      contentId,
      contentType,
      subModuleId,
      completed: allPassed,
      points: earnedPoints,
      originalPoints,
      additionalData
    });
  }

  /**
   * Update progress for note content
   */
  public static async updateNoteProgress(
    options: NoteProgressOptions
  ): Promise<ContentProgressData> {
    const { userId, contentId, subModuleId, batchId, originalPoints } = options;

    return this.updateProgress({
      userId,
      contentId,
      contentType: 'note',
      subModuleId,
      batchId,
      completed: true,
      points: originalPoints,
      originalPoints
    });
  }

  /**
   * Public method to recalculate and update progress for a specific submodule and its parent module
   * @param userId The user ID
   * @param subModuleId The submodule ID
   * @param courseId The course ID
   */
  public static async recalculateSubmoduleAndModuleProgress(
    userProgressId: string,
    subModuleId: string,
    moduleId: string
  ): Promise<void> {
    try {
      // Call the internal method to update progress
      await this.updateAggregateProgress(userProgressId, subModuleId, moduleId);
    } catch (error) {
      console.error(
        'Error recalculating submodule and module progress:',
        error
      );
      if (error instanceof ErrorHandler) throw error;
      throw new ErrorHandler(
        500,
        `Failed to recalculate progress: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Core progress update method (private)
   */
  private static async updateProgress(
    options: ProgressUpdateOptions & {
      additionalData?: Record<string, unknown>;
    }
  ): Promise<ContentProgressData> {
    const {
      userId,
      contentId,
      contentType,
      subModuleId,
      batchId,
      completed = false,
      points = 0,
      originalPoints = 0,
      additionalData = {}
    } = options;

    try {
      // 1. Get submodule information
      const submodule = (await this.getSubmoduleInfo(subModuleId)) as {
        module: {
          _id: string;
          course: string;
        };
      };
      const moduleId = submodule.module._id.toString();
      const courseId = submodule.module.course.toString();

      // 2. Find user progress document
      const userProgress = await this.findOrCreateUserProgress(
        userId,
        courseId,
        batchId as string
      );

      // NEW STEP: Ensure batch exists before proceeding to calculate penalties
      if (!userProgress.batch) {
        await this.ensureBatchExists(userProgress);

        // Reload user progress if batch was just set
        if (!userProgress.batch) {
          const refreshedProgress = await UserProgress.findById(
            userProgress._id
          );
          if (refreshedProgress) {
            Object.assign(userProgress, refreshedProgress);
          }
        }
      }

      // 3. Update content item progress
      const contentProgress = await this.updateContentItemProgress(
        userProgress,
        contentId,
        contentType,
        completed,
        points,
        originalPoints,
        additionalData,
        subModuleId // Pass the subModuleId to updateContentItemProgress
      );
      // 4. Update aggregate progress
      await this.updateAggregateProgress(
        userProgress._id as string,
        subModuleId,
        moduleId
      );

      return contentProgress;
    } catch (error) {
      console.error(`Error updating progress for ${contentType}:`, error);
      if (error instanceof ErrorHandler) throw error;
      throw new ErrorHandler(
        500,
        `Failed to update progress: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Get submodule information
   */
  private static async getSubmoduleInfo(
    subModuleId: string
  ): Promise<ISubModule> {
    const submodule = await submoduleDao.findById(subModuleId, 'module');

    if (!submodule) {
      throw new ErrorHandler(404, 'Submodule not found');
    }

    return submodule;
  }

  /**
   * Find or create user progress document
   */
  private static async findOrCreateUserProgress(
    userId: string,
    courseId: string,
    batchId: string
  ): Promise<IUserProgress> {
    const now = new Date();

    const query: Record<string, unknown> = {
      user: new Types.ObjectId(userId),
      course: new Types.ObjectId(courseId)
    };

    if (batchId) {
      query.batch = new Types.ObjectId(batchId);
    }

    const userProgress = await UserProgress.findOneAndUpdate(
      query,
      {
        $set: { lastActivityAt: now },
        $setOnInsert: {
          user: new Types.ObjectId(userId),
          course: new Types.ObjectId(courseId),
          ...(batchId && { batch: new Types.ObjectId(batchId) }),
          overallProgress: 0,
          totalPointsEarned: 0,
          contentItemProgress: [],
          moduleProgress: [],
          subModuleProgress: []
        }
      },
      { new: true, upsert: true }
    );

    if (!userProgress) {
      throw new ErrorHandler(500, 'Failed to get or create user progress');
    }

    return userProgress;
  }

  /**
   * Update content item progress
   */
  private static async updateContentItemProgress(
    userProgress: IUserProgress,
    contentId: string,
    contentType: ContentType,
    completed: boolean,
    points: number,
    originalPoints: number,
    additionalData: Record<string, unknown>,
    subModuleId: string
  ): Promise<ContentProgressData> {
    // Get content model name
    const contentModel = this.getContentModelFromType(contentType);
    const now = new Date();

    // Find existing content item index based on contentId, contentType AND subModuleId
    const contentItemIndex = userProgress.contentItemProgress.findIndex(
      item =>
        item.contentId.toString() === contentId &&
        item.contentType === contentType &&
        item.subModuleId.toString() === subModuleId
    );

    // Check if already completed
    const isAlreadyCompleted =
      contentItemIndex >= 0 &&
      userProgress.contentItemProgress[contentItemIndex].completed;

    // Always calculate penalty if user is in a batch (regardless of completion)
    let daysLate = 0;
    let penaltyApplied = 0;
    let penaltyPercentage = 0;
    let earnedPoints = points;

    if (userProgress.batch && subModuleId && contentId) {
      // Calculate penalty and apply it
      const penaltyResult = await this.calculateDeadlinePenalty(
        userProgress.user,
        userProgress.batch,
        subModuleId,
        points,
        originalPoints,
        now
      );

      // Store penalty details
      daysLate = penaltyResult.daysLate;
      penaltyApplied = penaltyResult.penaltyApplied;
      penaltyPercentage = penaltyResult.penaltyPercentage;

      // Apply penalty to points (even if not completed)
      earnedPoints = penaltyResult.pointsAfterPenalty;

      // Log penalty calculation for monitoring/debugging
      console.warn('Content Penalty Applied:', {
        contentId,
        contentType,
        subModuleId,
        daysLate,
        penaltyPercentage,
        penaltyApplied,
        originalPoints,
        finalPoints: earnedPoints,
        completed
      });
    } else if (contentItemIndex >= 0) {
      // If already in progress, use the higher of the existing or new points
      const existingPoints =
        userProgress.contentItemProgress[contentItemIndex].pointsEarned || 0;
      earnedPoints = Math.max(existingPoints, points);
    }

    // Prepare the update
    if (contentItemIndex >= 0) {
      // Update existing item
      const updateData: Record<string, unknown> = {};

      // Handle special processing for coding submissions
      if (
        additionalData.submissionId &&
        (contentType === 'coding_problem' || contentType === 'coding_lab')
      ) {
        // Only add the submission to the submissions array if points are higher or equal
        // than current points, which means this is a better or equal submission
        if (
          earnedPoints >=
          (userProgress.contentItemProgress[contentItemIndex].pointsEarned || 0)
        ) {
          await UserProgress.updateOne(
            {
              _id: userProgress._id,
              'contentItemProgress.contentId': new Types.ObjectId(contentId),
              'contentItemProgress.contentType': contentType,
              'contentItemProgress.subModuleId': new Types.ObjectId(subModuleId)
            },
            {
              $addToSet: {
                'contentItemProgress.$[elem].submissions': new Types.ObjectId(
                  additionalData.submissionId as string
                )
              }
            },
            {
              arrayFilters: [
                {
                  'elem.contentId': new Types.ObjectId(contentId),
                  'elem.contentType': contentType,
                  'elem.subModuleId': new Types.ObjectId(subModuleId)
                }
              ]
            }
          );
        }
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { submissionId, ...restAdditionalData } = additionalData;
        Object.assign(updateData, restAdditionalData);
      } else {
        // For other updates that don't involve submission IDs
        Object.assign(updateData, additionalData);
      }

      // Always update penalty information
      updateData.daysLate = daysLate;
      updateData.penaltyApplied = penaltyApplied;
      updateData.penaltyPercentage = penaltyPercentage;
      updateData.originalPoints = originalPoints;

      // Only update completion status if not already completed or explicitly marking as completed
      if (!isAlreadyCompleted || completed) {
        updateData.completed = completed;
        if (
          completed &&
          !userProgress.contentItemProgress[contentItemIndex].completedAt
        ) {
          updateData.completedAt = now;
        }
      }

      // Update points if higher than current
      if (
        earnedPoints >
        (userProgress.contentItemProgress[contentItemIndex].pointsEarned || 0)
      ) {
        updateData.pointsEarned = earnedPoints;
      }

      // Update attempts with +1
      updateData.attempts =
        (userProgress.contentItemProgress[contentItemIndex].attempts || 0) + 1;

      // Only perform the update if there are fields to update
      if (Object.keys(updateData).length > 0) {
        // Apply the update using positional operator with contentId, contentType and subModuleId as filters
        await UserProgress.updateOne(
          {
            _id: userProgress._id
          },
          {
            $set: Object.entries(updateData).reduce(
              (result, [key, value]) => {
                result[`contentItemProgress.$[elem].${key}`] = value;
                return result;
              },
              {} as Record<string, unknown>
            ),
            $currentDate: { lastActivityAt: true }
          },
          {
            arrayFilters: [
              {
                'elem.contentId': new Types.ObjectId(contentId),
                'elem.contentType': contentType,
                'elem.subModuleId': new Types.ObjectId(subModuleId)
              }
            ]
          }
        );
      }
    } else {
      // Create new progress item
      const newProgressItem: Record<string, unknown> = {
        contentType,
        contentId: new Types.ObjectId(contentId),
        contentModel,
        subModuleId: new Types.ObjectId(subModuleId),
        completed,
        pointsEarned: earnedPoints,
        originalPoints,
        attempts: 1,
        daysLate,
        penaltyApplied,
        penaltyPercentage,
        ...(completed ? { completedAt: now } : {})
      };

      // Handle submissions for new content items
      if (
        additionalData.submissionId &&
        (contentType === 'coding_problem' || contentType === 'coding_lab')
      ) {
        // Initialize submissions array with the new submission ID
        newProgressItem.submissions = [
          new Types.ObjectId(additionalData.submissionId as string)
        ];

        // Remove submissionId from additionalData as we've handled it
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { submissionId, ...restAdditionalData } = additionalData;
        Object.assign(newProgressItem, restAdditionalData);
      } else {
        // For other content types or backward compatibility
        Object.assign(newProgressItem, additionalData);
      }

      // Add to array
      await UserProgress.updateOne(
        { _id: userProgress._id },
        {
          $push: { contentItemProgress: newProgressItem },
          $currentDate: { lastActivityAt: true }
        }
      );
    }

    return {
      contentId,
      contentType,
      subModuleId,
      completed: completed || isAlreadyCompleted,
      pointsEarned: earnedPoints
    };
  }

  /**
   * Map content type to model name
   */
  private static getContentModelFromType(contentType: ContentType): string {
    const contentTypeToModel: Record<ContentType, string> = {
      video: 'Video',
      note: 'Note',
      mcq: 'MCQ',
      coding_problem: 'CodingProblem',
      coding_lab: 'CodingLab'
    };

    return contentTypeToModel[contentType];
  }

  /**
   * Update aggregate progress based on content point calculations
   */
  private static async updateAggregateProgress(
    userProgressId: string,
    subModuleId: string,
    moduleId: string
  ): Promise<void> {
    try {
      // 1. Get user progress document - use lean() for better performance
      const userProgress = await UserProgress.findById(userProgressId);
      if (!userProgress) {
        throw new ErrorHandler(404, 'User progress not found');
      }

      // 2. Validate batch information (required for submodule progress)
      if (!userProgress.batch) {
        await this.ensureBatchExists(userProgress);
      }

      // 3. Get all content for this submodule with their potential points
      const submoduleContents =
        await submoduleContentDao.findAllBySubmodule(subModuleId);

      // 4. Calculate submodule points and progress
      const {
        // totalPossiblePoints: submoduleTotalPoints,
        earnedPoints: submoduleEarnedPoints,
        completionPercentage: submoduleProgress
      } = this.calculatePointsForSubmodule(userProgress, submoduleContents);

      // 5. Update submodule progress in user progress document
      await this.updateSubmoduleProgress(
        userProgress,
        subModuleId,
        submoduleProgress,
        submoduleEarnedPoints
      );

      // Refetch the user progress to get the updated data
      const updatedUserProgress = await UserProgress.findById(userProgressId);
      if (!updatedUserProgress) {
        throw new ErrorHandler(404, 'Updated user progress not found');
      }

      // 6. Get all submodules for this module for module progress calculation
      const moduleSubmodules = await submoduleDao.findAllByModule(moduleId);

      // 7. Calculate module points and progress with the updated user progress
      const {
        // totalPossiblePoints: moduleTotalPoints,
        earnedPoints: moduleEarnedPoints,
        completionPercentage: moduleProgress
      } = this.calculatePointsForModule(updatedUserProgress, moduleSubmodules);

      // 8. Update module progress in user progress document
      await this.updateModuleProgress(
        updatedUserProgress,
        moduleId,
        moduleProgress,
        moduleEarnedPoints
      );

      // 9. Update overall progress by calculating the ratio of all module points earned versus total possible module points
      // Get all modules for this course
      const allCourseModules = await moduleDao.findAll({
        course: updatedUserProgress.course
      });

      // Get all module IDs for the course
      const courseModuleIds = allCourseModules.map(module =>
        String(module._id)
      );

      // Calculate total possible points for all modules in course
      const totalCoursePossiblePoints = allCourseModules.reduce(
        (sum, module) => sum + (module.totalPoints || 0),
        0
      );

      // Refetch one more time to ensure we have the latest module progress data
      const finalUserProgress = await UserProgress.findById(userProgressId);
      if (!finalUserProgress) {
        throw new ErrorHandler(404, 'Final user progress not found');
      }

      // Calculate total points earned across all modules in the course
      let totalCoursePointsEarned = 0;
      finalUserProgress.moduleProgress.forEach(progress => {
        if (courseModuleIds.includes(progress.module.toString())) {
          totalCoursePointsEarned += progress.pointsEarned || 0;
        }
      });

      // Calculate overall progress as the percentage of points earned out of total possible points
      const overallProgress = Math.round(
        totalCoursePossiblePoints > 0
          ? Math.min(
              100,
              (totalCoursePointsEarned / totalCoursePossiblePoints) * 100
            )
          : 0
      );

      // Get total points from all content items
      const totalPointsResult: Array<{ totalPoints: number }> =
        await UserProgress.aggregate([
          { $match: { _id: new Types.ObjectId(userProgressId) } },
          {
            $project: {
              totalPoints: { $sum: '$contentItemProgress.pointsEarned' }
            }
          }
        ]);

      const totalPointsEarned = totalPointsResult[0]?.totalPoints || 0;
      // Make a certificate logic for the user here
      // Check if the user is eligible for a certificate
      // Check if the user does not already have a certificate
      const isEligibleForCertificate = await getCertificateEligibilityService(
        userProgress.user,
        userProgress.course
      );
      if (isEligibleForCertificate.eligible) {
        const user: IUser | null = await getUserById(
          userProgress.user.toString()
        );
        const courseDetails = await getCourseDetails(
          userProgress.course.toString()
        );

        const batchDetails = await getBatchDetails(
          userProgress.batch.toString()
        );

        if (user && courseDetails && batchDetails && isEligibleForCertificate) {
          if (!isEligibleForCertificate.certificateType) {
            return;
          }
          console.warn(isEligibleForCertificate.certificateType);
          const isCertificateGenerated = createCertificateService(
            userProgress.user,
            userProgress.course,
            userProgress.batch,
            user,
            courseDetails,
            batchDetails,
            isEligibleForCertificate.certificateType
          );
          if ((await isCertificateGenerated).length > 0) {
            console.warn('Certificate generated successfully');
          }
        }
      }

      // 10. Save all changes using an atomic update
      const progress: IUserProgress | null =
        await UserProgress.findByIdAndUpdate(
          userProgressId,
          {
            $set: {
              totalPointsEarned: totalPointsEarned,
              overallProgress: overallProgress
            },
            $currentDate: { lastActivityAt: true } // Ensure lastActivityAt is always updated
          },
          { new: true }
        );

      if (progress) {
        await upsertLeaderboardRankings(progress);
      }
    } catch (error) {
      console.error('Error in updateAggregateProgress:', error);
      if (error instanceof Error) {
        console.error('Error details:', error.message);
        console.error('Error stack:', error.stack);
      }
      throw error;
    }
  }

  /**
   * Ensure batch exists in the user progress document
   */
  private static async ensureBatchExists(
    userProgress: IUserProgress
  ): Promise<void> {
    const courseEnrollment = await EnrolledCourseLinkDao.findOne({
      userId: userProgress.user,
      courseId: userProgress.course
    });

    if (courseEnrollment?.batchId) {
      userProgress.batch = courseEnrollment.batchId;
      await UserProgress.updateOne(
        { _id: userProgress._id },
        { $set: { batch: courseEnrollment.batchId } }
      );
    }
  }

  /**
   * Calculate points and progress for a submodule
   */
  private static calculatePointsForSubmodule(
    userProgress: IUserProgress,
    submoduleContents: ISubmoduleContent[]
  ): {
    totalPossiblePoints: number;
    earnedPoints: number;
    completionPercentage: number;
  } {
    // Extract content IDs for filtering user progress
    const submoduleContentIds = submoduleContents.map(content => {
      if (typeof content.contentId === 'string') {
        return content.contentId;
      } else if ('_id' in content.contentId) {
        return String(content.contentId._id);
      }
      return '';
    });

    // Get the subModuleId from the first submodule content item - safely
    let subModuleId = '';
    if (submoduleContents.length > 0) {
      const firstContent = submoduleContents[0];
      if (firstContent && typeof firstContent.submodule !== 'undefined') {
        // cast to ObjectId and call toString() to get the actual id hex
        subModuleId = (firstContent.submodule as Types.ObjectId).toString();
      }
    }

    // Get all content items completed by the user for this submodule
    const userContentItems = userProgress.contentItemProgress.filter(item => {
      const contentMatch = submoduleContentIds.includes(
        item.contentId.toString()
      );

      // Filter by either exact subModuleId match or by contentId only if subModuleId isn't defined yet
      // (for backward compatibility with existing data)
      const submoduleMatch =
        !subModuleId ||
        !item.subModuleId ||
        item.subModuleId.toString() === subModuleId;

      return contentMatch && submoduleMatch;
    });

    // Calculate total possible points from all content
    const totalPossiblePoints = submoduleContents.reduce((sum, content) => {
      if (
        typeof content.contentId !== 'string' &&
        'points' in content.contentId
      ) {
        return sum + (content.contentId.points || 0);
      }
      return sum;
    }, 0);

    // Calculate earned points from user progress
    const earnedPoints = userContentItems.reduce(
      (sum, item) => sum + (item.pointsEarned || 0),
      0
    );

    // Calculate progress percentage, handle edge case
    const completionPercentage = Math.round(
      totalPossiblePoints > 0
        ? Math.min(100, (earnedPoints / totalPossiblePoints) * 100)
        : 0
    );

    return { totalPossiblePoints, earnedPoints, completionPercentage };
  }

  /**
   * Update submodule progress in user progress document
   */
  private static async updateSubmoduleProgress(
    userProgress: IUserProgress,
    subModuleId: string,
    progressPercentage: number,
    pointsEarned: number
  ): Promise<void> {
    // 80% threshold for completion
    const isCompleted =
      progressPercentage >= Number(config.subModuleCompletionPercentage);

    // Calculate total penalties from all content items in this submodule
    const submoduleContentItems = userProgress.contentItemProgress.filter(
      item => item.subModuleId.toString() === subModuleId
    );

    // Sum all penalties applied to content in this submodule
    // Store the highest penalty applied on this submodule (percentage)
    const totalPenaltyApplied = submoduleContentItems.reduce(
      (max, item) => Math.max(max, item.penaltyApplied || 0),
      0
    );

    // Find the maximum days late across all content items
    const maxDaysLate = submoduleContentItems.reduce(
      (max, item) => Math.max(max, item.daysLate || 0),
      0
    );

    console.warn(`Submodule penalty calculation for ${subModuleId}:`, {
      totalPenaltyApplied,
      maxDaysLate,
      contentItems: submoduleContentItems.length
    });

    const submoduleProgressIndex = userProgress.subModuleProgress.findIndex(
      item => item.subModule.toString() === subModuleId
    );

    if (submoduleProgressIndex >= 0) {
      // Update existing entry using atomic operation
      const updateData: Record<string, unknown> = {
        'subModuleProgress.$.progress': progressPercentage,
        'subModuleProgress.$.pointsEarned': pointsEarned,
        'subModuleProgress.$.completed': isCompleted,
        'subModuleProgress.$.penaltyApplied': totalPenaltyApplied,
        'subModuleProgress.$.daysLate': maxDaysLate
      };

      // Only set completedAt if not already set and now completed
      if (
        isCompleted &&
        !userProgress.subModuleProgress[submoduleProgressIndex].completedAt
      ) {
        updateData['subModuleProgress.$.completedAt'] = new Date();
      }

      await UserProgress.updateOne(
        {
          _id: userProgress._id,
          'subModuleProgress.subModule': new Types.ObjectId(subModuleId)
        },
        {
          $set: updateData
        }
      );
    } else {
      // First check if the entry actually exists to avoid race conditions
      const existingEntry = await UserProgress.findOne({
        _id: userProgress._id,
        'subModuleProgress.subModule': new Types.ObjectId(subModuleId)
      });

      if (!existingEntry) {
        // Only create if it truly doesn't exist
        const newEntry = {
          subModule: new Types.ObjectId(subModuleId),
          progress: progressPercentage,
          completed: isCompleted,
          pointsEarned: pointsEarned,
          penaltyApplied: totalPenaltyApplied, // Add calculated penalty
          daysLate: maxDaysLate, // Add calculated days late
          completedAt: isCompleted ? new Date() : undefined
        };

        // Add completedAt if completed
        await UserProgress.updateOne(
          { _id: userProgress._id },
          {
            $addToSet: { subModuleProgress: newEntry }
          }
        );
      } else {
        // If it does exist (race condition), update it instead
        const updateData: Record<string, unknown> = {
          'subModuleProgress.$.progress': progressPercentage,
          'subModuleProgress.$.pointsEarned': pointsEarned,
          'subModuleProgress.$.completed': isCompleted,
          'subModuleProgress.$.penaltyApplied': totalPenaltyApplied,
          'subModuleProgress.$.daysLate': maxDaysLate
        };

        // Only set completedAt if not already set and now completed
        const existingProgress = existingEntry.subModuleProgress.find(
          p => p.subModule.toString() === subModuleId
        );

        if (isCompleted && existingProgress && !existingProgress.completedAt) {
          updateData['subModuleProgress.$.completedAt'] = new Date();
        }

        await UserProgress.updateOne(
          {
            _id: userProgress._id,
            'subModuleProgress.subModule': new Types.ObjectId(subModuleId)
          },
          {
            $set: updateData
          }
        );
      }
    }
  }

  /**
   * Calculate points and progress for a module
   */
  private static calculatePointsForModule(
    userProgress: IUserProgress,
    moduleSubmodules: ISubModule[]
  ): {
    totalPossiblePoints: number;
    earnedPoints: number;
    completionPercentage: number;
  } {
    // Get all submodule IDs for this module
    const moduleSubmoduleIds = moduleSubmodules.map(sm => String(sm._id));

    // Calculate total possible points for the module (sum of all submodules' points)
    const totalPossiblePoints = moduleSubmodules.reduce(
      (sum, submodule) => sum + (submodule.points || 0),
      0
    );

    // Calculate earned points from user's submodule progress
    let earnedPoints = 0;

    // Log each submodule progress for debugging
    userProgress.subModuleProgress.forEach(progress => {
      const subModuleId = progress.subModule.toString();
      const inCurrentModule = moduleSubmoduleIds.includes(subModuleId);

      if (inCurrentModule) {
        earnedPoints += progress.pointsEarned || 0;
      }
    });
    // Calculate progress percentage
    const completionPercentage = Math.round(
      totalPossiblePoints > 0
        ? Math.min(100, (earnedPoints / totalPossiblePoints) * 100)
        : 0
    );

    return { totalPossiblePoints, earnedPoints, completionPercentage };
  }

  /**
   * Update module progress in user progress document
   */

  private static async updateModuleProgress(
    userProgress: IUserProgress,
    moduleId: string,
    progressPercentage: number,
    pointsEarned: number
  ): Promise<void> {
    const isCompleted = progressPercentage >= 80; // 80% threshold for completion

    // Get all submodule IDs for this module
    const moduleSubmodules = await submoduleDao.findAllByModule(moduleId);
    const submoduleIds = moduleSubmodules.map((sm: ISubModule) =>
      typeof sm._id === 'string'
        ? sm._id
        : (sm._id as Types.ObjectId).toString()
    );

    // Calculate total penalties from all submodule progress entries for this module
    const moduleSubmoduleProgresses = userProgress.subModuleProgress.filter(
      item => submoduleIds.includes(item.subModule.toString())
    );

    // Sum all penalties applied across all submodules in this module
    const totalPenaltyApplied = moduleSubmoduleProgresses.reduce(
      (sum, item) => sum + (item.penaltyApplied || 0),
      0
    );

    // Find the maximum days late across all submodules in this module

    // Calculate total days late across all submodules in this module
    const totalDaysLate = moduleSubmoduleProgresses.reduce(
      (sum, item) => sum + (item.daysLate || 0),
      0
    );

    const moduleProgressIndex = userProgress.moduleProgress.findIndex(
      item => item.module.toString() === moduleId
    );

    if (moduleProgressIndex >= 0) {
      // Update using atomic operation
      await UserProgress.updateOne(
        {
          _id: userProgress._id,
          'moduleProgress.module': new Types.ObjectId(moduleId)
        },
        {
          $set: {
            'moduleProgress.$.progress': progressPercentage,
            'moduleProgress.$.pointsEarned': pointsEarned,
            'moduleProgress.$.penaltyApplied': totalPenaltyApplied,
            'moduleProgress.$.daysLate': totalDaysLate,
            ...(!userProgress.moduleProgress[moduleProgressIndex].completedAt &&
            isCompleted
              ? { 'moduleProgress.$.completedAt': new Date() }
              : {})
          }
        }
      );
    } else {
      // Create new entry using atomic operation
      await UserProgress.updateOne(
        { _id: userProgress._id },
        {
          $addToSet: {
            // Use $addToSet to prevent duplicates
            moduleProgress: {
              module: new Types.ObjectId(moduleId),
              progress: progressPercentage,
              pointsEarned: pointsEarned,
              penaltyApplied: totalPenaltyApplied, // Add calculated penalty
              daysLate: totalDaysLate, // Add calculated days late
              ...(isCompleted ? { completedAt: new Date() } : {})
            }
          }
        }
      );
    }
  }

  /**
   * Calculate deadline penalty for late content completion
   */
  private static async calculateDeadlinePenalty(
    userId: string | Types.ObjectId,
    batchId: string | Types.ObjectId,
    subModuleId: string | Types.ObjectId,
    earnedPoints: number,
    originalPoints: number,
    completionDate: Date = new Date()
  ): Promise<{
    pointsAfterPenalty: number;
    daysLate: number;
    penaltyApplied: number;
    penaltyPercentage: number;
  }> {
    try {
      // 1. Get user deadline for this submodule
      const userDeadline = await UserDeadline.findOne({
        user: userId,
        batch: batchId,
        submodule: subModuleId,
        isActive: true
      }).lean();

      if (!userDeadline) {
        return {
          pointsAfterPenalty: earnedPoints,
          daysLate: 0,
          penaltyApplied: 0,
          penaltyPercentage: 0
        };
      }

      // 2. Calculate days late (setting deadline to end of day)
      const deadline = new Date(userDeadline.adjustedDeadline);
      deadline.setHours(23, 59, 59, 999); // Set to end of day

      const msPerDay = 1000 * 60 * 60 * 24;
      const daysLate = Math.max(
        0,
        Math.ceil((completionDate.getTime() - deadline.getTime()) / msPerDay)
      );

      // 3. If not late, no penalty
      if (daysLate <= 0) {
        return {
          pointsAfterPenalty: earnedPoints,
          daysLate: 0,
          penaltyApplied: 0,
          penaltyPercentage: 0
        };
      }

      // 4. Get penalty rules
      const batchDeadline = await BatchSubmoduleDeadline.findOne({
        batch: batchId,
        submodule: subModuleId,
        isActive: true
      }).lean();

      if (
        !batchDeadline ||
        !batchDeadline.penaltyRules ||
        batchDeadline.penaltyRules.length === 0
      ) {
        return {
          pointsAfterPenalty: earnedPoints,
          daysLate,
          penaltyApplied: 0,
          penaltyPercentage: 0
        };
      }

      // 5. Find applicable penalty rule (sort by daysLate in descending order)
      const sortedRules = [...batchDeadline.penaltyRules].sort(
        (a, b) => b.daysLate - a.daysLate
      );

      let penaltyPercentage = 0;

      // Find the maximum days late defined in the rules
      const maxDaysLateInRules =
        sortedRules.length > 0 ? sortedRules[0].daysLate : 0;

      // If user is later than the maximum defined rule, use the ENV penalty
      if (daysLate > maxDaysLateInRules && config.penaltyPercentage) {
        // Use the penalty percentage from environment variable
        penaltyPercentage = Number(config.penaltyPercentage);
        console.warn(
          `Applied maximum penalty from ENV: ${penaltyPercentage}% for ${daysLate} days late (exceeds max rule of ${maxDaysLateInRules} days)`
        );
      } else {
        // Original logic for finding the applicable rule
        for (const rule of sortedRules) {
          if (daysLate >= rule.daysLate) {
            penaltyPercentage = rule.penaltyPercentage;
            break;
          }
        }
      }

      // 6. Calculate penalty amount and final points
      // Store the percentage in penaltyApplied instead of points
      const penaltyApplied = penaltyPercentage; // Store percentage value directly

      // Calculate the actual points to deduct (penalty on earnedPoints, not originalPoints)
      const pointsToDeduct = (earnedPoints * penaltyPercentage) / 100;

      // Apply minimum penalty if needed
      const actualPointsDeducted =
        penaltyPercentage > 0 && pointsToDeduct < 0.1 ? 0.1 : pointsToDeduct;

      const pointsAfterPenalty = Math.max(
        0,
        earnedPoints - actualPointsDeducted
      );

      return {
        pointsAfterPenalty,
        daysLate,
        penaltyApplied, // Now stores percentage value
        penaltyPercentage
      };
    } catch (error) {
      console.error('Error calculating deadline penalty:', error);
      return {
        pointsAfterPenalty: earnedPoints,
        daysLate: 0,
        penaltyApplied: 0,
        penaltyPercentage: 0
      };
    }
  }
}
