import axios from 'axios';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import {
  IBatchExecutionResult,
  IExecutionResult
} from '../../interfaces/code-execution.interface';
import { config } from '../../config/config';

interface IJudge0Response {
  stdout: string;
  stderr: string;
  compile_output: string;
  time: string;
  memory: string;
  status: {
    id: number;
    description: string;
  };
}

interface IBatchSubmissionResponse {
  submissions: IJudge0Response[];
}

export class CodeExecutionService {
  private static headers = {
    'x-Auth-Token': config.judge0ApiKey,
    'Content-Type': 'application/json'
  };

  /**
   * Execute code against a specific input
   */
  public static async executeCode(
    code: string,
    language: string,
    mainCode: string,
    input: string,
    expectedOutput: string
  ): Promise<IExecutionResult> {
    try {
      // Create zip file with code based on language
      const base64zip = await this.createLanguageZip(language, code, mainCode);

      // Prepare submission for Judge0
      const data = {
        additional_files: base64zip,
        language_id: 89, // Custom language ID for using additional files
        stdin: input ? Buffer.from(input).toString('base64') : undefined,
        expected_output: expectedOutput
          ? Buffer.from(expectedOutput).toString('base64')
          : undefined
      };

      // Send to Judge0
      const options = {
        method: 'POST',
        url: `${config.judge0ApiUrl}/submissions`,
        params: {
          base64_encoded: 'true',
          wait: 'false',
          fields: '*'
        },
        headers: this.headers,
        data: data
      };

      const response: {
        data: {
          token: string;
          status: {
            id: number;
            description: string;
          };
          stdout: string;
          stderr: string;
        };
      } = await axios.request(options);

      const submissionToken = response.data.token;
      // Poll for results since wait is set to false
      const result = await this.pollSubmissionResult(submissionToken);

      // Process results
      return this.processJudge0Response(result);
    } catch (error) {
      console.error('Error executing code:', error);
      throw new Error(
        `Code execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Poll for submission results
   */
  private static async pollSubmissionResult(
    token: string
  ): Promise<IJudge0Response> {
    const maxAttempts = 10;
    const pollingInterval = 2000; // 1 second

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const response: {
          data: IJudge0Response;
        } = await axios.request({
          method: 'GET',
          url: `${config.judge0ApiUrl}/submissions/${token}`,
          params: {
            base64_encoded: 'true',
            fields: '*'
          },
          headers: this.headers
        });

        // Check if processing is complete
        if (
          response.data.status &&
          response.data.status.id !== 1 &&
          response.data.status.id !== 2
        ) {
          return response.data;
        }
        // logs for debugging

        // Wait before next polling attempt
        await new Promise(resolve => setTimeout(resolve, pollingInterval));
      } catch (error) {
        console.error('Error polling for results:', error);
      }
    }

    throw new Error('Timed out waiting for code execution results');
  }

  /**
   * Execute multiple test cases in a batch
   */
  public static async executeBatch(
    code: string,
    language: string,
    mainCode: string,
    testCases: Array<{ input: string; expectedOutput: string }>
  ): Promise<IBatchExecutionResult> {
    try {
      // Create zip file with code based on language
      const base64zip = await this.createLanguageZip(language, code, mainCode);

      // Prepare submissions array for Judge0
      const submissions = testCases.map(testCase => ({
        additional_files: base64zip,
        language_id: 89, // Custom language ID for using additional files
        stdin: testCase.input
          ? Buffer.from(testCase.input).toString('base64')
          : undefined,
        expected_output: testCase.expectedOutput
          ? Buffer.from(testCase.expectedOutput).toString('base64')
          : undefined
      }));

      // Send batch to Judge0
      const response: {
        data: Array<{ token: string }>;
      } = await axios.request({
        method: 'POST',
        url: `${config.judge0ApiUrl}/submissions/batch`,
        params: {
          base64_encoded: 'true'
        },
        headers: this.headers,
        data: { submissions }
      });

      const tokens = response.data.map(item => item.token);

      // Poll for batch results
      const batchResults = await this.pollBatchSubmissionResult(tokens);

      // Process each submission result
      const results = batchResults.submissions.map(
        (submissionResult, index) => {
          const testCase = testCases[index];
          return {
            input: testCase.input,
            expectedOutput: testCase.expectedOutput,
            ...this.processJudge0Response(submissionResult)
          };
        }
      );

      return { results };
    } catch (error) {
      console.error('Error executing batch code:', error);
      throw new Error(
        `Batch code execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Poll for batch submission results
   */
  private static async pollBatchSubmissionResult(
    tokens: string[]
  ): Promise<IBatchSubmissionResponse> {
    const maxAttempts = 15;
    const pollingInterval = 1500; // 1.5 seconds

    // Join tokens with commas for the API request
    const tokensParam = tokens.join(',');

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const response: {
          data: {
            submissions: IJudge0Response[];
          };
        } = await axios.request({
          method: 'GET',
          url: `${config.judge0ApiUrl}/submissions/batch`,
          params: {
            tokens: tokensParam,
            base64_encoded: 'true',
            fields: '*'
          },
          headers: this.headers
        });

        // Check if all submissions are complete
        const allComplete = response.data.submissions.every(
          sub => sub.status && sub.status.id !== 1 && sub.status.id !== 2
        );

        if (allComplete) {
          return response.data;
        }

        // Wait before next polling attempt
        await new Promise(resolve => setTimeout(resolve, pollingInterval));
      } catch (error) {
        console.error('Error polling for batch results:', error);
      }
    }

    throw new Error('Timed out waiting for batch code execution results');
  }

  /**
   * Process the response from Judge0
   */
  private static processJudge0Response(
    response: IJudge0Response
  ): IExecutionResult {
    // Extract and decode output
    const output = response.stdout
      ? Buffer.from(response.stdout, 'base64').toString('utf-8')
      : '';

    // Check for errors
    const error = response.stderr
      ? Buffer.from(response.stderr, 'base64').toString('utf-8')
      : response.compile_output
        ? Buffer.from(response.compile_output, 'base64').toString('utf-8')
        : 'runtime error';

    const executionTime = response.time ? parseFloat(response.time) : 0;
    const memoryUsage = response.memory ? parseInt(response.memory, 10) : 0;

    return {
      output,
      error,
      executionTime,
      memoryUsage,
      status: response.status ? response.status.description : 'Unknown'
    };
  }

  /**
   * Create a ZIP file with the code based on language
   */
  private static async createLanguageZip(
    language: string,
    code: string,
    mainCode?: string
  ): Promise<string> {
    const zip = new JSZip();
    let files: Record<string, string> = {};

    // Configure files based on language with standard commands
    switch (language.toLowerCase()) {
      case 'javascript':
        if (mainCode !== '' && mainCode !== undefined) {
          files = {
            'main.js': mainCode,
            'helper.js': code,
            run: `node main.js`
          };
        } else {
          files = {
            'main.js': code, // The user's main code
            run: `node main.js`
          };
        }
        break;
      case 'python':
        if (mainCode !== '' && mainCode !== undefined) {
          files = {
            'main.py': mainCode,
            'helper.py': code,
            run: `python3 main.py`
          };
        } else {
          files = {
            'main.py': code, // The user's main code
            run: `python3 main.py`
          };
        }
        break;
      case 'java':
        if (mainCode !== '' && mainCode !== undefined) {
          files = {
            'Main.java': mainCode,
            'Solution.java': code,
            compile: 'javac Main.java Solution.java',
            run: 'java Main'
          };
        } else {
          files = {
            'Main.java': code,
            compile: 'javac Main.java',
            run: 'java Main'
          };
        }
        break;
      case 'c':
        files = {
          'main.c': code,
          compile: 'gcc -o a.out main.c -lm',
          run: './a.out'
        };
        break;
      default:
        throw new Error(`Unsupported language: ${language}`);
    }

    // Add files to ZIP
    for (const [fileName, fileContent] of Object.entries(files)) {
      zip.file(fileName, fileContent);
    }

    // Generate base64 representation
    return await zip.generateAsync({ type: 'base64' });
  }
}
