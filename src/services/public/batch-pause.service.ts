import mongoose from 'mongoose';
import batchPauseDao from '../../dao/batch-pause.dao';
import BatchPause from '../../models/batch-pause.model';
import { IBatchPause } from '../../interfaces/batch-pause.interface';
import EnrolledCourseLink from '../../models/enrolled-course-link.model';
import ErrorHandler from '../../utils/error-handler';

interface PauseBatchParams {
  userId: string;
  batchId: string;
  startDate: string | Date;
  endDate: string | Date;
  reason: string;
  comments?: string;
}

export class BatchPauseService {
  /**
   * Pause a batch for a user
   */
  async pauseBatch(params: PauseBatchParams): Promise<IBatchPause> {
    const { userId, batchId, startDate, endDate, reason, comments } = params;

    // Check if user has remaining pause days
    const userObjId = new mongoose.Types.ObjectId(userId);
    const batchObjId = new mongoose.Types.ObjectId(batchId);

    const remainingDays = await BatchPause.getRemainingPauseDays(
      userObjId,
      batchObjId
    );

    if (remainingDays <= 0) {
      throw new Error('No pause days remaining for this batch');
    }

    // Check for any active pauses
    const activePauses = await batchPauseDao.findActiveByUser(userId);
    if (activePauses.length > 0) {
      throw new ErrorHandler(
        400,
        'User already has an active pause for this batch'
      );
    }

    // Create the batch pause record
    // Note: The validation for max pause days happens in the pre-save hook
    return await batchPauseDao.create({
      user: userObjId,
      batch: batchObjId,
      course: await this.getCourseIdFromBatch(batchId), // Implement this method or modify as needed
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      reason,
      comments,
      status: 'active'
    });

    // as soon as the batch is paused, we need to update the user-deadline for this batch and course
    // we need to get the user current progress and also get the current module
    // also get all the deadlines for this batch and course that are initially set
    // and then we need to set the new deadlines for the user
    // based on the new pause dates
    // but the dates will be affected only if the user again continues the batch
    // and if the batch is resumed automatically
  }

  /**
   * Resume a paused batch
   */
  async resumeBatch(pauseId: string): Promise<boolean> {
    const objId = new mongoose.Types.ObjectId(pauseId);
    return await BatchPause.resumePauseEarly(objId);
  }

  /**
   * Get pause history for a user in a batch
   */
  async getPauseHistory(
    userId: string,
    batchId: string
  ): Promise<IBatchPause[]> {
    return await batchPauseDao.findAll(
      {
        user: new mongoose.Types.ObjectId(userId),
        batch: new mongoose.Types.ObjectId(batchId)
      },
      { createdAt: -1 } // Sort by creation date, newest first
    );
  }

  /**
   * Get remaining pause days for a user in a batch
   */
  async getRemainingPauseDays(
    userId: string,
    batchId: string
  ): Promise<number> {
    const userObjId = new mongoose.Types.ObjectId(userId);
    const batchObjId = new mongoose.Types.ObjectId(batchId);

    return await BatchPause.getRemainingPauseDays(userObjId, batchObjId);
  }

  /**
   * Helper method to get course ID from batch
   * This is a placeholder - implement according to your data model
   */
  private async getCourseIdFromBatch(
    batchId: string
  ): Promise<mongoose.Types.ObjectId> {
    // You'll need to implement this based on your batch model
    const userLinkModel = await EnrolledCourseLink.findOne({
      batchId: new mongoose.Types.ObjectId(batchId)
    });
    if (userLinkModel) {
      return userLinkModel.courseId;
    }
    return new mongoose.Types.ObjectId();
  }
}

export default new BatchPauseService();
