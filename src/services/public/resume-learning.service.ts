import mongoose from 'mongoose';
import enrolledCourseDao from '../../dao/enrolled-course.dao';
import { ILastViewedContent } from '../../interfaces/enrolled-course-link.interface';
import submoduleDao from '../../dao/submodule.dao';
/**
 * Update the last viewed content for a user in a course
 */
export const updateLastViewedContent = async (
  userId: string | mongoose.Types.ObjectId,
  contentId: string | mongoose.Types.ObjectId,
  submoduleId: string | mongoose.Types.ObjectId,
  title?: string
): Promise<boolean> => {
  try {
    // Find submodule with populate module to find courseID
    const submodule = await submoduleDao.findById(submoduleId, 'module');
    if (!submodule || !submodule.module) {
      return false;
    }

    const module =
      typeof submodule.module === 'object' && 'course' in submodule.module
        ? submodule.module
        : null;

    if (!module || !module.course) {
      return false;
    }

    const courseId = module.course.toString();

    // Find enrollment
    const enrollment = await enrolledCourseDao.findOne({
      userId: new mongoose.Types.ObjectId(userId),
      courseId: new mongoose.Types.ObjectId(courseId)
    });

    if (!enrollment) {
      return false;
    }

    // Create last viewed content object
    const lastViewedContent: ILastViewedContent = {
      contentId: new mongoose.Types.ObjectId(contentId),
      submoduleId: new mongoose.Types.ObjectId(submoduleId),
      moduleId: new mongoose.Types.ObjectId(
        typeof submodule.module === 'object'
          ? (submodule.module._id as string)
          : submodule.module
      ),
      title,
      viewedAt: new Date()
    };

    // Update enrollment with last viewed content
    await enrolledCourseDao.update(enrollment._id.toString(), {
      lastViewedContent,
      lastActivity: new Date()
    });

    return true;
  } catch (error) {
    console.error('Error updating last viewed content:', error);
    return false;
  }
};

/**
 * Get the last viewed content for a user in a course
 */
export const getLastViewedContent = async (
  userId: string | mongoose.Types.ObjectId,
  courseId: string | mongoose.Types.ObjectId
): Promise<ILastViewedContent | null> => {
  try {
    const enrollment = await enrolledCourseDao.findOne({
      userId: new mongoose.Types.ObjectId(userId),
      courseId: new mongoose.Types.ObjectId(courseId)
    });

    return enrollment?.lastViewedContent || null;
  } catch (error) {
    console.error('Error getting last viewed content:', error);
    return null;
  }
};
