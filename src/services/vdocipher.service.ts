import axios from 'axios';
import { config } from '../config/config';

interface VdoCipherOptions {
  ttl?: number;
  userId?: string | null;
  email?: string;
  sessionId?: string;
}

interface VdoCipherOtpResponse {
  otp: string;
  playbackInfo: string;
  [key: string]: unknown;
}

interface VdoCipherVideoDetails {
  title: string;
  description?: string;
  [key: string]: unknown;
}

interface VdoCipherVideoWithIframeSrc {
  title: string;
  description: string;
  iframeSrc: string;
}

export class vdoCipherService {
  private readonly apiSecret: string;
  private readonly baseUrl = 'https://dev.vdocipher.com/api/videos';

  constructor() {
    this.apiSecret = config.vdoCipherApiSecret;
    if (!this.apiSecret) {
      console.warn('VdoCipher API secret not found in environment variables');
    }
  }

  /**
   * Get OTP for video playback
   */
  async getOtp(
    videoId: string,
    options: VdoCipherOptions
  ): Promise<VdoCipherOtpResponse> {
    const { ttl = 300, userId = null, email, sessionId } = options;

    const annotateText = email || sessionId;

    try {
      const response = await axios.post(
        `${this.baseUrl}/${videoId}/otp`,
        {
          ttl,
          userId,
          annotate: JSON.stringify([
            {
              type: 'rtext',
              text: annotateText,
              alpha: '0.7',
              color: '#ffffff',
              size: '16',
              interval: '7000'
            }
          ])
        },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Apisecret ${this.apiSecret}`
          }
        }
      );

      if (!response.status || response.status !== 200) {
        throw new Error(
          `VdoCipher API error: ${response.status} ${response.statusText}`
        );
      }

      return response.data as VdoCipherOtpResponse;
    } catch (error) {
      console.error('Error getting VdoCipher OTP:', error);
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(
          `VdoCipher API error: ${error.response.status} ${error.response.statusText}`
        );
      }
      throw error;
    }
  }

  /**
   * Get video details from VdoCipher
   */
  async getVideoDetails(videoId: string): Promise<VdoCipherVideoDetails> {
    try {
      const response = await axios.get(`${this.baseUrl}/${videoId}`, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Apisecret ${this.apiSecret}`
        }
      });

      if (!response.status || response.status !== 200) {
        throw new Error(
          `VdoCipher API error: ${response.status} ${response.statusText}`
        );
      }

      return response.data as VdoCipherVideoDetails;
    } catch (error) {
      console.error('Error getting VdoCipher video details:', error);
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(
          `VdoCipher API error: ${error.response.status} ${error.response.statusText}`
        );
      }
      throw error;
    }
  }

  /**
   * Generate iframe source URL
   */
  async generateIframeSrc(
    videoId: string,
    options: VdoCipherOptions
  ): Promise<string> {
    const otpResponse = await this.getOtp(videoId, options);
    return `https://player.vdocipher.com/v2/?otp=${otpResponse.otp}&playbackInfo=${otpResponse.playbackInfo}&startTime=10`;
  }

  /**
   * Get complete video details with iframe source
   */
  async getVideoWithIframeSrc(
    videoId: string,
    options: VdoCipherOptions
  ): Promise<VdoCipherVideoWithIframeSrc> {
    try {
      // Get iframe source
      const iframeSrc = await this.generateIframeSrc(videoId, options);

      // Get video metadata
      const details = await this.getVideoDetails(videoId);

      return {
        title: details.title,
        description:
          details.description && details.description.length > 0
            ? details.description
            : 'No description!',
        iframeSrc
      };
    } catch (error) {
      console.error('Error getting complete video details:', error);
      throw error;
    }
  }
}

export default new vdoCipherService();
