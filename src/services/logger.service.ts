import { Request } from 'express';
import mongoose from 'mongoose';
import activityLogDao from '../dao/activity-log.dao';
import { IActivityLog } from '../interfaces/activity-log.interface';
import logger from '../config/logger';

// Common action types for consistency (optional - you can use string literals too)
export const ActionTypes = {
  USER: {
    STATUS_ACTIVE: 'active'
  },
  MODULE: {
    ADD: 'ADD_MODULE',
    UPDATE: 'UPDATE_MODULE',
    DELETE: 'DELETE_MODULE',
    VIEW: 'VIEW_MODULE',
    LIST: 'LIST_MODULE'
  },
  SUBMODULE: {
    ADD: 'ADD_SUBMODULE',
    UPDATE: 'UPDATE_SUBMODULE',
    DELETE: 'DELETE_SUBMODULE',
    VIEW: 'VIEW_SUBMODULE',
    LIST: 'LIST_SUBMODULE'
  },
  SUB_MODULE_DEADLINE: {
    ADD: 'ADD_SUB_MODULE_DEADLINE',
    UPDATE: 'UPDATE_SUB_MODULE_DEADLINE',
    DELETE: 'DELETE_SUB_MODULE_DEADLINE',
    VIEW: 'VIEW_SUB_MODULE_DEADLINE',
    LIST: 'LIST_SUB_MODULE_DEADLINE'
  },
  MCQ: {
    ADD: 'ADD_MCQ',
    UPDATE: 'UPDATE_MCQ',
    DELETE: 'DELETE_MCQ',
    VIEW: 'VIEW_MCQ',
    LIST: 'LIST_MCQ'
  },
  CODING_QUESTION: {
    ADD: 'ADD_CODING_QUESTION',
    UPDATE: 'UPDATE_CODING_QUESTION',
    DELETE: 'DELETE_CODING_QUESTION',
    VIEW: 'VIEW_CODING_QUESTION',
    LIST: 'LIST_CODING_QUESTION'
  },
  NOTES: {
    ADD: 'ADD_NOTES',
    UPDATE: 'UPDATE_NOTES',
    DELETE: 'DELETE_NOTES',
    VIEW: 'VIEW_NOTES',
    LIST: 'LIST_NOTES'
  }
};

export interface LogParams {
  userId: string | mongoose.Types.ObjectId;
  userName: string;
  action: string;
  entityId?: string | mongoose.Types.ObjectId;
  entityType?: string;
  description: string;
  details?: Record<string, string | number | boolean | object | undefined>;
  req?: Request;
}

/**
 * Universal logger service for tracking system activities
 */
export const Logger = {
  /**
   * Log any activity in the system
   */
  async log(params: LogParams): Promise<IActivityLog | null> {
    try {
      const {
        userId,
        userName,
        action,
        entityId,
        entityType,
        description,
        details,
        req
      } = params;

      const logData: Partial<IActivityLog> = {
        userId:
          typeof userId === 'string'
            ? new mongoose.Types.ObjectId(userId)
            : userId,
        userName,
        action,
        description
      };

      if (entityId) {
        logData.entityId =
          typeof entityId === 'string'
            ? new mongoose.Types.ObjectId(entityId)
            : entityId;
      }

      if (entityType) {
        logData.entityType = entityType;
      }

      if (details) {
        logData.details = details;
      }

      // Add request-specific data if available
      if (req) {
        logData.ipAddress = req.ip || req.socket.remoteAddress;
        logData.userAgent = req.headers['user-agent'];
      }

      // Create and save activity log
      const activityLog = await activityLogDao.create(logData);
      return activityLog;
    } catch (error) {
      logger.error('Failed to log activity', { error });
      // Return null instead of throwing to prevent disrupting main flow
      return null;
    }
  }
};

export default Logger;
