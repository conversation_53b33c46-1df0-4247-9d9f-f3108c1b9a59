import mongoose from 'mongoose';
import {
  IEnrolledCourseLink,
  IEnrolledCourseLinkDao
} from '../interfaces/enrolled-course-link.interface';
import EnrolledCourseLink from '../models/enrolled-course-link.model';

export class EnrolledCourseLinkDao implements IEnrolledCourseLinkDao {
  async create(
    data: Partial<IEnrolledCourseLink>
  ): Promise<IEnrolledCourseLink> {
    return EnrolledCourseLink.create(data);
  }

  async createWithSession(
    data: Partial<IEnrolledCourseLink>,
    session: mongoose.ClientSession
  ): Promise<IEnrolledCourseLink | null> {
    return EnrolledCourseLink.create([data], { session }).then(docs => {
      if (docs.length > 0) {
        return docs[0];
      }
      return null;
    });
  }

  async findById(id: string): Promise<IEnrolledCourseLink | null> {
    return EnrolledCourseLink.findById(id).exec();
  }

  async findAll(
    filter: Record<string, string | number | boolean | object> | object = {},
    sort: Record<string, 1 | -1> = {},
    skip: number = 0,
    limit: number = 10
  ): Promise<IEnrolledCourseLink[]> {
    return EnrolledCourseLink.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .exec();
  }

  async findOne(
    filter: Record<string, string | number | boolean | object> | object
  ): Promise<IEnrolledCourseLink | null> {
    return EnrolledCourseLink.findOne(filter).exec();
  }

  async findOneWithSession(
    filter: Record<string, string | number | boolean | object> | object,
    session: mongoose.ClientSession
  ): Promise<IEnrolledCourseLink | null> {
    return EnrolledCourseLink.findOne(filter).session(session).exec();
  }

  async update(
    id: string,
    data: Partial<IEnrolledCourseLink>
  ): Promise<IEnrolledCourseLink | null> {
    return EnrolledCourseLink.findByIdAndUpdate(id, data, { new: true }).exec();
  }

  async delete(id: string): Promise<IEnrolledCourseLink | null> {
    return EnrolledCourseLink.findByIdAndDelete(id).exec();
  }
}

export default new EnrolledCourseLinkDao();
