import mongoose, { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rde<PERSON> } from 'mongoose';
import {
  IBatchPause,
  IBatchPauseDao
} from '../interfaces/batch-pause.interface';
import BatchPause from '../models/batch-pause.model';

export class BatchPauseDao implements IBatchPauseDao {
  /**
   * Find all batch pauses with pagination and filters
   */
  async findAll(
    filter: FilterQuery<IBatchPause>,
    sort: { [key: string]: SortOrder } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50
  ): Promise<IBatchPause[]> {
    return await BatchPause.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
  }

  /**
   * Find batch pause by ID
   */
  async findById(id: string): Promise<IBatchPause | null> {
    return await BatchPause.findById(id).lean();
  }

  /**
   * Find one batch pause by filter
   */
  async findOne(filter: FilterQuery<IBatchPause>): Promise<IBatchPause | null> {
    return await BatchPause.findOne(filter).lean();
  }

  /**
   * Create a new batch pause
   */
  async create(
    data: Partial<IBatchPause>,
    session?: mongoose.ClientSession
  ): Promise<IBatchPause> {
    const batchPause = await BatchPause.create([data], { session });
    return batchPause[0];
  }

  /**
   * Update an existing batch pause
   */
  async update(
    id: string,
    data: Partial<IBatchPause>
  ): Promise<IBatchPause | null> {
    return await BatchPause.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Delete a batch pause
   */
  async delete(id: string): Promise<IBatchPause | null> {
    return await BatchPause.findByIdAndDelete(id);
  }

  /**
   * Find active pauses by user
   */
  async findActiveByUser(userId: string): Promise<IBatchPause[]> {
    return await BatchPause.find({
      user: new mongoose.Types.ObjectId(userId),
      status: 'active'
    }).lean();
  }

  /**
   * Complete a pause and update days used
   */
  async completePause(
    id: string,
    daysUsed: number
  ): Promise<IBatchPause | null> {
    return await BatchPause.findByIdAndUpdate(
      id,
      {
        status: 'completed',
        daysUsed,
        endDate: new Date()
      },
      { new: true }
    );
  }
}

export default new BatchPauseDao();
