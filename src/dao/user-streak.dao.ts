import mongoose, { FilterQuery } from 'mongoose';
import {
  IUserStreak,
  IUserStreakDao
} from '../interfaces/user-streak.interface';
import UserStreak from '../models/user-streak.model';

export class UserStreakDao implements IUserStreakDao {
  /**
   * Find all user streaks with pagination and filters
   */
  async findAll(
    filter: FilterQuery<IUserStreak>,
    sort: { [key: string]: -1 | 1 } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50
  ): Promise<IUserStreak[]> {
    return await UserStreak.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
  }

  /**
   * Find user streak by ID
   */
  async findById(id: string): Promise<IUserStreak | null> {
    return await UserStreak.findById(id).lean();
  }

  /**
   * Find user streak by user ID
   */
  async findByUser(userId: string): Promise<IUserStreak | null> {
    return await UserStreak.findOne({
      user: new mongoose.Types.ObjectId(userId)
    }).lean();
  }

  /**
   * Find one user streak by filter
   */
  async findOne(filter: FilterQuery<IUserStreak>): Promise<IUserStreak | null> {
    return await UserStreak.findOne(filter).lean();
  }

  /**
   * Create a new user streak
   */
  async create(
    data: Partial<IUserStreak>,
    session?: mongoose.ClientSession
  ): Promise<IUserStreak> {
    const userStreak = await UserStreak.create([data], { session });
    return userStreak[0];
  }

  /**
   * Update an existing user streak
   */
  async update(
    id: string,
    data: Partial<IUserStreak>
  ): Promise<IUserStreak | null> {
    return await UserStreak.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Increment streak and add activity
   */
  async incrementStreak(
    userId: string,
    points: number,
    activityData: {
      type: 'lesson' | 'practice';
      contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab';
      entityId?: mongoose.Types.ObjectId;
      entityType?: string;
      pointsEarned: number;
    }
  ): Promise<IUserStreak | null> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let userStreak = await UserStreak.findOne({
      user: new mongoose.Types.ObjectId(userId)
    });

    if (!userStreak) {
      userStreak = await UserStreak.create({
        user: new mongoose.Types.ObjectId(userId),
        currentStreak: 1,
        longestStreak: 1,
        lastActivityDate: new Date(),
        streakHistory: [
          {
            date: today,
            pointsEarned: points,
            activities: [
              {
                type: activityData.type,
                contentType: activityData.contentType,
                entityId: activityData.entityId,
                entityType: activityData.entityType,
                pointsEarned: activityData.pointsEarned,
                timestamp: new Date()
              }
            ]
          }
        ],
        totalDaysActive: 1
      });
      return userStreak;
    }

    const lastActivityDate = new Date(userStreak.lastActivityDate);
    lastActivityDate.setHours(0, 0, 0, 0);

    // Handle streak increments across days
    if (lastActivityDate.getTime() === today.getTime()) {
      // Same day activity - keep current streak
    } else {
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const dayBeforeYesterday = new Date(yesterday);
      dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 1);

      // Check if activity was yesterday to maintain streak
      if (lastActivityDate.getTime() === yesterday.getTime()) {
        userStreak.currentStreak += 1;
        if (userStreak.currentStreak > userStreak.longestStreak) {
          userStreak.longestStreak = userStreak.currentStreak;
        }
      } else if (lastActivityDate.getTime() < yesterday.getTime()) {
        // Activity gap was more than 1 day - reset streak
        userStreak.currentStreak = 1;
      }
      // If last activity was future date (server time mismatch), keep current streak
    }

    // Add today's activity
    const todayHistoryIndex = userStreak.streakHistory.findIndex(
      h => new Date(h.date).setHours(0, 0, 0, 0) === today.getTime()
    );
    if (todayHistoryIndex !== -1) {
      // Deduplicate: only add if not already present (type + contentType + entityId)
      const exists = userStreak.streakHistory[
        todayHistoryIndex
      ].activities.some(
        act =>
          act.type === activityData.type &&
          act.contentType === activityData.contentType &&
          String(act.entityId) === String(activityData.entityId)
      );
      if (!exists) {
        userStreak.streakHistory[todayHistoryIndex].pointsEarned += points;
        userStreak.streakHistory[todayHistoryIndex].activities.push({
          type: activityData.type,
          contentType: activityData.contentType,
          entityId: activityData.entityId,
          entityType: activityData.entityType,
          pointsEarned: activityData.pointsEarned,
          timestamp: new Date()
        });
      }
    } else {
      userStreak.streakHistory.push({
        date: today,
        pointsEarned: points,
        activities: [
          {
            type: activityData.type,
            contentType: activityData.contentType,
            entityId: activityData.entityId,
            entityType: activityData.entityType,
            pointsEarned: activityData.pointsEarned,
            timestamp: new Date()
          }
        ]
      });
    }
    userStreak.lastActivityDate = new Date();
    if (userStreak.streakHistory.length > 100) {
      userStreak.streakHistory = userStreak.streakHistory.slice(-100);
    }
    await userStreak.save();
    return userStreak;
  }

  /**
   * Reset user streak
   */
  async resetStreak(userId: string): Promise<IUserStreak | null> {
    return await UserStreak.findOneAndUpdate(
      { user: new mongoose.Types.ObjectId(userId) },
      { currentStreak: 0 },
      { new: true }
    );
  }
}

export default new UserStreakDao();
