import mongoose, { FilterQuery } from 'mongoose';
import {
  ICodeSubmission,
  ICodeSubmissionDao
} from '../interfaces/code-submission.interface';
import CodeSubmission from '../models/code-submission';
import { Request } from 'express';

export class CodeSubmissionDao implements ICodeSubmissionDao {
  /**
   * Find all code submissions with pagination and filters
   */
  async findAll(
    filter: FilterQuery<ICodeSubmission>,
    sort: NonNullable<Request['sort']> = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50,
    populate?: string | string[]
  ): Promise<ICodeSubmission[]> {
    const query = CodeSubmission.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit);

    if (populate) {
      query.populate(populate);
    }

    return await query.lean();
  }

  /**
   * Find code submission by ID
   */
  async findById(
    id: string,
    populate?: string | string[]
  ): Promise<ICodeSubmission | null> {
    const query = CodeSubmission.findById(id);
    if (populate) {
      query.populate(populate);
    }
    return await query.lean();
  }

  /**
   * Find one code submission by filter
   */
  async findOne(
    filter: FilterQuery<ICodeSubmission>,
    populate?: string | string[]
  ): Promise<ICodeSubmission | null> {
    const query = CodeSubmission.findOne(filter);
    if (populate) {
      query.populate(populate);
    }
    return await query.lean();
  }

  /**
   * Create a new code submission
   */
  async create(
    data: Partial<ICodeSubmission>,
    session?: mongoose.ClientSession
  ): Promise<ICodeSubmission> {
    const codeSubmission = await CodeSubmission.create([data], { session });
    return codeSubmission[0];
  }

  /**
   * Update an existing code submission
   */
  async update(
    id: string,
    data: Partial<ICodeSubmission>
  ): Promise<ICodeSubmission | null> {
    return await CodeSubmission.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Delete a code submission
   */
  async delete(id: string): Promise<ICodeSubmission | null> {
    return await CodeSubmission.findByIdAndDelete(id);
  }
}

export default new CodeSubmissionDao();
