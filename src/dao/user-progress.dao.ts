import mongoose, { FilterQ<PERSON>y, UpdateQuery, QueryOptions } from 'mongoose';
import {
  IUserProgress,
  IUserProgressDao
} from '../interfaces/user-progress.interface';
import UserProgress from '../models/user-progress.model';

export class userProgressDao implements IUserProgressDao {
  async findAll(
    filter: FilterQuery<IUserProgress>,
    sort: { [key: string]: -1 | 1 } = { createdAt: -1 },
    skip?: number,
    limit?: number,
    populate?: string | string[]
  ): Promise<IUserProgress[]> {
    const query = UserProgress.find(filter);
    if (sort) query.sort(sort);
    if (typeof skip === 'number') query.skip(skip);
    if (typeof limit === 'number') query.limit(limit);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async create(
    data: Partial<IUserProgress>,
    session?: mongoose.ClientSession
  ): Promise<IUserProgress> {
    const userProgress = await UserProgress.create([data], { session });
    return userProgress[0];
  }

  async findById(
    id: string | mongoose.Types.ObjectId,
    populate?: string | string[]
  ): Promise<IUserProgress | null> {
    const query = UserProgress.findById(id);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async update(
    id: string,
    data: UpdateQuery<IUserProgress>,
    session?: mongoose.ClientSession
  ): Promise<IUserProgress | null> {
    return await UserProgress.findByIdAndUpdate(id, data, {
      new: true,
      session
    });
  }

  async findByUserAndCourse(
    userId: string | mongoose.Types.ObjectId,
    courseId: string | mongoose.Types.ObjectId,
    populate?: string | string[]
  ): Promise<IUserProgress | null> {
    const query = UserProgress.findOne({
      user: userId,
      course: courseId
    });
    if (populate) query.populate(populate);
    return query.exec();
  }

  async findOneAndUpdate(
    filter: FilterQuery<IUserProgress>,
    update: UpdateQuery<IUserProgress>,
    options: QueryOptions = { new: true },
    session?: mongoose.ClientSession
  ): Promise<IUserProgress | null> {
    if (session) {
      options.session = session;
    }
    return UserProgress.findOneAndUpdate(filter, update, options);
  }

  async findOne(
    filter: FilterQuery<IUserProgress>,
    populate?: string | string[]
  ): Promise<IUserProgress | null> {
    const query = UserProgress.findOne(filter);
    if (populate) query.populate(populate);
    return query.exec();
  }

  // DAO: userProgressDao.ts or similar
  async findOneWithSelection(
    filter: FilterQuery<IUserProgress>,
    projection?:
      | string
      | string[]
      | Record<string, string | number | boolean | object>,
    populate?: string | string[]
  ): Promise<IUserProgress | null> {
    const query = UserProgress.findOne(filter);

    if (projection) {
      if (typeof projection === 'object' && !Array.isArray(projection)) {
        // Supports object projection like { contentItemProgress: { $elemMatch: {...} } }
        query.select(projection);
      } else {
        const selection = Array.isArray(projection)
          ? projection.join(' ')
          : projection;
        query.select(selection);
      }
    }

    if (populate) query.populate(populate);

    return query.exec();
  }
}

export default new userProgressDao();
