// import mongoose from 'mongoose';
import mongoose, { FilterQuery, SortOrder } from 'mongoose';
import { ISubModule, ISubModuleDao } from '../interfaces/submodule.interface';
import SubModule from '../models/submodule.model';
import moduleDao from '../dao/module.dao';

export class SubModuleDao implements ISubModuleDao {
  async findAll(
    filter: FilterQuery<ISubModule>,
    sort: { [key: string]: SortOrder } = { createdAt: -1 },
    skip?: number,
    limit?: number,
    populate?: string | string[]
  ): Promise<ISubModule[]> {
    const query = SubModule.find(filter);
    if (sort) query.sort(sort);
    if (typeof skip === 'number') query.skip(skip);
    if (typeof limit === 'number') query.limit(limit);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async findById(
    id: string | mongoose.Types.ObjectId,
    populate?: string | string[]
  ): Promise<ISubModule | null> {
    const query = SubModule.findById(id);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async findOne(
    filter: FilterQuery<ISubModule>,
    sort?: { [key: string]: SortOrder },
    populate?: string | string[]
  ): Promise<ISubModule | null> {
    const query = SubModule.findOne(filter);
    if (sort) query.sort(sort);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async create(
    data: Partial<ISubModule>,
    session?: mongoose.ClientSession
  ): Promise<ISubModule> {
    const newSubModule = await SubModule.create([data], { session });
    return newSubModule[0];
  }

  async update(
    id: string,
    data: Partial<ISubModule>,
    session?: mongoose.ClientSession
  ): Promise<ISubModule | null> {
    return SubModule.findByIdAndUpdate(id, data, { new: true, session }).exec();
  }

  async simulateSubModuleReorder(
    course: mongoose.Types.ObjectId | string
  ): Promise<ISubModule[]> {
    const submodules = SubModule.find({
      module: new mongoose.Types.ObjectId(course)
    }).sort({ order: 1 });
    return submodules;
  }

  async bulkUpdateOrders(
    submodules: { _id: string; order: number }[]
  ): Promise<void> {
    const bulkOps = submodules.map(submodule => ({
      updateOne: {
        filter: { _id: new mongoose.Types.ObjectId(submodule._id) },
        update: { order: submodule.order }
      }
    }));
    await SubModule.bulkWrite(bulkOps);
  }

  async findAllByModule(moduleId: string): Promise<ISubModule[]> {
    return SubModule.find({ module: moduleId }).sort({ order: 1 }).exec();
  }

  async getNextSubmoduleAcrossModules(
    currentSubmodule: ISubModule
  ): Promise<ISubModule | null> {
    // Try next submodule in the same module
    const nextSubmodule = await this.findOne({
      module: currentSubmodule.module,
      order: currentSubmodule.order + 1
    });
    if (nextSubmodule) return nextSubmodule;

    // If not found, get the next module
    const moduleDoc = await moduleDao.findById(
      currentSubmodule.module as string
    );
    if (!moduleDoc) return null;
    const nextModule = await moduleDao.findOne({
      course: moduleDoc.course,
      order: moduleDoc.order + 1
    });
    if (!nextModule) return null;

    // Get the first submodule of the next module
    return this.findOne({ module: nextModule._id }, { order: 1 });
  }
}

export default new SubModuleDao();
