import mongoose, { Fi<PERSON><PERSON><PERSON>y, SortOrder } from 'mongoose';
import {
  ISubmissionStorage,
  ISubmissionStorageDao
} from '../interfaces/submission-storage.interface';
import SubmissionStorage from '../models/submission-storage';

export class SubmissionStorageDao implements ISubmissionStorageDao {
  /**
   * Find all submission storages with pagination and filters
   */
  async findAll(
    filter: FilterQuery<ISubmissionStorage>,
    sort: { [key: string]: SortOrder } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50,
    populate?: string | string[]
  ): Promise<ISubmissionStorage[]> {
    const query = SubmissionStorage.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit);

    if (populate) {
      query.populate(populate);
    }

    return await query.lean();
  }

  /**
   * Find submission storage by ID
   */
  async findById(
    id: string,
    populate?: string | string[]
  ): Promise<ISubmissionStorage | null> {
    const query = SubmissionStorage.findById(id);
    if (populate) {
      query.populate(populate);
    }
    return await query.lean();
  }

  /**
   * Find one submission storage by filter
   */
  async findOne(
    filter: FilterQuery<ISubmissionStorage>,
    populate?: string | string[]
  ): Promise<ISubmissionStorage | null> {
    const query = SubmissionStorage.findOne(filter);
    if (populate) {
      query.populate(populate);
    }
    return await query.lean();
  }

  /**
   * Create a new submission storage
   */
  async create(
    data: Partial<ISubmissionStorage>,
    session?: mongoose.ClientSession
  ): Promise<ISubmissionStorage> {
    const submissionStorage = await SubmissionStorage.create([data], {
      session
    });
    return submissionStorage[0];
  }

  /**
   * Update an existing submission storage
   */
  async update(
    filter: FilterQuery<ISubmissionStorage>,
    data: Partial<ISubmissionStorage> | object,
    session?: mongoose.ClientSession
  ): Promise<ISubmissionStorage | null> {
    return await SubmissionStorage.findOneAndUpdate(filter, data, {
      new: true,
      session
    });
  }

  /**
   * Delete a submission storage
   */
  async delete(
    filter: FilterQuery<ISubmissionStorage>,
    session?: mongoose.ClientSession
  ): Promise<ISubmissionStorage | null> {
    return await SubmissionStorage.findOneAndDelete(filter, { session });
  }
}

export default new SubmissionStorageDao();
