import mongoose, { FilterQuery } from 'mongoose';
import { ICodingLab, ICodingLabDao } from '../interfaces/coding-lab.interface';
import CodingLab from '../models/coding-lab.model';

export class CodingLabDao implements ICodingLabDao {
  /**
   * Find all coding labs with pagination and filters
   */
  async findAll(
    filter: FilterQuery<ICodingLab>,
    sort: { [key: string]: -1 | 1 } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50
  ): Promise<ICodingLab[]> {
    return await CodingLab.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
  }

  /**
   * Find coding lab by ID
   */
  async findById(id: string): Promise<ICodingLab | null> {
    return await CodingLab.findById(id).lean();
  }

  /**
   * Find one coding lab by filter
   */
  async findOne(filter: FilterQuery<ICodingLab>): Promise<ICodingLab | null> {
    return await CodingLab.findOne(filter).lean();
  }

  /**
   * Create a new coding lab
   */
  async create(
    data: Partial<ICodingLab>,
    session?: mongoose.ClientSession
  ): Promise<ICodingLab> {
    const codingLab = await CodingLab.create([data], { session });
    return codingLab[0];
  }

  /**
   * Update an existing coding lab
   */
  async update(
    id: string,
    data: Partial<ICodingLab>
  ): Promise<ICodingLab | null> {
    return await CodingLab.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Delete a coding lab
   */
  async delete(id: string): Promise<ICodingLab | null> {
    return await CodingLab.findByIdAndDelete(id);
  }

  /**
   * Find coding labs by tags
   */
  async findByTags(tags: string[]): Promise<ICodingLab[]> {
    return await CodingLab.find({ tags: { $in: tags } })
      .sort({ createdAt: -1 })
      .lean();
  }
}

export default new CodingLabDao();
