import mongoose, { FilterQuery, SortOrder } from 'mongoose';
import {
  IAnnouncement,
  IAnnouncementDao
} from '../interfaces/announcement.interface';
import Announcement from '../models/announcement.model';

export class AnnouncementDao implements IAnnouncementDao {
  /**
   * Create a new announcement
   */
  async create(
    data: Partial<IAnnouncement>,
    session?: mongoose.ClientSession
  ): Promise<IAnnouncement> {
    const announcement = await Announcement.create([data], { session });
    return announcement[0];
  }

  async findAll(
    filter: FilterQuery<IAnnouncement>,
    populate?: string | string[]
  ): Promise<IAnnouncement[]> {
    const query = Announcement.find(filter);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async findOne(
    filter: FilterQuery<IAnnouncement>,
    sort: { [key: string]: SortOrder } = { createdAt: -1 },
    populate?: string | string[]
  ): Promise<IAnnouncement | null> {
    const query = Announcement.findOne(filter);
    if (sort) query.sort(sort);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async update(
    filter: FilterQuery<IAnnouncement>,
    data: Partial<IAnnouncement>,
    session?: mongoose.ClientSession
  ): Promise<IAnnouncement | null> {
    const query = Announcement.findOneAndUpdate(filter, data, {
      new: true,
      session
    });
    return query.exec();
  }

  async delete(
    filter: FilterQuery<IAnnouncement>,
    session?: mongoose.ClientSession
  ): Promise<IAnnouncement | null> {
    const query = Announcement.findOneAndDelete(filter, { session });
    return query.exec();
  }

  async findById(
    id: string,
    populate?: string | string[]
  ): Promise<IAnnouncement | null> {
    const query = Announcement.findById(id);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async findByIdAndUpdate(
    id: string,
    data: Partial<IAnnouncement>,
    session?: mongoose.ClientSession
  ): Promise<IAnnouncement | null> {
    const query = Announcement.findByIdAndUpdate(id, data, {
      new: true,
      session
    });
    return query.exec();
  }
}

// Export singleton instance
const announcementDao = new AnnouncementDao();
export default announcementDao;
