import mongoose, { FilterQuery } from 'mongoose';
import {
  ICertificate,
  ICertificateDao
} from '../interfaces/certificate.interface';
import Certificate from '../models/certificate.model';

export class CertificateDao implements ICertificateDao {
  /**
   * Find all certificates with pagination and filters
   */
  async findAll(
    filter: FilterQuery<ICertificate>,
    sort: { [key: string]: -1 | 1 } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50
  ): Promise<ICertificate[]> {
    return await Certificate.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
  }

  /**
   * Find certificate by ID
   */
  async findById(id: string): Promise<ICertificate | null> {
    return await Certificate.findById(id).lean();
  }

  /**
   * Find certificate by certificate ID
   */
  async findByCertificateId(
    certificateId: string
  ): Promise<ICertificate | null> {
    return await Certificate.findOne({ certificateId }).lean();
  }

  /**
   * Find one certificate by filter
   */
  async findOne(
    filter: FilterQuery<ICertificate>
  ): Promise<ICertificate | null> {
    return await Certificate.findOne(filter).lean();
  }

  /**
   * Create a new certificate
   */
  async create(
    data: Partial<ICertificate>,
    session?: mongoose.ClientSession
  ): Promise<ICertificate> {
    const certificate = await Certificate.create([data], { session });
    return certificate[0];
  }

  /**
   * Update an existing certificate
   */
  async update(
    id: string,
    data: Partial<ICertificate>
  ): Promise<ICertificate | null> {
    return await Certificate.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Delete a certificate
   */
  async delete(id: string): Promise<ICertificate | null> {
    return await Certificate.findByIdAndDelete(id);
  }

  /**
   * Find certificates by user
   */
  async findByUser(userId: string): Promise<ICertificate[]> {
    return await Certificate.find({
      user: new mongoose.Types.ObjectId(userId)
    }).lean();
  }

  /**
   * Find certificates by course
   */
  async findByCourse(courseId: string): Promise<ICertificate[]> {
    return await Certificate.find({
      course: new mongoose.Types.ObjectId(courseId)
    }).lean();
  }
}

export default new CertificateDao();
