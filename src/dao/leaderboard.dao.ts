import mongoose, { FilterQuery } from 'mongoose';
import {
  ILeaderboard,
  ILeaderboardDao
} from '../interfaces/leaderboard.interface';
import Leaderboard from '../models/leaderboard.model';

export class LeaderboardDao implements ILeaderboardDao {
  /**
   * Find all leaderboards with pagination and filters
   */
  async findAll(
    filter: FilterQuery<ILeaderboard>,
    sort: { [key: string]: -1 | 1 } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50
  ): Promise<ILeaderboard[]> {
    return await Leaderboard.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
  }

  /**
   * Find leaderboard by ID
   */
  async findById(id: string): Promise<ILeaderboard | null> {
    return await Leaderboard.findById(id).lean();
  }

  /**
   * Find one leaderboard by filter
   */
  async findOne(
    filter: FilterQuery<ILeaderboard>
  ): Promise<ILeaderboard | null> {
    return await Leaderboard.findOne(filter).lean();
  }

  /**
   * Create a new leaderboard
   */
  async create(
    data: Partial<ILeaderboard>,
    session?: mongoose.ClientSession
  ): Promise<ILeaderboard> {
    const leaderboard = await Leaderboard.create([data], { session });
    return leaderboard[0];
  }

  /**
   * Update an existing leaderboard
   */
  async update(
    id: string,
    data: Partial<ILeaderboard>
  ): Promise<ILeaderboard | null> {
    return await Leaderboard.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Delete a leaderboard
   */
  async delete(id: string): Promise<ILeaderboard | null> {
    return await Leaderboard.findByIdAndDelete(id);
  }

  /**
   * Find leaderboard by batch and period
   */
  async findByBatch(
    batchId: string,
    period: 'daily' | 'weekly' | 'monthly' | 'overall'
  ): Promise<ILeaderboard | null> {
    return await Leaderboard.findOne({
      batch: new mongoose.Types.ObjectId(batchId),
      period
    })
      .sort({ lastUpdated: -1 })
      .lean();
  }

  /**
   * Update rankings of a leaderboard
   */
  async updateRankings(
    id: string,
    rankings: ILeaderboard['rankings']
  ): Promise<ILeaderboard | null> {
    return await Leaderboard.findByIdAndUpdate(
      id,
      {
        rankings,
        lastUpdated: new Date()
      },
      { new: true }
    );
  }
}

export default new LeaderboardDao();
