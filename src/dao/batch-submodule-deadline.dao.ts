import BatchSubmoduleDeadline from '../models/batch-submodule-deadline.model';
import {
  IBatchSubmoduleDeadline,
  IBatchSubmoduleDeadlineDao
} from '../interfaces/batch-submodule-deadline.interface';
import mongoose, { FilterQuery, SortOrder } from 'mongoose';

export class BatchSubmoduleDeadlineDao implements IBatchSubmoduleDeadlineDao {
  /**
   * Find all batch submodule deadlines with pagination and filters
   */
  async findAll(
    filter: FilterQuery<IBatchSubmoduleDeadline>,
    sort: { [key: string]: SortOrder } = { createdAt: -1 },
    skip: number,
    limit: number
  ): Promise<IBatchSubmoduleDeadline[]> {
    return await BatchSubmoduleDeadline.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
  }
  /**
   * Find batch submodule deadline by ID
   */
  async findById(id: string): Promise<IBatchSubmoduleDeadline | null> {
    return await BatchSubmoduleDeadline.findById(id).lean();
  }

  /**
   * Create a new batch submodule deadline
   */
  async create(
    data: Partial<IBatchSubmoduleDeadline>,
    session?: mongoose.ClientSession
  ): Promise<IBatchSubmoduleDeadline> {
    const batchSubmoduleDeadline = await BatchSubmoduleDeadline.create([data], {
      session
    });
    return batchSubmoduleDeadline[0];
  }

  async update(
    id: string,
    data: Partial<IBatchSubmoduleDeadline>,
    session?: mongoose.ClientSession
  ): Promise<IBatchSubmoduleDeadline | null> {
    return await BatchSubmoduleDeadline.findByIdAndUpdate(id, data, {
      new: true,
      session
    }).lean();
  }

  async delete(id: string): Promise<IBatchSubmoduleDeadline | null> {
    return await BatchSubmoduleDeadline.findByIdAndDelete(id);
  }
  async findOne(
    filter: FilterQuery<IBatchSubmoduleDeadline>
  ): Promise<IBatchSubmoduleDeadline | null> {
    return await BatchSubmoduleDeadline.findOne(filter).lean();
  }

  async findBySubmoduleAndUpdate(
    submodule: string | mongoose.Types.ObjectId,
    data: Partial<IBatchSubmoduleDeadline>,
    session?: mongoose.ClientSession
  ): Promise<IBatchSubmoduleDeadline | null> {
    return await BatchSubmoduleDeadline.findOneAndUpdate({ submodule }, data, {
      new: true,
      session: session || undefined
    }).lean();
  }

  /**
   * Find a batch-submodule deadline record and update it, or create if it doesn't exist
   * @param filter - The filter to find the record
   * @param update - The data to update
   * @param options - MongoDB update options
   * @returns The updated or newly created document
   */
  async findOneAndUpdate(
    filter: {
      batch: string | mongoose.Types.ObjectId;
      submodule: string | mongoose.Types.ObjectId;
    },
    update: {
      startDate: Date;
      deadline: Date;
      days: number;
      penaltyRules: {
        daysLate: number;
        penaltyPercentage: number;
      }[];
    },
    options: { upsert: boolean; new: boolean } = { upsert: true, new: true }
  ): Promise<IBatchSubmoduleDeadline | null> {
    try {
      const result = await BatchSubmoduleDeadline.findOneAndUpdate(
        filter,
        update,
        options
      );
      return result;
    } catch (error) {
      console.error(
        'Error in findOneAndUpdate batch-submodule-deadline:',
        error
      );
      throw error;
    }
  }
}

export default new BatchSubmoduleDeadlineDao();
