import mongoose, { FilterQuery, SortOrder } from 'mongoose';
import { IModule, IModuleDao } from '../interfaces/module.interface';
import Module from '../models/module.model';

interface ModuleWithSubModule {
  _id: string;
  title: string;
  description: string;
  order: number;
  duration: number;
  totalPoints: number;
  submodules: {
    _id: string;
    title: string;
    description: string;
    order: number;
    duration: number;
    totalPoints: number;
    isUnlocked: boolean;
    deadlineInfo: {
      startDate: Date | null;
      deadline: Date | null;
      dependsOnPreviousSubmodule: boolean | null;
    } | null;
  }[];
}

export class ModuleDao implements IModuleDao {
  async findAll(
    filter: FilterQuery<IModule>,
    sort: { [key: string]: SortOrder } = { createdAt: -1 },
    skip?: number,
    limit?: number,
    populate?: string | string[]
  ): Promise<IModule[]> {
    const query = Module.find(filter);
    if (sort) query.sort(sort);
    if (typeof skip === 'number') query.skip(skip);
    if (typeof limit === 'number') query.limit(limit);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async findById(
    id: string | mongoose.Types.ObjectId,
    populate?: string | string[]
  ): Promise<IModule | null> {
    const query = Module.findById(id);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async findOne(
    filter: FilterQuery<IModule>,
    populate?: string | string[]
  ): Promise<IModule | null> {
    const query = Module.findOne(filter);
    if (populate) query.populate(populate);
    return query.exec();
  }

  async create(
    data: Partial<IModule>,
    session?: mongoose.ClientSession
  ): Promise<IModule> {
    const module = await Module.create([data], { session });
    return module[0];
  }

  async update(
    id: string,
    data: Partial<IModule>,
    session?: mongoose.ClientSession
  ): Promise<IModule | null> {
    return Module.findByIdAndUpdate(id, data, { new: true, session }).exec();
  }

  async simulateModuleReorder(
    course: mongoose.Types.ObjectId | string
  ): Promise<IModule[]> {
    const modules = await Module.find({
      course: new mongoose.Types.ObjectId(course)
    })
      .sort({ order: 1 })
      .exec();
    return modules;
  }
  async reorderModules(modules: IModule[]): Promise<void> {
    const bulkOps = modules.map((mod, index) => ({
      updateOne: {
        filter: { _id: mod._id },
        update: { $set: { order: index + 1 } }
      }
    }));

    if (bulkOps.length > 0) {
      await Module.bulkWrite(bulkOps);
    }
  }
  async bulkUpdateOrders(
    modules: { _id: string; order: number }[]
  ): Promise<void> {
    if (!modules || modules.length === 0) return;

    const bulkOps = modules.map(mod => ({
      updateOne: {
        filter: { _id: mod._id },
        update: { $set: { order: mod.order } }
      }
    }));

    await Module.bulkWrite(bulkOps);
  }

  async getModulesWithSubmodulesAndDeadlines(
    courseId: string,
    batchId: string,
    userId: string
  ): Promise<
    {
      _id: string;
      title: string;
      description: string;
      order: number;
      duration: number;
      totalPoints: number;
      submodules: {
        _id: string;
        title: string;
        description: string;
        order: number;
        duration: number;
        totalPoints: number;
        isUnlocked: boolean;
        deadlineInfo: {
          startDate: Date | null;
          deadline: Date | null;
          dependsOnPreviousSubmodule: boolean | null;
        } | null;
      }[];
    }[]
  > {
    // Use the static method from the Module model
    return Module.getWithSubmodulesAndDeadlines(courseId, batchId, userId);
  }
  /**
   * Get modules with submodules (without deadlines)
   */
  async getModulesWithSubmodules(
    courseId: string | mongoose.Types.ObjectId
  ): Promise<ModuleWithSubModule[]> {
    return await Module.aggregate([
      {
        $match: {
          course: new mongoose.Types.ObjectId(courseId)
        }
      },
      {
        $sort: { order: 1 }
      },
      {
        $lookup: {
          from: 'submodules',
          let: { moduleId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$module', '$$moduleId']
                }
              }
            },
            {
              $sort: { order: 1 }
            }
          ],
          as: 'submodules'
        }
      }
    ]);
  }
}

export default new ModuleDao();
