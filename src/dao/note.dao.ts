import mongoose from 'mongoose';
import { INote, INoteDao } from '../interfaces/note.interface';
import Note from '../models/note.model';

export class NoteDao implements INoteDao {
  /**
   * Create a new note
   */
  async create(
    data: Partial<INote>,
    session?: mongoose.ClientSession
  ): Promise<INote> {
    const note = await Note.create([data], { session });
    return note[0];
  }

  /**
   * Find a note by ID
   */
  async findById(
    id: string | mongoose.Types.ObjectId,
    populate?: string | string[]
  ): Promise<INote | null> {
    const query = Note.findById(id);
    if (populate) query.populate(populate);
    query.lean();
    return query.exec();
  }

  /**
   * Update a note
   */
  async update(
    id: string | mongoose.Types.ObjectId,
    data: Partial<INote>,
    session?: mongoose.ClientSession
  ): Promise<INote | null> {
    const query = Note.findOneAndUpdate({ _id: id }, data, {
      new: true,
      session
    });
    query.lean();
    return query.exec();
  }

  /**
   * Delete a note
   */
  async delete(id: string | mongoose.Types.ObjectId): Promise<INote | null> {
    // Check if the note exists before deleting
    const note = await this.findById(id);
    if (!note) {
      throw new Error(`Note with ID not found`);
    }
    const query = Note.findByIdAndDelete(id);
    query.lean();
    return query.exec();
  }
}

export default new NoteDao();
