import mongoose, { FilterQuery } from 'mongoose';
import { IVideo, IVideoDao } from '../interfaces/video.interface';
import Video from '../models/video.model';

export class videoDao implements IVideoDao {
  async findAll(
    filter: FilterQuery<IVideo>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number,
    populate?: string | string[]
  ): Promise<IVideo[]> {
    const query = Video.find(filter);
    if (sort) query.sort(sort);
    if (typeof skip === 'number') query.skip(skip);
    if (typeof limit === 'number') query.limit(limit);
    if (populate) query.populate(populate);
    return query.exec();
  }
  async create(
    data: Partial<IVideo>,
    session?: mongoose.ClientSession
  ): Promise<IVideo> {
    const video = await Video.create([data], { session });
    return video[0];
  }

  async findById(
    id: string | mongoose.Types.ObjectId,
    populate?: string | string[]
  ): Promise<IVideo | null> {
    const query = Video.findById(id);
    if (populate) query.populate(populate);
    query.lean(); // Add this line to convert the document to a plain JavaScript object
    return query.exec();
  }

  async update(
    id: string,
    data: Partial<IVideo>,
    session?: mongoose.ClientSession
  ): Promise<IVideo | null> {
    return await Video.findByIdAndUpdate(id, data, {
      new: true,
      session
    });
  }

  async delete(
    id: string | mongoose.Types.ObjectId,
    session?: mongoose.ClientSession
  ) {
    const video = await Video.findByIdAndDelete(id, { session });
    return video;
  }
}

export default new videoDao();
