import mongoose, { FilterQuery } from 'mongoose';
import { IDoubt, IDoubtDao } from '../interfaces/doubt.interface';
import Doubt from '../models/doubt.model';

export class DoubtDao implements IDoubtDao {
  /**
   * Create a new doubt
   */
  async create(
    data: Partial<IDoubt>,
    session?: mongoose.ClientSession
  ): Promise<IDoubt> {
    const doubt = await Doubt.create([data], { session });
    return doubt[0];
  }

  /**
   * Find doubt by ID
   */
  async findById(id: string): Promise<IDoubt | null> {
    return await Doubt.findById(id)
      .populate('user', 'name email avatar')
      .populate('assignedTo', 'name email avatar')
      .populate('resolvedBy', 'name email avatar')
      .lean();
  }

  /**
   * Find all doubts with pagination and filters
   */
  async findAll(
    filter: FilterQuery<IDoubt>,
    sort: { [key: string]: -1 | 1 } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50
  ): Promise<IDoubt[]> {
    return await Doubt.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('user', 'name email avatar')
      .populate('assignedTo', 'name email avatar')
      .lean();
  }

  /**
   * Update a doubt
   */
  async update(id: string, data: Partial<IDoubt>): Promise<IDoubt | null> {
    return await Doubt.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Add a response to a doubt
   */

  /**
   * Mark a doubt as resolved
   */
  async markAsResolved(
    id: string,
    resolvedBy: mongoose.Types.ObjectId
  ): Promise<IDoubt | null> {
    return await Doubt.findByIdAndUpdate(
      id,
      {
        status: 'resolved',
        resolvedBy,
        resolvedAt: new Date()
      },
      { new: true }
    );
  }

  /**
   * Assign a doubt to a user
   */
  async assignDoubt(
    id: string,
    assignedTo: mongoose.Types.ObjectId
  ): Promise<IDoubt | null> {
    return await Doubt.findByIdAndUpdate(
      id,
      {
        assignedTo,
        status: 'in_progress'
      },
      { new: true }
    );
  }

  /**
   * Count doubts by status
   */
}

export default new DoubtDao();
