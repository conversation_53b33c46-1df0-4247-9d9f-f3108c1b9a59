import mongoose, { FilterQuery } from 'mongoose';
import {
  ICodePairSession,
  ICodePairSessionDao
} from '../interfaces/code-pair-session.interface';
import CodePairSession from '../models/code-pair-session.model';
import { Request } from 'express';

export class CodePairSessionDao implements ICodePairSessionDao {
  /**
   * Find all code pair sessions with pagination and filters
   */
  async findAll(
    filter: FilterQuery<ICodePairSession>,
    sort: NonNullable<Request['sort']> = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50,
    populate?: string | string[]
  ): Promise<ICodePairSession[]> {
    const query = CodePairSession.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit);

    if (populate) {
      query.populate(populate);
    }

    return await query.lean();
  }

  /**
   * Find code pair session by ID
   */
  async findById(
    id: string,
    populate?: string | string[]
  ): Promise<ICodePairSession | null> {
    const query = CodePairSession.findById(id);
    if (populate) {
      query.populate(populate);
    }
    return await query.lean();
  }

  /**
   * Find one code pair session by filter
   */
  async findOne(
    filter: FilterQuery<ICodePairSession>,
    populate?: string | string[]
  ): Promise<ICodePairSession | null> {
    const query = CodePairSession.findOne(filter);
    if (populate) {
      query.populate(populate);
    }
    return await query.lean();
  }

  /**
   * Create a new code pair session
   */
  async create(
    data: Partial<ICodePairSession>,
    session?: mongoose.ClientSession
  ): Promise<ICodePairSession> {
    const codePairSession = await CodePairSession.create([data], { session });
    return codePairSession[0];
  }

  /**
   * Update an existing code pair session
   */
  async update(
    id: string,
    data: Partial<ICodePairSession>
  ): Promise<ICodePairSession | null> {
    return await CodePairSession.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Delete a code pair session
   */
  async delete(id: string): Promise<ICodePairSession | null> {
    return await CodePairSession.findByIdAndDelete(id);
  }
}

export default new CodePairSessionDao();
