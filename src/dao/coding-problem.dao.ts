import mongoose, { FilterQuery } from 'mongoose';
import {
  ICodingProblem,
  ICodingProblemDao
} from '../interfaces/coding-problem.interface';
import CodingProblem from '../models/coding-problem.model';

export class CodingProblemDao implements ICodingProblemDao {
  /**
   * Find all coding problems with pagination and filters
   */
  async findAll(
    filter: FilterQuery<ICodingProblem>,
    sort: { [key: string]: -1 | 1 } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50
  ): Promise<ICodingProblem[]> {
    return await CodingProblem.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
  }

  /**
   * Find coding problem by ID
   */
  async findById(id: string): Promise<ICodingProblem | null> {
    return await CodingProblem.findById(id).lean();
  }

  /**
   * Find coding problem by problemId
   */
  async findByProblemId(problemId: string): Promise<ICodingProblem | null> {
    return await CodingProblem.findOne({ problemId }).lean();
  }

  /**
   * Find one coding problem by filter
   */
  async findOne(
    filter: FilterQuery<ICodingProblem>
  ): Promise<ICodingProblem | null> {
    return await CodingProblem.findOne(filter).lean();
  }

  /**
   * Create a new coding problem
   */
  async create(
    data: Partial<ICodingProblem>,
    session?: mongoose.ClientSession
  ): Promise<ICodingProblem> {
    const codingProblem = await CodingProblem.create([data], { session });
    return codingProblem[0];
  }

  /**
   * Update an existing coding problem
   */
  async update(
    id: string,
    data: Partial<ICodingProblem>
  ): Promise<ICodingProblem | null> {
    return await CodingProblem.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Delete a coding problem
   */
  async delete(
    id: string,
    session?: mongoose.ClientSession
  ): Promise<ICodingProblem | null> {
    return await CodingProblem.findByIdAndDelete(id, { session });
  }

  /**
   * Find coding problems by tags and optional difficulty
   */

  /**
   * Find coding problems that have boilerplate or solutions in a specific language
   */
  async findByLanguage(language: string): Promise<ICodingProblem[]> {
    return await CodingProblem.find({
      $or: [
        { 'boilerplate.language': language },
        { 'solutions.language': language }
      ]
    }).lean();
  }
}

export default new CodingProblemDao();
