import mongoose, { Filter<PERSON><PERSON>y, SortOrder, Types } from 'mongoose';
import {
  ISubmoduleContent,
  ISubmoduleContentDao
} from '../interfaces/submodule-content.interface';
import SubmoduleContent from '../models/submodule-content.model';

export class SubmoduleContentDao implements ISubmoduleContentDao {
  /**
   * Find all submodule contents with pagination and filters
   */
  async findAll(
    filter: FilterQuery<ISubmoduleContent>,
    sort: { [key: string]: SortOrder } = { createdAt: -1 },
    skip?: number,
    limit?: number
  ): Promise<ISubmoduleContent[]> {
    const query = SubmoduleContent.find(filter);
    if (sort) query.sort(sort);
    if (typeof skip === 'number') query.skip(skip);
    if (typeof limit === 'number') query.limit(limit);
    return query.exec();
  }

  /**
   * Find submodule content by ID
   */
  async findById(id: string): Promise<ISubmoduleContent | null> {
    return await SubmoduleContent.findById(id).lean();
  }

  /**
   * Create a new submodule content
   */
  async create(
    data: Partial<ISubmoduleContent>,
    session?: mongoose.ClientSession
  ): Promise<ISubmoduleContent> {
    const submoduleContent = await SubmoduleContent.create([data], { session });
    return submoduleContent[0];
  }

  async update(
    id: string,
    data: Partial<ISubmoduleContent>,
    session?: mongoose.ClientSession
  ): Promise<ISubmoduleContent | null> {
    return await SubmoduleContent.findByIdAndUpdate(id, data, {
      new: true,
      session
    });
  }

  async delete(id: string): Promise<ISubmoduleContent | null> {
    return await SubmoduleContent.findByIdAndDelete(id);
  }

  async findOne(
    filter: FilterQuery<ISubmoduleContent>
  ): Promise<ISubmoduleContent | null> {
    return await SubmoduleContent.findOne(filter);
  }

  async bulkUpdateOrders(
    data: { _id: string; order: number }[],
    session?: mongoose.ClientSession
  ): Promise<void> {
    const bulkOps = data.map(item => ({
      updateOne: {
        filter: { _id: item._id },
        update: { order: item.order },
        session
      }
    }));
    await SubmoduleContent.bulkWrite(bulkOps);
  }

  async simulateSubModuleReorder(
    submoduleId: mongoose.Types.ObjectId | string
  ): Promise<ISubmoduleContent[] | null> {
    const submoduleContents = await SubmoduleContent.find({
      submodule: submoduleId
    }).sort({ order: 1 });
    return submoduleContents;
  }

  async findByContentIdAndTypeWithCustomPopulate(
    contentId: string | mongoose.Types.ObjectId,
    contentType: string,
    populatePaths?: { path: string; populate?: { path: string } }[]
  ): Promise<ISubmoduleContent | null> {
    const query = SubmoduleContent.findOne({
      contentId: new mongoose.Types.ObjectId(contentId),
      contentType: contentType
    });

    if (populatePaths && populatePaths.length > 0) {
      populatePaths.forEach(populateOption => {
        query.populate(populateOption);
      });
    }

    return query.exec();
  }

  async findByContentIdAndType(
    contentId: string | mongoose.Types.ObjectId,
    contentType: string,
    populate?: boolean
  ): Promise<ISubmoduleContent | null> {
    const query = SubmoduleContent.findOne({
      contentId: new mongoose.Types.ObjectId(contentId),
      contentType: contentType
    });

    if (populate) {
      query.populate({
        path: 'submodule',
        populate: {
          path: 'module'
        }
      });
    }

    return query.exec();
  }

  async findAllBySubmodule(subModuleId: string): Promise<ISubmoduleContent[]> {
    const submodules = await SubmoduleContent.find({
      submodule: new Types.ObjectId(subModuleId),
      isActive: true
    }).populate('contentId'); // Populate the contentId to access the points field

    return submodules;
  }
}

// make a static method to calculate all the submodule content orders

export default new SubmoduleContentDao();
