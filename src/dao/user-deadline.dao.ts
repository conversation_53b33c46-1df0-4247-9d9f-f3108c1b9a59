import mongoose, { FilterQuery } from 'mongoose';
import {
  IUserDeadline,
  IUserDeadlineDao
} from '../interfaces/user-deadline.interface';
import UserDeadline from '../models/user-deadline.model';

export class UserDeadlineDao implements IUserDeadlineDao {
  /**
   * Find all user deadlines with pagination and filters
   */
  async findAll(
    filter: FilterQuery<IUserDeadline>,
    sort: { [key: string]: -1 | 1 } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50
  ): Promise<IUserDeadline[]> {
    return await UserDeadline.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
  }

  /**
   * Find user deadline by ID
   */
  async findById(id: string): Promise<IUserDeadline | null> {
    return await UserDeadline.findById(id).lean();
  }

  /**
   * Find one user deadline by filter
   */
  async findOne(
    filter: FilterQuery<IUserDeadline>
  ): Promise<IUserDeadline | null> {
    return await UserDeadline.findOne(filter).lean();
  }

  /**
   * Create a new user deadline
   */
  async create(
    data: Partial<IUserDeadline>,
    session?: mongoose.ClientSession
  ): Promise<IUserDeadline> {
    const userDeadline = await UserDeadline.create([data], { session });
    return userDeadline[0];
  }

  /**
   * Update an existing user deadline
   */
  async update(
    id: string,
    data: Partial<IUserDeadline>
  ): Promise<IUserDeadline | null> {
    return await UserDeadline.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Delete a user deadline
   */
  async delete(id: string): Promise<IUserDeadline | null> {
    return await UserDeadline.findByIdAndDelete(id);
  }

  /**
   * Find user deadlines by user and batch
   */
  async findByUserAndBatch(
    userId: string,
    batchId: string,
    submoduleId?: string
  ): Promise<IUserDeadline[]> {
    return await UserDeadline.find({
      user: new mongoose.Types.ObjectId(userId),
      batch: new mongoose.Types.ObjectId(batchId),
      ...(submoduleId && {
        submodule: new mongoose.Types.ObjectId(submoduleId)
      }),
      isActive: true
    })
      .sort({ adjustedDeadline: 1 })
      .lean();
  }

  /**
   * Find user deadline by user and submodule
   */
  async findByUserAndSubmodule(
    userId: string,
    submoduleId: string
  ): Promise<IUserDeadline | null> {
    return await UserDeadline.findOne({
      user: new mongoose.Types.ObjectId(userId),
      submodule: new mongoose.Types.ObjectId(submoduleId),
      isActive: true
    }).lean();
  }

  /**
   * Adjust deadlines by adding pause days
   * Returns the number of affected deadlines
   */
  async adjustDeadline(
    userId: string | mongoose.Types.ObjectId,
    batchId: string | mongoose.Types.ObjectId,
    pauseDays: number,
    pauseStartDate: Date
  ): Promise<number> {
    // Convert string IDs to ObjectId if needed

    // Call the static method to adjust deadlines
    const modifiedCount = await UserDeadline.adjustDeadlinesForPause(
      userId as mongoose.Types.ObjectId,
      batchId as mongoose.Types.ObjectId,
      pauseDays,
      pauseStartDate
    );

    return modifiedCount;
  }

  async adjustDeadlinesForEarlyResume(
    userId: string | mongoose.Types.ObjectId,
    batchId: string | mongoose.Types.ObjectId,
    daysToAdjustBack: number
  ): Promise<number> {
    // Convert string IDs to ObjectId if needed
    const userObjId =
      typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;

    const batchObjId =
      typeof batchId === 'string'
        ? new mongoose.Types.ObjectId(batchId)
        : batchId;

    // Call the static method to adjust deadlines
    const modifiedCount = await UserDeadline.adjustDeadlinesForEarlyResumption(
      userObjId,
      batchObjId,
      daysToAdjustBack
    );

    return modifiedCount;
  }
}

export default new UserDeadlineDao();
