import mongoose, { FilterQuery } from 'mongoose';
import { IMCQ, IMCQDao } from '../interfaces/mcq.interface';
import MCQ from '../models/mcq.model';

export class MCQDao implements IMCQDao {
  /**
   * Find all MCQs with pagination and filters
   */
  async findAll(
    filter: FilterQuery<IMCQ>,
    sort: { [key: string]: -1 | 1 } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50
  ): Promise<IMCQ[]> {
    return await MCQ.find(filter).sort(sort).skip(skip).limit(limit).lean();
  }

  /**
   * Find MCQ by ID
   */
  async findById(id: string): Promise<IMCQ | null> {
    return await MCQ.findById(id).lean();
  }

  /**
   * Find one MCQ by filter
   */
  async findOne(filter: FilterQuery<IMCQ>): Promise<IMCQ | null> {
    return await MCQ.findOne(filter).lean();
  }

  /**
   * Create a new MCQ
   */
  async create(
    data: Partial<IMCQ>,
    session?: mongoose.ClientSession
  ): Promise<IMCQ> {
    const mcq = await MCQ.create([data], { session });
    return mcq[0];
  }

  /**
   * Update an existing MCQ
   */
  async update(id: string, data: Partial<IMCQ>): Promise<IMCQ | null> {
    return await MCQ.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Delete an MCQ
   */
  async delete(id: string): Promise<IMCQ | null> {
    return await MCQ.findByIdAndDelete(id);
  }
}

export default new MCQDao();
