import mongoose, { <PERSON><PERSON><PERSON><PERSON><PERSON>, SortOrder } from 'mongoose';
import { IBookmark, IBookmarkDao } from '../interfaces/bookmark.interface';
import Bookmark from '../models/bookmark.model';

export class BookmarkDao implements IBookmarkDao {
  /**
   * Find all bookmarks with pagination and filters
   */
  async findAll(
    filter: FilterQuery<IBookmark>,
    sort: { [key: string]: SortOrder } = { createdAt: -1 },
    skip: number = 0,
    limit: number = 50
  ): Promise<IBookmark[]> {
    return await Bookmark.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
  }

  /**
   * Find bookmark by ID
   */
  async findById(id: string): Promise<IBookmark | null> {
    return await Bookmark.findById(id).lean();
  }

  /**
   * Find one bookmark by filter
   */
  async findOne(filter: FilterQuery<IBookmark>): Promise<IBookmark | null> {
    return await Bookmark.findOne(filter).lean();
  }

  /**
   * Create a new bookmark
   */
  async create(
    data: Partial<IBookmark>,
    session?: mongoose.ClientSession
  ): Promise<IBookmark> {
    const bookmark = await Bookmark.create([data], { session });
    return bookmark[0];
  }

  /**
   * Update an existing bookmark
   */
  async update(
    id: string,
    data: Partial<IBookmark>
  ): Promise<IBookmark | null> {
    return await Bookmark.findByIdAndUpdate(id, data, { new: true });
  }

  /**
   * Delete a bookmark
   */
  async delete(id: string): Promise<IBookmark | null> {
    return await Bookmark.findByIdAndDelete(id);
  }

  /**
   * Get user bookmarks by batch
   */
  async getUserBookmarksByBatchId(
    userId: string | mongoose.Types.ObjectId,
    batchId: string | mongoose.Types.ObjectId,
    populate?: string | string[]
  ): Promise<IBookmark[]> {
    const query = Bookmark.find({
      user: userId,
      batch: batchId,
      isActive: true
    });

    if (populate) {
      query.populate(populate);
    }

    return await query.lean();
  }

  /**
   * Toggle bookmark status
   */
  async toggleBookmark(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId,
    contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
    contentId: mongoose.Types.ObjectId
  ): Promise<{ bookmarked: boolean; message: string }> {
    const bookmark = await Bookmark.toggleBookmark(
      userId,
      batchId,
      submoduleId,
      contentType,
      contentId
    );
    return bookmark;
  }

  /**
   * Check if content is bookmarked
   */
  async isBookmarked(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
    contentId: mongoose.Types.ObjectId
  ): Promise<boolean> {
    const bookmark = await Bookmark.findOne({
      user: userId,
      batch: batchId,
      contentType,
      contentId,
      isActive: true
    });

    return !!bookmark;
  }
}

export default new BookmarkDao();
