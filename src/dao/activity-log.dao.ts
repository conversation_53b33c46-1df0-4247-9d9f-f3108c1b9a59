import mongoose, { SortOrder } from 'mongoose';
import ActivityLogModel from '../models/activity-log.model';
import {
  IActivityLog,
  IActivityLogDao
} from '../interfaces/activity-log.interface';

export class ActivityLogDao implements IActivityLogDao {
  /**
   * Find all activity logs with pagination and filters
   */
  async findAll(
    filter: Record<string, string | number | boolean | Date> | object,
    sort: Record<string, SortOrder>,
    skip: number,
    limit: number
  ): Promise<IActivityLog[]> {
    return await ActivityLogModel.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
  }

  /**
   * Find activity log by ID
   */
  async findById(id: string): Promise<IActivityLog | null> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return null;
    }
    return await ActivityLogModel.findById(id).lean();
  }

  /**
   * Find one activity log by filter
   */
  async findOne(
    filter: Record<string, string | number | boolean | Date | object>
  ): Promise<IActivityLog | null> {
    return await ActivityLogModel.findOne(filter);
  }

  /**
   * Create a new activity log
   */
  async create(data: Partial<IActivityLog>): Promise<IActivityLog> {
    return await ActivityLogModel.create(data);
  }

  /**
   * Update an activity log
   */
  async update(
    id: string,
    data: Partial<IActivityLog>
  ): Promise<IActivityLog | null> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return null;
    }
    return await ActivityLogModel.findByIdAndUpdate(id, data, {
      new: true,
      runValidators: true
    });
  }

  /**
   * Delete an activity log
   */
  async delete(id: string): Promise<IActivityLog | null> {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return null;
    }
    return await ActivityLogModel.findByIdAndDelete(id);
  }

  /**
   * Find activity logs by user ID
   */
  async findByUser(
    userId: string,
    filter: Record<string, string | number | boolean | Date | object>,
    sort: Record<string, SortOrder>,
    skip: number,
    limit: number
  ): Promise<IActivityLog[]> {
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return [];
    }

    const userFilter = { ...filter, user: userId };

    return await ActivityLogModel.find(userFilter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('-__v')
      .lean();
  }

  /**
   * Find activity logs by entity ID
   */
  async findByEntity(
    entityId: string,
    entityType: string,
    filter: Record<string, string | number | boolean | Date | object>,
    sort: Record<string, SortOrder>,
    skip: number,
    limit: number
  ): Promise<IActivityLog[]> {
    if (!mongoose.Types.ObjectId.isValid(entityId)) {
      return [];
    }

    const entityFilter = {
      ...filter,
      entityId: entityId,
      entityType: entityType
    };

    return await ActivityLogModel.find(entityFilter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('user', 'name email')
      .select('-__v')
      .lean();
  }

  /**
   * Count activity logs by filter
   */
  async count(
    filter: Record<string, string | number | boolean | Date | object>
  ): Promise<number> {
    return await ActivityLogModel.countDocuments(filter);
  }

  async deleteMany(
    filter: Record<string, string | number | boolean | Date | object>
  ): Promise<number> {
    const result = await ActivityLogModel.deleteMany(filter);
    return result.deletedCount || 0;
  }

  /**
   * Find activity logs by action type
   */
  async findByAction(
    actionType: string,
    filter: Record<string, string | number | boolean | Date | object>,
    sort: Record<string, SortOrder>,
    skip: number,
    limit: number
  ): Promise<IActivityLog[]> {
    const actionFilter = { ...filter, actionType };

    return await ActivityLogModel.find(actionFilter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('user', 'name email')
      .select('-__v')
      .lean();
  }
}

export default new ActivityLogDao();
