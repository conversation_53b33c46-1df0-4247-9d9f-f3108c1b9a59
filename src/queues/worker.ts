import { Worker, Job } from 'bullmq';
import { redis } from '../utils/redis';
import { handlePostPurchaseJob } from '../services/private/job-handler.service';
import { IJobData } from '../interfaces/job-data.interface';
import axios from 'axios';
import { config } from '../config/config';
import logger from '../config/logger';
// import { cleanJobIfCompleted } from '../services/jobs/cleanupJob';

// Create a service for status updates
const updateEnrollmentStatus = async (
  enrollmentId: string,
  status: 'success' | 'failed',
  error?: string
) => {
  try {
    const response = await axios.post(
      `${config.prePurchaseUrl}/api/v3/private/enrollments/${enrollmentId}/status`,
      {
        status,
        error: error ? String(error).substring(0, 500) : undefined
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'server-api-key': config.serverApiKey
        },
        timeout: 5000 // 5 second timeout
      }
    );

    logger.info(
      `Status update for enrollment ${enrollmentId} sent: ${status}`,
      {
        response: response.status,
        enrollmentId
      }
    );
    return response;
  } catch (updateError) {
    logger.error(`Failed to update enrollment status for ${enrollmentId}`, {
      originalStatus: status,
      updateError,
      enrollmentId
    });
    throw updateError;
  }
};

const cleanupJob = async (job: Job): Promise<boolean> => {
  const jobId = job.id;

  try {
    await job.remove();
    logger.info('🧹 Job removed from Redis', { jobId });
    return true;
  } catch (error) {
    logger.error('❌ Failed to remove job from Redis', {
      jobId,
      error: error instanceof Error ? error.message : error
    });
    return false;
  }
};

// Define worker with retry strategy
const worker = new Worker<IJobData>(
  'enrollment-processing-queue',
  async (job: Job<IJobData>) => {
    const jobId = job.id;
    const { enrollmentId } = job.data;

    logger.info('📥 Processing enrollment job', {
      jobId,
      enrollmentId,
      attempt: job.attemptsMade + 1
    });

    try {
      // Process the enrollment
      const result = await handlePostPurchaseJob(job);

      if (!result) {
        throw new Error('Job processing returned false');
      }

      // Update status as success
      await updateEnrollmentStatus(enrollmentId, 'success');

      logger.info('✅ Enrollment processed successfully', {
        jobId,
        enrollmentId,
        attempt: job.attemptsMade + 1
      });

      return result;
    } catch (error) {
      logger.error('❌ Error processing enrollment', {
        jobId,
        enrollmentId,
        error,
        attempt: job.attemptsMade + 1,
        maxAttempts: job.opts.attempts
      });

      // Only update status as failed on final attempt
      if (job.attemptsMade + 1 >= (job.opts.attempts || 1)) {
        try {
          await updateEnrollmentStatus(enrollmentId, 'failed', String(error));
        } catch (statusUpdateError) {
          logger.error('Failed to send failure status', {
            statusUpdateError,
            originalError: error,
            enrollmentId
          });
        }
      }

      throw error;
    }
  },
  {
    connection: redis,
    concurrency: 5, // Process 5 jobs at a time
    limiter: {
      max: 100,
      duration: 60000 // 100 jobs per minute
    }
  }
);

// Worker events
worker.on('ready', () => {
  logger.info('✨ Worker is ready to process enrollment jobs');
});

worker.on('failed', (job: Job<IJobData, string> | undefined, error: Error) => {
  logger.error('❌ Job processing ultimately failed', {
    jobId: job?.id,
    enrollmentId: job?.data?.enrollmentId,
    error,
    attempts: job?.attemptsMade
  });
});

worker.on('completed', async (job: Job<IJobData, string> | undefined) => {
  logger.info('✅ Job completed successfully', {
    jobId: job?.id,
    enrollmentId: job?.data.enrollmentId
  });
  const jobId = job?.id as string;
  if (!job || !jobId) {
    return;
  }
  const clearedJob = await cleanupJob(job);
  if (clearedJob) {
    logger.info('🧹 Job removed from Redis', { jobId });
  } else {
    logger.error('❌ Failed to remove job from Redis', { jobId });
  }
});

worker.on('drained', () => {
  console.warn('🎉 Queue has been drained — all jobs are processed!');
});

worker.on('error', (error: Error) => {
  logger.error('❌ Worker error:', { error });
});

export default worker;
