import { IUser } from '../interfaces/user.interface';

declare global {
  namespace Express {
    interface Request {
      user?: IUser | null;
      pagination?: {
        page: number;
        limit: number;
        skip: number;
      };
      dbQuery?: {
        filter: Record<string>;
        sort: Record<string, 1 | -1>;
      };
      filter?: {
        [key: string]: string | number | boolean | Date | RegExp | object;
      };
      sort?: {
        [key: string]: 1 | -1;
      };
      select?: string;
      populate?: string | string[];
      fields?: string;
    }
  }
}
export {};
