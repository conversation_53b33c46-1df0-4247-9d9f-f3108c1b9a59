import express, { <PERSON>quest<PERSON><PERSON><PERSON> } from 'express';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import {
  pauseBatchValidator,
  resumeBatchValidator,
  getPauseHistoryValidator
} from '../../validators/public/batch-pause.validator';
import {
  pauseBatch,
  resumeBatch,
  getPauseHistory,
  getRemainingPauseDays
} from '../../controllers/public/batch-pause.controller';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(isAuthenticated as RequestHandler);

// Pause a batch
router.post(
  '/batches/:batchId/pause',
  pauseBatchValidator,
  pauseBatch as RequestHandler
);

// Resume a paused batch
router.post(
  '/batches/:batchId/pause/:pauseId/resume',
  resumeBatchValidator,
  resumeBatch as RequestHandler
);

// Get pause history for a batch
router.get(
  '/batches/:batchId/pause-history',
  getPauseHistoryValidator,
  getPauseHist<PERSON> as RequestHandler
);

// Get remaining pause days
router.get(
  '/batches/:batchId/remaining-pause-days',
  getPauseHistoryValidator, // Reusing the same validator
  getRemainingPauseDays as RequestHandler
);

export default router;
