import express, { <PERSON>quest<PERSON><PERSON><PERSON> } from 'express';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import {
  getAllAnnouncementController,
  getAnnouncementByIdController
} from '../../controllers/public/announcement.controller';

const router = express.Router();

router.get(
  '/batch/:batchId',
  isAuthenticated as RequestHandler,
  getAllAnnouncementController as RequestHandler
);

router.get(
  '/:id',
  isAuthenticated as RequestHandler,
  getAnnouncementByIdController as RequestHandler
);

export default router;
