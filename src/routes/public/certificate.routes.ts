import { Router, RequestHandler } from 'express';
import {
  getSocialShareLink,
  verifyCertificateController
} from '../../controllers/public/certificate.controller';
import { isAuthenticated } from '../../middlewares/auth.middleware';

const router = Router();

// Public certificate verification route
router.get(
  '/verify/:certificateId',
  verifyCertificateController as RequestHandler
);

router.get(
  '/share/:social/:certificateId',
  isAuthenticated as RequestHandler,
  getSocialShareLink as RequestHandler
);

export default router;
