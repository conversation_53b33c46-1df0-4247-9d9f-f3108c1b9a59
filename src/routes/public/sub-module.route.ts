import { Router, RequestHandler } from 'express';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import { validateSubmoduleProgressParams } from '../../validators/public/sub-module.validator';
import { getSubmoduleContentController } from '../../controllers/public/sub-module.controller';
import { isUserEnrolledForSubmodule } from '../../middlewares/userIsEnrolled.middleware';

const router = Router();

router.use(isAuthenticated as RequestHandler);

router.get(
  '/:submoduleId/content',
  validateSubmoduleProgressParams,
  isUserEnrolledForSubmodule as RequestHandler,
  getSubmoduleContentController as RequestHandler
);

export default router;
