import { Router, RequestHandler } from 'express';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import {
  validateCodingProblemIdParam,
  validateCodingProblemSolution,
  validateCodingProblemSubmissionBody,
  validateCodingProblemGetBody
} from '../../validators/public/coding-problem.validator';
import {
  getCodingProblemController,
  runCodeController,
  submitCodeController,
  getSolutionController
} from '../../controllers/public/coding-problem.controller';
import { trackContentView } from '../../middlewares/track-content-view.middleware';

const router = Router();

router.use(isAuthenticated as RequestHandler);

// Get coding problem details
router.post(
  '/:id',
  validateCodingProblemIdParam,
  validateCodingProblemGetBody,
  trackContentView as RequestHandler,
  getCodingProblemController as RequestHandler
);

// Run code (test without submitting)
router.post(
  '/:id/run',
  validateCodingProblemIdParam,
  validateCodingProblemSubmissionBody,
  runCodeController as RequestHandler
);

// Submit code solution
router.post(
  '/:id/submit',
  validateCodingProblemIdParam,
  validateCodingProblemSubmissionBody,
  submitCodeController as RequestHandler
);

// Get solution for a coding problem
router.get(
  '/:id/solution',
  validateCodingProblemIdParam,
  validateCodingProblemSolution,
  getSolutionController as RequestHandler
);

export default router;
