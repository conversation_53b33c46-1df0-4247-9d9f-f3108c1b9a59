import { Router } from 'express';
import userRouter from './user.route';
import bookmarksRouter from './bookmark.route';
import courseModulesRouter from './course-modules.route';
import subModuleRouter from './sub-module.route';
import certificateRouter from './certificate.routes';
import videoRouter from './video.route';
import announcementRouter from './announcement.route';
import noteRouter from './note.route';
import mcqRouter from './mcq.route';
import codingProblemRouter from './coding-problem.routes';
import batchPause from './batch-pause.route';
import leaderBoard from './leaderboard.route';
const router = Router();

router.get('/', (req, res) => {
  res.send('Public route');
});

router.use('/users', userRouter);

router.use('/bookmarks', bookmarksRouter);

router.use('/course-modules', courseModulesRouter);

router.use('/sub-modules', subModuleRouter);

router.use('/certificates', certificateRouter);

router.use('/announcements', announcementRouter);

router.use('/content/videos', videoRouter);

router.use('/content/notes', noteRouter);

router.use('/content/mcqs', mcqRouter);

router.use('/content/coding-problems', codingProblemRouter);

router.use('/batch-pause', batchPause);

router.use('/leaderboard', leaderBoard);

export default router;
