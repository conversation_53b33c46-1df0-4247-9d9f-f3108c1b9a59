import { Router, RequestHandler } from 'express';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import { getLeaderboardControllerByBatchId } from '../../controllers/public/leaderboard.controller';
import { validateGetLeaderboardByBatchId } from '../../validators/public/leaderboard.validator';

const router = Router();

router.use(isAuthenticated as RequestHandler);

router.get(
  '/:batchId',
  validateGetLeaderboardByBatchId,
  getLeaderboardControllerByBatchId as RequestHandler
);
// router.get(
//   '/',
//   validateGetLeaderboardByBatchId,
//   getAllLeaderboard as RequestHandler
// );

export default router;
