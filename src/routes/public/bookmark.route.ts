import { <PERSON>quest<PERSON><PERSON><PERSON>, Router } from 'express';
// import { body, param, query } from 'express-validator';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import {
  listBookmarks,
  toggleBookmarkController,
  getMyBookmarksController
} from '../../controllers/public/bookmark.controller';
import { bookmarkCreationValidator } from '../../validators/public/bookmark.validator';

const router = Router();

router.get(
  '/',
  isAuthenticated as RequestHandler,
  getMyBookmarksController as RequestHandler
);

router.get(
  '/batch/:batchId',
  isAuthenticated as RequestHandler,
  listBookmarks as <PERSON>questHandler
);

router.post(
  '/toggle',
  isAuthenticated as <PERSON>quest<PERSON><PERSON><PERSON>,
  bookmarkCreationValidator,
  toggleBookmarkController as RequestHandler
);

export default router;
