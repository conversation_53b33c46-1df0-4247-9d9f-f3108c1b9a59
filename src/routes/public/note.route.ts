import { Router, RequestHandler } from 'express';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import {
  validateNoteCompletedBody,
  validateNoteIdParam,
  validateNoteGetBody
} from '../../validators/public/note.validator';
import {
  getNote<PERSON>ontroller,
  markNoteCompletedController
} from '../../controllers/public/note.controller';
import { trackContentView } from '../../middlewares/track-content-view.middleware';

const router = Router();

router.use(isAuthenticated as RequestHandler);

// Get note details
router.post(
  '/:id',
  validateNoteIdParam,
  validateNoteGetBody,
  trackContentView as RequestHandler,
  getNoteController as RequestHandler
);

// Mark note as completed (implicit progress update)
router.post(
  '/:id/complete',
  validateNoteIdParam,
  validateNoteCompletedBody,
  markNoteCompletedController as <PERSON>questHandler
);

export default router;
