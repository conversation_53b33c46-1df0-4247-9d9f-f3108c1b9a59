// import { Router, RequestHand<PERSON> } from 'express';
// import { getCodingLabController, submitCodingLabSolutionController } from '../../../controllers/public/content/coding-lab.controller';
// import { isAuthenticated } from '../../../middlewares/auth.middleware';
// import { validateCodingLabIdParam, validateCodingLabSubmissionBody } from '../../../validators/public/content/coding-lab.validator';

// const router = Router();

// // Get coding lab details
// router.get(
//   '/:id',
//   isAuthenticated as RequestHandler,
//   validateCodingLabIdParam,
//   getCodingLabController as RequestHandler
// );

// // Submit coding lab solution
// router.post(
//   '/:id/submit',
//   isAuthenticated as RequestHandler,
//   validateCodingLabIdParam,
//   validateCodingLabSubmissionBody,
//   submitCodingLabSolutionController as RequestHandler
// );

// export default router;
