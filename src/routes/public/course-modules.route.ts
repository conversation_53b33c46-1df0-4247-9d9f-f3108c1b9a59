import { Router, RequestHandler } from 'express';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import { validateCourseParams } from '../../validators/public/course-modules.validator';
import { getCourseModulesController } from '../../controllers/public/course-modules.controller';

const router = Router();

router.use(isAuthenticated as RequestHandler);

router.get(
  '/:courseId',
  validateCourseParams,
  getCourseModulesController as RequestHandler
);

export default router;
