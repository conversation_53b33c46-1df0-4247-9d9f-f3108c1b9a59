import { Router, RequestHandler } from 'express';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import {
  validateMCQGetBody,
  validateMCQIdParam,
  validateMCQSubmissionBody
} from '../../validators/public/mcq.validator';
import {
  getMCQ<PERSON><PERSON>roller,
  submitMCQAnswerController
} from '../../controllers/public/mcq.controller';
import { trackContentView } from '../../middlewares/track-content-view.middleware';

const router = Router();

router.use(isAuthenticated as RequestHandler);

// Get MCQ details
router.post(
  '/:id',
  validateMCQIdParam,
  validateMCQGetBody,
  trackContentView as RequestHandler,
  getMCQController as RequestHandler
);

// Submit MCQ answer
router.post(
  '/:id/submit',
  validateMCQIdParam,
  validateMCQSubmissionBody,
  submitMCQAnswer<PERSON>ontroller as <PERSON>quest<PERSON>and<PERSON>
);

export default router;
