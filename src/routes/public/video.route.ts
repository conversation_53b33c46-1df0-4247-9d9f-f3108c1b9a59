import { Router, RequestHandler } from 'express';
import {
  validateVideoIdParam,
  validateVideoProgressBody,
  validateVideoGetBody
} from '../../validators/public/video.validator';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import {
  getVideoController,
  getVideoWithIframeSrcController,
  updateVideoProgressController
} from '../../controllers/public/video.controller';
import { trackContentView } from '../../middlewares/track-content-view.middleware';

const router = Router();

// Middleware to check authentication
router.use(isAuthenticated as RequestHandler);

// Get video details
router.get('/:id', validateVideoIdParam, getVideoController as RequestHandler);

// Update video progress
router.post(
  '/:id/progress',
  validateVideoIdParam,
  validateVideoProgressBody,
  updateVideoProgressController as RequestHandler
);

// Get video details with iframe source
router.post(
  '/:id/player',
  validateVideoIdParam,
  validateVideoGetBody,
  trackContentView as RequestHandler,
  getVideoWithIframeSrcController as RequestHandler
);

export default router;
