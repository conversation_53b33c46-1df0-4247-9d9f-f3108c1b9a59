import express, { Request<PERSON>and<PERSON> } from 'express';

import {
  createAnnouncementController,
  deleteAnnouncementController,
  updateAnnouncementController
} from '../../controllers/private/announcement.controller';
import {
  checkAnnouncementIdValidator,
  createAnnouncementValidator
  // updateAnnouncementValidator
} from '../../validators/private/announcement.validator';
import {
  isAuthenticated,
  requireRole
} from '../../middlewares/auth.middleware';
import { UserRole } from '../../interfaces/user.interface';

const router = express.Router();

// Create new announcement
router.post(
  '/',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  createAnnouncementValidator,
  createAnnouncementController as RequestHandler
);

router.patch(
  '/:id',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  // updateAnnouncementValidator,
  updateAnnouncement<PERSON>ontroller as RequestHandler
);

router.delete(
  '/:id',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  checkAnnouncementIdValidator,
  deleteAnnouncementController as RequestHandler
);

export default router;
