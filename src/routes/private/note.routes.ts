import express, { <PERSON>quest<PERSON><PERSON><PERSON> } from 'express';
import {
  createNote<PERSON><PERSON>roller,
  deleteNoteController,
  updateNoteController
} from '../../controllers/private/note.controller';
import {
  createNoteValidator,
  noteParamIdValidator
} from '../../validators/private/note.validator';
import {
  isAuthenticated,
  requireRole
} from '../../middlewares/auth.middleware';
import { UserRole } from '../../interfaces/user.interface';

const router = express.Router();

// Create new Note
router.post(
  '/',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  createNoteValidator,
  createNoteController as <PERSON>questHandler
);

router.patch(
  '/:id',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  noteParamIdValidator,
  updateNoteController as RequestHandler
);

router.delete(
  '/:id',
  isAuthenticated as Request<PERSON>and<PERSON>,
  requireRole(UserRole.ADMIN) as Request<PERSON><PERSON><PERSON>,
  noteParamIdValidator,
  deleteNoteController as RequestHandler
);

export default router;
