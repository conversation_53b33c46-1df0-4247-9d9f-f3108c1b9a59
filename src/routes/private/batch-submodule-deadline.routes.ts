import express, { RequestHand<PERSON> } from 'express';
import {
  createBatchSubmoduleDeadlineController,
  getBatchSubmoduleDeadlineController,
  initializeAllDeadlineController,
  updateBatchSubmoduleDeadlineController
} from '../../controllers/private/batch-submodule-deadline.controller';
import {
  createBatchSubmoduleDeadlineValidator,
  initializeAllDeadlineValidator
} from '../../validators/private/batch-submodule-deadline.validator';
import {
  isAuthenticated,
  requireRole
} from '../../middlewares/auth.middleware';
import { UserRole } from '../../interfaces/user.interface';
import { batchSubModuleDeadlineQueryParser } from '../../middlewares/query.middleware';

const router = express.Router();

router.post(
  '/',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  createBatchSubmoduleDeadlineValidator,
  createBatchSubmoduleDeadlineController as RequestHandler
);

router.post(
  '/allSubmoduleDeadline',
  isAuthenticated as RequestHand<PERSON>,
  requireRole(UserRole.ADMIN) as RequestHandler,
  initializeAllDeadlineValidator,
  initializeAllDeadlineController as RequestHandler
);

router.get(
  '/:batchId/deadline',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  batchSubModuleDeadlineQueryParser,
  getBatchSubmoduleDeadlineController as RequestHandler
);

router.put(
  '/:batchId/deadline',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  updateBatchSubmoduleDeadlineController as RequestHandler
);

export default router;
