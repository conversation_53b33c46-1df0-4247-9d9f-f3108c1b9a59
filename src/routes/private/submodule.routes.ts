import express, { Request<PERSON>and<PERSON> } from 'express';
import {
  createSubmoduleController,
  reorderSubmoduleController,
  updateSubmoduleController
  // getSubmoduleByIdController,
} from '../../controllers/private/submodule.controller';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import {
  reorderSubmoduleValidator,
  updateSubmoduleValidator,
  validateSubmoduleIdParam
} from '../../validators/private/submodule.validator';

const router = express.Router();

router.post(
  '/create',
  isAuthenticated as RequestHandler,
  createSubmoduleController as <PERSON>questHandler
);

// router.get('/:id', getSubmoduleByIdController as RequestHandler);

router.patch(
  '/:id',
  isAuthenticated as RequestHandler,
  validateSubmoduleIdParam,
  updateSubmoduleValidator,
  updateSubmoduleController as RequestHandler
);

router.put(
  '/:id/reorder',
  isAuthenticated as Request<PERSON>and<PERSON>,
  reorderSubmoduleValidator,
  reorderSubmoduleController as RequestHandler
);

export default router;
