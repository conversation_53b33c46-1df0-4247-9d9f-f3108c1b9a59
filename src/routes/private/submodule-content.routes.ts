import express, { Request<PERSON><PERSON><PERSON> } from 'express';
import {
  createSubmodule<PERSON>ontent<PERSON>ontroller,
  getAllSubmoduleContentBySubmoduleIdController,
  removeContentFromSubmoduleController,
  reorderSubmoduleContentController,
  updateSubmoduleContentController
} from '../../controllers/private/submodule-content.controller';
import {
  createSubmoduleContentValidator,
  updateSubmoduleContentValidator,
  validateSubmoduleContentIdParam,
  validateSubmoduleContentIdParamForRemove
} from '../../validators/private/submodule-content.validator';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import { isUserEnrolled } from '../../middlewares/userIsEnrolled.middleware';

const router = express.Router();

router.post(
  '/',
  isAuthenticated as RequestHandler,
  createSubmoduleContentValidator,
  createS<PERSON><PERSON>duleContentController as Request<PERSON><PERSON><PERSON>
);

// Temp route for getting all submodule content by submodule id
router.get(
  '/:courseId/:batchId/:id',
  isAuthenticated as RequestHandler,
  isUserEnrolled as RequestHandler,
  getAllSubmoduleContentBySubmoduleIdController as RequestHandler
);

router.patch(
  '/:id',
  isAuthenticated as RequestHandler,
  validateSubmoduleContentIdParam,
  updateSubmoduleContentValidator,
  updateSubmoduleContentController as RequestHandler
);

router.put(
  '/:submoduleId/content/:contentId',
  isAuthenticated as RequestHandler,
  validateSubmoduleContentIdParamForRemove,
  removeContentFromSubmoduleController as RequestHandler
);

router.put(
  '/:id/reorder',
  isAuthenticated as RequestHandler,
  updateSubmoduleContentValidator,
  reorderSubmoduleContentController as RequestHandler
);

export default router;
