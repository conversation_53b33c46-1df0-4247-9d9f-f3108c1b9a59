import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import {
  createCodingProblemController,
  getCodingProblemByIdController,
  getCodingProblemByProblemIdController,
  getCodingProblemsByLanguageController,
  updateCodingProblemController,
  deleteCodingProblemController
} from '../../controllers/private/coding-problem.controller';
import {
  createCodingProblemValidator,
  updateCodingProblemValidator
} from '../../validators/private/coding-problem.validator';
import {
  isAuthenticated,
  requireRole
} from '../../middlewares/auth.middleware';
import { UserRole } from '../../interfaces/user.interface';

const router = express.Router();

// Create new coding problem
router.post(
  '/',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  createCodingProblemValidator,
  createCodingProblemController as RequestHandler
);

// Get coding problem by MongoDB ID
router.get('/:id', getCodingProblemByIdController as RequestHandler);

// Get coding problem by problemId
router.get(
  '/by-problem-id/:problemId',
  getCodingProblemByProblemIdController as RequestHandler
);

// Get coding problems by language
router.get(
  '/by-language/:language',
  getCodingProblemsByLanguageController as RequestHandler
);

router.patch(
  '/:id',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  updateCodingProblemValidator,
  updateCodingProblemController as RequestHandler
);

router.delete(
  '/:id',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  deleteCodingProblemController as RequestHandler
);

export default router;
