import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import {
  createVideoController,
  deleteVideoController,
  updateVideoController
} from '../../controllers/private/video.controller';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import {
  createVideoValidator,
  updateVideoValidator
} from '../../validators/private/video.validator';
import { validateVideoIdParam } from '../../validators/public/video.validator';

const router = express.Router();

router.post(
  '/',
  isAuthenticated as RequestHandler,
  createVideoValidator,
  createVideoController as RequestHandler
);

router.patch(
  '/:id',
  isAuthenticated as <PERSON>quest<PERSON><PERSON><PERSON>,
  updateVideoValidator,
  updateVideoController as RequestHandler
);

router.delete(
  '/:id',
  isAuthenticated as <PERSON>quest<PERSON>and<PERSON>,
  validateVideoIdParam,
  deleteVideoController as <PERSON>questHand<PERSON>
);

export default router;
