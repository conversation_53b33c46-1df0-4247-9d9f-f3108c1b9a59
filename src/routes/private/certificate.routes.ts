import { Router, RequestHandler } from 'express';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import { checkCertificateEligibilityController } from '../../controllers/private/certificate.controller';
import {
  // certificateCreationValidator,
  courseIdParamsValidator
} from '../../validators/private/certificate.validator';

const router = Router();

// Create certificate route
// router.post(
//   '/generate',
//   isAuthenticated as RequestHandler,
//   certificateCreationValidator,
//   createCertificateController as RequestHandler
// );

router.get(
  '/eligibility/:courseId',
  isAuthenticated as RequestHandler,
  courseIdParamsValidator,
  checkCertificateEligibilityController as RequestHandler
);

export default router;
