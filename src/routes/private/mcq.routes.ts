import express, { <PERSON>quest<PERSON>and<PERSON> } from 'express';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>er,
  getMCQ<PERSON>yId<PERSON>ontroller,
  deleteMCQ<PERSON>ontroller,
  updateMCQController
} from '../../controllers/private/mcq.controller';
import {
  createMcqValidator,
  getMcqByIdValidator,
  updateMcqValidator
} from '../../validators/private/mcq.validator';
import {
  isAuthenticated,
  requireRole
} from '../../middlewares/auth.middleware';
import { UserRole } from '../../interfaces/user.interface';

const router = express.Router();

// Create new MCQ
router.post(
  '/',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  createMcqValidator,
  createMCQController as RequestHandler
);

router.patch(
  '/:id',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  updateMcqV<PERSON>da<PERSON>,
  updateMCQController as RequestHand<PERSON>
);

// Get MCQ by ID
router.get('/:id', getMCQByIdController as RequestHandler);

router.delete(
  '/:id',
  isAuthenticated as RequestHandler,
  requireRole(UserRole.ADMIN) as RequestHandler,
  getMcqByIdValidator,
  deleteMCQController as RequestHandler
);

export default router;
