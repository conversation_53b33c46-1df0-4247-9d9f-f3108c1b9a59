import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import {
  createModuleController,
  reorderModuleController,
  updateModuleController
} from '../../controllers/private/module.controller';
import { isAuthenticated } from '../../middlewares/auth.middleware';
import {
  createModuleValidator,
  reorderModuleValidator,
  updateModuleValidator
} from '../../validators/private/module.validator';
// import { requireRole } from '../../middlewares/auth.middleware';
// import { UserRole } from '../../interfaces/user.interface';
const router = express.Router();

router.post(
  '/create',
  isAuthenticated as <PERSON>quest<PERSON>and<PERSON>,
  createModuleValidator,
  createModuleController as RequestHandler
);

router.put(
  '/:id/reorder',
  isAuthenticated as RequestHandler,
  reorderModuleValidator,
  reorderModuleController as RequestHandler
);

router.patch(
  '/:id',
  isAuthenticated as <PERSON>quest<PERSON><PERSON><PERSON>,
  updateModuleValidator,
  updateModule<PERSON>ontroller as <PERSON>quest<PERSON><PERSON><PERSON>
);

export default router;
