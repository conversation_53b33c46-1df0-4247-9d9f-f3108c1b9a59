import { Router } from 'express';
import errorLogsRouter from './error-log.routes';
import moduleRouters from './module.routes';
import batchSubmoduleDeadlineRouters from './batch-submodule-deadline.routes';
import submoduleContentRouters from './submodule-content.routes';
import submoduleRouters from './submodule.routes';
import mcqRouters from './mcq.routes';
import notesRouters from './note.routes';
import doubtRouters from './doubt.routes';
import codingProblemRouters from './coding-problem.routes';
import announcementRouters from './announcement.routes';
import videoRouters from './video.routes';
import certificateRouters from './certificate.routes';

const router = Router();

router.use('/error-logs', errorLogsRouter);

router.use('/modules', moduleRouters);

router.use('/certificate', certificateRouters);

router.use('/batch-submodule-deadlines', batchSubmoduleDeadlineRouters);

router.use('/submodule', submoduleRouters);

router.use('/submodule-contents', submoduleContentRouters);

router.use('/mcq', mcqRouters);

router.use('/note', notesRouters);

router.use('/doubt', doubtRouters);

router.use('/coding-problem', codingProblemRouters);

router.use('/announcement', announcementRouters);

router.use('/video', videoRouters);

export default router;
