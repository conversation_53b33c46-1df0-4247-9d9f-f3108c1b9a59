import { body } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const bookmarkCreationValidator = validate<PERSON>hain([
  body('batch')
    .notEmpty()
    .withMessage('Batch ID is required')
    .isMongoId()
    .withMessage('Batch ID must be a valid MongoDB ID'),
  body('submodule')
    .notEmpty()
    .withMessage('Submodule ID is required')
    .isMongoId()
    .withMessage('Submodule ID must be a valid MongoDB ID'),
  body('contentType')
    .notEmpty()
    .withMessage('Content type is required')
    .isIn(['video', 'note', 'mcq', 'coding_problem', 'coding_lab'])
    .withMessage(
      'Content type must be one of: video, note, mcq, coding_problem, coding_lab'
    ),
  body('contentId')
    .notEmpty()
    .withMessage('Content ID is required')
    .isMongoId()
    .withMessage('Content ID must be a valid MongoDB ID'),
  body('contentModel')
    .optional()
    .isIn(['video', 'note', 'mcq', 'coding_problem', 'coding_lab'])
    .withMessage(
      'Content model must be one of: video, note, mcq, coding_problem, coding_lab'
    ),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean value')
    .isIn([true, false])
    .withMessage('isActive must be either true or false')
]);
