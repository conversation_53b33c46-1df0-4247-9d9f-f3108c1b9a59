import { query, param } from 'express-validator';

export const validateGetLeaderboardByBatchId = [
  param('batchId')
    .notEmpty()
    .withMessage('batchId is required')
    .isMongoId()
    .withMessage('batchId must be a valid MongoDB ObjectId'),
  query('period')
    .optional()
    .isIn(['daily', 'weekly', 'monthly', 'overall'])
    .withMessage('period must be one of daily, weekly, monthly, overall'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('limit must be a positive integer between 1 and 100')
];
