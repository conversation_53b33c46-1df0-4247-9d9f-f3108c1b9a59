import { body, param } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const validateMCQIdParam = validateChain([
  param('id').isMongoId().withMessage('Invalid MCQ ID format')
]);

export const validateMCQSubmissionBody = validateChain([
  body('selectedOptions')
    .isArray()
    .withMessage('Selected options must be an array')
    .notEmpty()
    .withMessage('At least one option must be selected'),
  body('subModuleId')
    .isMongoId()
    .withMessage('subModuleId must be a valid MongoDB ID')
    .notEmpty()
    .withMessage('Submodule ID is required')
]);

export const validateMCQGetBody = validateChain([
  body('subModuleId')
    .isMongoId()
    .withMessage('subModuleId must be a valid MongoDB ID')
    .notEmpty()
    .withMessage('Submodule ID is required')
]);
