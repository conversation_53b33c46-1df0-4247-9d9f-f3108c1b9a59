import { param, query } from 'express-validator';
import { validate<PERSON>hai<PERSON> } from '../../utils/helper';

export const validateSubmoduleProgressParams = validateChain([
  param('submoduleId')
    .notEmpty()
    .withMessage('Submodule ID is required')
    .isMongoId()
    .withMessage('Invalid Submodule ID format'),

  query('batchId').optional().isMongoId().withMessage('Invalid Batch ID format')
]);
