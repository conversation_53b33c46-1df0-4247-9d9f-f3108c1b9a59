import { param, body, query } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const validateCodingProblemIdParam = validateChain([
  param('id').isMongoId().withMessage('Invalid coding problem ID format')
]);

// Requireed language in query
export const validateCodingProblemSolution = validateChain([
  query('language')
    .isString()
    .withMessage('Language must be a string')
    .notEmpty()
    .withMessage('Language cannot be empty')
]);

export const validateCodingProblemSubmissionBody = validateChain([
  body('code')
    .isString()
    .withMessage('Code must be a string')
    .notEmpty()
    .withMessage('Code cannot be empty'),
  body('language')
    .isString()
    .withMessage('Language must be a string')
    .notEmpty()
    .withMessage('Language cannot be empty'),
  body('compilerId')
    .optional()
    .isString()
    .withMessage('Compiler ID must be a string')
    .notEmpty()
    .withMessage('Compiler ID cannot be empty'),
  body('customTestCases')
    .optional()
    .isArray()
    .withMessage('Custom test cases must be an array'),
  body('customTestCases.*.input')
    .optional()
    .isString()
    .withMessage('Test case input must be a string'),
  body('customTestCases.*.expectedOutput')
    .optional()
    .isString()
    .withMessage('Test case expected output must be a string')
]);

export const validateCodingProblemGetBody = validateChain([
  body('subModuleId')
    .isMongoId()
    .withMessage('subModuleId must be a valid MongoDB ID')
    .notEmpty()
    .withMessage('Submodule ID is required')
]);
