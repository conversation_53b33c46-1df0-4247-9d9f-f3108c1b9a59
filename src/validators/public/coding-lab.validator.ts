import { param, body } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const validateCodingLabIdParam = validateChain([
  param('id').isMongoId().withMessage('Invalid coding lab ID format')
]);

export const validateCodingLabSubmissionBody = validateChain([
  body('code')
    .isString()
    .withMessage('Code must be a string')
    .notEmpty()
    .withMessage('Code cannot be empty'),
  body('language')
    .isString()
    .withMessage('Language must be a string')
    .notEmpty()
    .withMessage('Language cannot be empty')
]);
