import { body, param } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const pauseBatchValidator = validateChain([
  param('batchId').isMongoId().withMessage('Invalid batch ID format'),
  body('startDate').isISO8601().withMessage('Start date must be a valid date'),
  body('endDate').isISO8601().withMessage('End date must be a valid date'),
  body('reason')
    .isString()
    .notEmpty()
    .withMessage('Reason for pause is required')
    .isLength({ min: 10, max: 500 })
    .withMessage('Reason must be between 10 and 500 characters')
]);

export const resumeBatchValidator = validateChain([
  param('batchId').isMongoId().withMessage('Invalid batch ID format'),
  param('pauseId').isMongoId().withMessage('Invalid pause ID format')
]);

export const getPauseHistoryValidator = validateChain([
  param('batchId').isMongoId().withMessage('Invalid batch ID format')
]);
