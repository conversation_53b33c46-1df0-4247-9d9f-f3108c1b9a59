import { param, body } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const validateVideoIdParam = validateChain([
  param('id').isMongoId().withMessage('Invalid video ID format')
]);

export const validateVideoProgressBody = validateChain([
  body('watchedDuration')
    .isNumeric()
    .withMessage('Watched duration must be a number')
    .toInt(),
  body('completed')
    .optional()
    .isBoolean()
    .withMessage('Completed field must be a boolean')
    .toBoolean(),
  body('subModuleId')
    .isMongoId()
    .withMessage('subModuleId must be a valid MongoDB ID')
]);

export const validateVideoGetBody = validateChain([
  body('subModuleId')
    .isMongoId()
    .withMessage('subModuleId must be a valid MongoDB ID')
    .notEmpty()
    .withMessage('Submodule ID is required')
]);
