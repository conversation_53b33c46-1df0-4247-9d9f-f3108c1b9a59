import { body, param } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const validateNoteIdParam = validateChain([
  param('id').isMongoId().withMessage('Invalid note ID format')
]);

export const validateNoteCompletedBody = validateChain([
  body('subModuleId')
    .isMongoId()
    .withMessage('subModuleId must be a valid MongoDB ID')
    .notEmpty()
    .withMessage('subModuleId is required')
]);

export const validateNoteGetBody = validateChain([
  body('subModuleId')
    .isMongoId()
    .withMessage('subModuleId must be a valid MongoDB ID')
    .notEmpty()
    .withMessage('Submodule ID is required')
]);
