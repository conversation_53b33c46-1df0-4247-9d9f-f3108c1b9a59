import { body, param } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';
import { isValidObjectId } from 'mongoose';

export const validateModuleIdParam = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Module ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid ID format')
]);

export const createModuleValidator = validateChain([
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isString()
    .withMessage('Title must be a string'),
  body('description')
    .optional()
    .isString()
    .withMessage('Description must be a string'),
  body('course')
    .notEmpty()
    .withMessage('Course ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid Course ID format'),
  body('order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer')
]);

export const updateModuleValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Module ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid ID format'),
  body('title').optional().isString().withMessage('Title must be a string'),
  body('description')
    .optional()
    .isString()
    .withMessage('Description must be a string'),
  body('course')
    .optional()
    .custom(isValidObjectId)
    .withMessage('Invalid Course ID format')
]);

export const reorderModuleValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Module ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid ID format'),
  body('order')
    .notEmpty()
    .withMessage('Order is required')
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer')
]);
