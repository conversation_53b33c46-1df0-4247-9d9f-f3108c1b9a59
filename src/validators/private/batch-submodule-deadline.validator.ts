import { body } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const createBatchSubmoduleDeadlineValidator = validateChain([
  body('batch')
    .isMongoId()
    .withMessage('Batch ID must be a valid MongoDB ObjectId'),
  body('submodule')
    .isMongoId()
    .withMessage('Submodule ID must be a valid MongoDB ObjectId'),
  body('startDate')
    .notEmpty()
    .withMessage('Start date is required')
    .isISO8601()
    .withMessage('Invalid start date'),
  body('days')
    .notEmpty()
    .withMessage('Days is required')
    .isInt({ min: 1 })
    .withMessage('Days must be a positive integer'),
  body('dependsOnPreviousSubmodule')
    .notEmpty()
    .withMessage('Depends On Previous Submodule is required'),
  body('isActive').notEmpty().withMessage('isActive is required')
]);

export const initializeAllDeadlineValidator = validateChain([
  body('batch')
    .isMongoId()
    .withMessage('Batch ID must be a valid MongoDB ObjectId'),
  body('submodule')
    .isMongoId()
    .withMessage('Submodule ID must be a valid MongoDB ObjectId'),
  body('startDate')
    .notEmpty()
    .withMessage('Start date is required')
    .isISO8601()
    .withMessage('Invalid start date'),
  body('penaltyRules')
    .optional()
    .isArray()
    .withMessage('Penalty rules must be an array'),
  body('days')
    .notEmpty()
    .withMessage('Days is required')
    .isInt({ min: 1 })
    .withMessage('Days must be a positive integer'),
  body('dependsOnPreviousSubmodule')
    .notEmpty()
    .withMessage('Depends On Previous Submodule is required'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
]);
