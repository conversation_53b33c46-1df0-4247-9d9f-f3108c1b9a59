import { body, param } from 'express-validator';
import { validate<PERSON><PERSON><PERSON> } from '../../utils/helper';
import { isValidObjectId } from 'mongoose';

export const createMcqValidator = validateChain([
  body('title')
    .notEmpty()
    .withMessage('MCQ title is required')
    .trim()
    .isLength({ min: 3 })
    .withMessage('Title must be at least 3 characters'),

  body('question').notEmpty().withMessage('Question is required'),

  body('image')
    .optional()
    .isBase64()
    .withMessage('Image must be a valid base64 string'),

  body('codeSnippets')
    .notEmpty()
    .withMessage('Code snippets are required')
    .isArray()
    .withMessage('Code snippets must be an array'),

  body('codeSnippets.*.language')
    .notEmpty()
    .withMessage('Code snippet language is required'),

  body('codeSnippets.*.code')
    .notEmpty()
    .withMessage('Code snippet content is required'),

  body('options')
    .isArray({ min: 4, max: 6 })
    .withMessage('Each MCQ must have between 4 and 6 options')
    .custom((options: { text: string; isCorrect: boolean }[]) => {
      const texts = options.map(option => option.text);
      const uniqueTexts = new Set(texts);
      if (texts.length !== uniqueTexts.size) {
        throw new Error('Option texts must be unique');
      }
      return true;
    }),

  body('options.*.text').notEmpty().withMessage('Option text is required'),

  body('options.*.isCorrect')
    .isBoolean()
    .withMessage('isCorrect must be a boolean'),

  body('selectionType')
    .notEmpty()
    .withMessage('Selection type is required')
    .isIn(['single', 'multiple'])
    .withMessage('Selection type must be either single or multiple')
    .custom((selectionType: string, { req }) => {
      const body = req.body as { options: { isCorrect: boolean }[] };
      const options = body.options || [];
      const correctOptions = options.filter(option => option.isCorrect);
      if (selectionType === 'single' && correctOptions.length !== 1) {
        throw new Error(
          'For single selection, exactly one option must be correct'
        );
      }
      if (selectionType === 'multiple' && correctOptions.length < 1) {
        throw new Error(
          'For multiple selection, at least one option must be correct'
        );
      }
      return true;
    }),

  body('points')
    .notEmpty()
    .withMessage('Points are required')
    .isInt({ min: 0 })
    .withMessage('Points must be a non-negative integer'),

  body('explanation')
    .optional()
    .custom(
      (explanation: { text?: string; video?: string; image?: string }) => {
        if (!explanation.text && !explanation.video && !explanation.image) {
          throw new Error(
            'Explanation must include at least one of the following: text, video, or image'
          );
        }
        return true;
      }
    ),

  body('difficulty')
    .notEmpty()
    .withMessage('Difficulty level is required')
    .isIn(['easy', 'medium', 'hard'])
    .withMessage('Difficulty must be one of: easy, medium, hard'),

  body('tags').optional().isArray().withMessage('Tags must be an array'),

  body('tags.*').optional().isString().withMessage('Each tag must be a string'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
]);

export const updateMcqValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('MCQ ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid MCQ ID format'),

  // Same validations as create but all fields optional
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3 })
    .withMessage('Title must be at least 3 characters'),

  body('question').optional(),

  body('image').optional().isURL().withMessage('Image must be a valid URL'),

  body('options')
    .optional()
    .isArray({ min: 2 })
    .withMessage('At least 2 options are required'),

  body('selectionType')
    .optional()
    .isIn(['single', 'multiple'])
    .withMessage('Selection type must be either single or multiple'),

  body('points')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Points must be a non-negative integer'),

  body('difficulty')
    .optional()
    .isIn(['easy', 'medium', 'hard'])
    .withMessage('Difficulty must be one of: easy, medium, hard')
]);

export const getMcqByIdValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('MCQ ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid MCQ ID format')
]);
