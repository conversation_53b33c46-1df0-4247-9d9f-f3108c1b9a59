import { body, param } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const certificateCreationValidator = validate<PERSON>hain([
  body('course')
    .notEmpty()
    .withMessage('Course ID is required')
    .isMongoId()
    .withMessage('Course ID must be a valid MongoDB ID'),
  body('batch')
    .notEmpty()
    .withMessage('Batch ID is required')
    .isMongoId()
    .withMessage('Batch ID must be a valid MongoDB ID')
]);

export const courseIdParamsValidator = validateChain([
  param('courseId')
    .notEmpty()
    .withMessage('Course ID is required')
    .isMongoId()
    .withMessage('Course ID must be a valid MongoDB ID')
]);
