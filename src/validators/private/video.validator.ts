import { body, param } from 'express-validator';
import { validate<PERSON><PERSON><PERSON> } from '../../utils/helper';
import { isValidObjectId } from 'mongoose';

export const createVideoValidator = validateChain([
  body('title')
    .notEmpty()
    .withMessage('Video title is required')
    .trim()
    .isLength({ min: 3 })
    .withMessage('Title must be at least 3 characters'),

  body('description')
    .notEmpty()
    .withMessage('Video description is required')
    .isLength({ min: 10 })
    .withMessage('Description must be at least 10 characters'),

  body('videoId')
    .notEmpty()
    .withMessage('Video ID is required')
    .isString()
    .withMessage('Video ID must be a string'),

  body('provider')
    .optional()
    .isString()
    .withMessage('Provider must be a string'),

  body('duration')
    .optional()
    .isNumeric()
    .withMessage('Duration must be a number')
    .isFloat({ min: 0 })
    .withMessage('Duration cannot be negative'),

  body('points')
    .optional()
    .isNumeric()
    .withMessage('Points must be a number')
    .isInt({ min: 0 })
    .withMessage('Points must be a non-negative integer'),

  body('tags').optional().isArray().withMessage('Tags must be an array'),

  body('tags.*').optional().isString().withMessage('Each tag must be a string'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
]);

export const updateVideoValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Video ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid video ID format'),

  // Same validations as create but all fields optional
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3 })
    .withMessage('Title must be at least 3 characters'),

  body('description')
    .optional()
    .isLength({ min: 10 })
    .withMessage('Description must be at least 10 characters'),

  body('videoId')
    .optional()
    .isString()
    .withMessage('Video ID must be a string'),

  body('provider')
    .optional()
    .isString()
    .withMessage('Provider must be a string'),

  body('duration')
    .optional()
    .isNumeric()
    .withMessage('Duration must be a number')
    .isFloat({ min: 0 })
    .withMessage('Duration cannot be negative'),

  body('points')
    .optional()
    .isNumeric()
    .withMessage('Points must be a number')
    .isInt({ min: 0 })
    .withMessage('Points must be a non-negative integer'),

  body('tags').optional().isArray().withMessage('Tags must be an array'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
]);

export const getVideoByIdValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Video ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid video ID format')
]);
