import { body, param } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';
import { isValidObjectId } from 'mongoose';

export const validateSubmoduleIdParam = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Submodule ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid ID format')
]);

export const createSubmoduleValidator = validateChain([
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isString()
    .withMessage('Title must be a string'),
  body('description')
    .optional()
    .isString()
    .withMessage('Description must be a string'),
  body('module')
    .notEmpty()
    .withMessage('Module ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid Module ID format'),
  body('order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer')
]);

export const updateSubmoduleValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Submodule ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid ID format'),
  body('title').optional().isString().withMessage('Title must be a string'),
  body('description')
    .optional()
    .isString()
    .withMessage('Description must be a string'),
  body('module')
    .optional()
    .custom(isValidObjectId)
    .withMessage('Invalid Module ID format')
]);

export const reorderSubmoduleValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Submodule ID is required')
    .custom(isValidObjectId)
    .withMessage('Invalid ID format'),
  body('order')
    .notEmpty()
    .withMessage('Order is required')
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer')
]);
