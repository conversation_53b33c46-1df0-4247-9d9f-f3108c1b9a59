import { body } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const createDoubtValidator = validate<PERSON>hain([
  body('user')
    .notEmpty()
    .withMessage('User ID is required')
    .isMongoId()
    .withMessage('Invalid user id'),
  body('userName')
    .notEmpty()
    .withMessage('User name is required')
    .isString()
    .withMessage('User name must be a string'),
  body('userAvatar')
    .optional()
    .isString()
    .withMessage('User avatar must be a string'),
  body('course')
    .notEmpty()
    .withMessage('Course ID is required')
    .isMongoId()
    .withMessage('Invalid course id'),
  body('module').optional().isMongoId().withMessage('Invalid module id'),
  body('subModule').optional().isMongoId().withMessage('Invalid sub-module id'),
  body('contentType')
    .optional()
    .isString()
    .withMessage('Content type must be a string')
    .isIn(['video', 'note', 'mcq', 'coding_problem', 'coding_lab'])
    .withMessage('Invalid content type'),
  body('contentId').optional().isMongoId().withMessage('Invalid content id'),
  body('contentModel')
    .optional()
    .isString()
    .withMessage('Content model must be a string')
    .isIn(['Video', 'Note', 'MCQ', 'CodingProblem', 'CodingLab'])
    .withMessage('Invalid content model'),
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isString()
    .withMessage('Title must be a string')
    .isLength({ min: 4 })
    .withMessage('Title must be at least 4 characters long'),
  body('description')
    .notEmpty()
    .withMessage('Description is required')
    .isString()
    .withMessage('Description must be a string')
    .isLength({ min: 10 })
    .withMessage('Description must be at least 10 characters long'),
  body('status')
    .optional()
    .isString()
    .withMessage('Status must be a string')
    .isIn(['open', 'in_progress', 'resolved', 'closed'])
    .withMessage('Invalid status'),
  body('priority')
    .optional()
    .isString()
    .withMessage('Priority must be a string')
    .isIn(['low', 'medium', 'high'])
    .withMessage('Invalid priority'),
  body('codeSnippet')
    .optional()
    .isString()
    .withMessage('Code snippet must be a string')
]);
