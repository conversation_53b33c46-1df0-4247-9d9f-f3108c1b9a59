import { body, param } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const createSubmoduleContentValidator = validateChain([
  body('submodule')
    .notEmpty()
    .withMessage('Submodule is required')
    .isMongoId()
    .withMessage('Submodule must be a valid MongoDB ObjectId'),
  body('contentType')
    .notEmpty()
    .withMessage('Content type is required')
    .isIn(['video', 'note', 'mcq', 'coding_problem', 'coding_lab'])
    .withMessage(
      'Content type must be one of: video, note, mcq, coding_problem, coding_lab'
    ),
  body('contentId')
    .notEmpty()
    .withMessage('Content ID is required')
    .isMongoId()
    .withMessage('Content ID must be a valid MongoDB ObjectId'),
  body('contentModel')
    .notEmpty()
    .withMessage('Content model is required')
    .isIn(['Video', 'Note', 'MCQ', 'CodingProblem', 'CodingLab'])
    .withMessage(
      'Content model must be one of: Video, Note, MCQ, CodingProblem, CodingLab'
    ),
  body('section')
    .notEmpty()
    .withMessage('Section is required')
    .isIn(['lesson', 'practice'])
    .withMessage('Section must be one of: lesson, practice'),
  body('order')
    .notEmpty()
    .withMessage('Order is required')
    .isNumeric()
    .withMessage('Order must be a number')
    .isInt({ min: 0 })
    .withMessage('Order must be a positive integer'),
  body('isActive')
    .notEmpty()
    .withMessage('isActive is required')
    .isBoolean()
    .withMessage('isActive must be a boolean')
]);

export const updateSubmoduleContentValidator = validateChain([
  body('submodule')
    .optional()
    .isMongoId()
    .withMessage('Submodule must be a valid MongoDB ObjectId'),
  body('contentType')
    .optional()
    .isIn(['video', 'note', 'mcq', 'coding_problem', 'coding_lab'])
    .withMessage(
      'Content type must be one of: video, note, mcq, coding_problem, coding_lab'
    ),
  body('contentId')
    .optional()
    .isMongoId()
    .withMessage('Content ID must be a valid MongoDB ObjectId'),
  body('contentModel')
    .optional()
    .isIn(['Video', 'Note', 'MCQ', 'CodingProblem', 'CodingLab'])
    .withMessage(
      'Content model must be one of: Video, Note, MCQ, CodingProblem, CodingLab'
    ),
  body('section')
    .optional()
    .isIn(['lesson', 'practice'])
    .withMessage('Section must be one of: lesson, practice'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
]);

export const validateSubmoduleContentIdParam = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Submodule content ID is required')
    .isMongoId()
    .withMessage('Submodule content ID must be a valid MongoDB ObjectId')
]);

export const validateSubmoduleContentIdParamForRemove = validateChain([
  param('submoduleId')
    .notEmpty()
    .withMessage('Submodule ID is required')
    .isMongoId()
    .withMessage('Submodule ID must be a valid MongoDB ObjectId'),
  param('contentId')
    .notEmpty()
    .withMessage('Content ID is required')
    .isMongoId()
    .withMessage('Content ID must be a valid MongoDB ObjectId')
]);
