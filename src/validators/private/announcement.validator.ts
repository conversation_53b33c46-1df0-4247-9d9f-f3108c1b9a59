import { Re<PERSON><PERSON><PERSON><PERSON> } from 'express';
import { body, param } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const createAnnouncementValidator = validateChain([
  body('title')
    .notEmpty()
    .withMessage('Announcement title is required')
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Title must be between 3 and 100 characters'),

  body('content')
    .notEmpty()
    .withMessage('Announcement content is required')
    .isLength({ min: 10 })
    .withMessage('Content must be at least 10 characters'),

  body('course')
    .optional()
    .isMongoId()
    .withMessage('Course must be a valid MongoDB ID'),

  body('batch')
    .optional()
    .isMongoId()
    .withMessage('Batch must be a valid MongoDB ID'),

  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high'])
    .withMessage('Priority must be one of: low, medium, high'),

  body('course')
    .optional()
    .isMongoId()
    .withMessage('Course must be a valid MongoDB ID'),

  body('batch')
    .optional()
    .isMongoId()
    .withMessage('Batch must be a valid MongoDB ID'),

  body('module')
    .optional()
    .isMongoId()
    .withMessage('Module must be a valid MongoDB ID'),

  body('attachments')
    .optional()
    .isArray()
    .withMessage('Attachments must be an array'),

  body('attachments.*.name')
    .optional()
    .notEmpty()
    .withMessage('Attachment name is required'),

  body('attachments.*.url')
    .optional()
    .notEmpty()
    .withMessage('Attachment URL is required')
    .isURL()
    .withMessage('Attachment URL must be a valid URL'),

  body('attachments.*.type')
    .optional()
    .notEmpty()
    .withMessage('Attachment type is required'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),

  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('Expiry date must be a valid date')
]);

export const updateAnnouncementValidator: RequestHandler[] = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Announcement ID is required')
    .isMongoId()
    .withMessage('Announcement ID must be a valid MongoDB ID'),
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Title must be between 3 and 100 characters'),
  body('content')
    .optional()
    .isLength({ min: 10 })
    .withMessage('Content must be at least 10 characters'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high'])
    .withMessage('Priority must be one of: low, medium, high'),
  body('course')
    .optional()
    .isMongoId()
    .withMessage('Course must be a valid MongoDB ID'),
  body('batch')
    .optional()
    .isMongoId()
    .withMessage('Batch must be a valid MongoDB ID'),
  body('module')
    .optional()
    .isMongoId()
    .withMessage('Module must be a valid MongoDB ID'),
  body('attachments')
    .optional()
    .isArray()
    .withMessage('Attachments must be an array')
]);

export const checkAnnouncementIdValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('Announcement ID is required')
    .isMongoId()
    .withMessage('Announcement ID must be a valid MongoDB ID')
]);
