import { body, param } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const createNoteValidator = validateChain([
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isString()
    .withMessage('Title must be a string')
    .trim()
    .isLength({ min: 4 })
    .withMessage('Title must be at least 4 characters long'),

  body('content')
    .notEmpty()
    .withMessage('Content is required')
    .isString()
    .withMessage('Content must be a string')
    .trim()
    .isLength({ min: 10 })
    .withMessage('Content must be at least 10 characters long'),

  body('images').optional().isArray().withMessage('Images must be an array'),
  body('images.*')
    .optional()
    .isString()
    .withMessage('Each image must be a string'),

  body('points')
    .notEmpty()
    .withMessage('Points are required')
    .isInt({ min: 0 })
    .withMessage('Points must be a non-negative integer'),

  body('tags').optional().isArray().withMessage('Tags must be an array'),
  body('tags.*')
    .optional()
    .isString()
    .withMessage('Each tag must be a string')
    .trim(),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
]);

export const noteParamIdValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('ID is required')
    .isMongoId()
    .withMessage('ID must be a valid MongoDB ObjectId')
]);
