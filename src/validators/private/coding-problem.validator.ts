import { body, param } from 'express-validator';
import { validate<PERSON>hain } from '../../utils/helper';

export const createCodingProblemValidator = validateChain([
  body('problemId')
    .notEmpty()
    .withMessage('Problem ID is required')
    .isString()
    .withMessage('Problem ID must be a string'),
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isString()
    .withMessage('Title must be a string'),
  body('description')
    .notEmpty()
    .withMessage('Description is required')
    .isString()
    .withMessage('Description must be a string'),
  body('boilerplate').isArray().withMessage('Boilerplate must be an array'),
  body('testCases').isArray().withMessage('Test cases must be an array'),
  body('points')
    .notEmpty()
    .withMessage('Total points is required')
    .isNumeric()
    .withMessage('Total points must be a number'),
  body('difficulty')
    .notEmpty()
    .withMessage('Difficulty is required')
    .isIn(['easy', 'medium', 'hard'])
    .withMessage('Difficulty must be one of easy, medium, or hard'),
  body('hints').optional().isArray().withMessage('Hints must be an array'),
  body('solutions')
    .optional()
    .isArray()
    .withMessage('Solutions must be an array'),
  body('tags').optional().isArray().withMessage('Tags must be an array'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
]);

export const updateCodingProblemValidator = validateChain([
  param('id')
    .notEmpty()
    .withMessage('ID is required')
    .isString()
    .withMessage('ID must be a string'),
  body('problemId')
    .optional()
    .isString()
    .withMessage('Problem ID must be a string'),
  body('title').optional().isString().withMessage('Title must be a string'),
  body('description')
    .optional()
    .isString()
    .withMessage('Description must be a string'),
  body('boilerplate')
    .optional()
    .isArray()
    .withMessage('Boilerplate must be an array'),
  body('testCases')
    .optional()
    .isArray()
    .withMessage('Test cases must be an array'),
  body('totalPoints')
    .optional()
    .isNumeric()
    .withMessage('Total points must be a number'),
  body('difficulty')
    .optional()
    .isIn(['easy', 'medium', 'hard'])
    .withMessage('Difficulty must be one of easy, medium, or hard'),
  body('hints').optional().isArray().withMessage('Hints must be an array'),
  body('solutions')
    .optional()
    .isArray()
    .withMessage('Solutions must be an array'),
  body('tags').optional().isArray().withMessage('Tags must be an array'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
]);
