import { Document } from 'mongoose';
interface Name {
  firstName: string;
  lastName: string;
}
interface PersonalInfo {
  bio: string;
  dob: Date | null;
  profession: string;
  collegeOrCompanyOrSchool: string;
  location: {
    city: string;
    state: string;
    country: string;
  };
  resume: string;
}
interface DeviceInfo {
  userAgent: string | undefined;
  ipAddress: string | undefined;
}

export enum UserRole {
  BLOCKED = 'blocked',
  DELETED = 'deleted',
  STUDENT = 'student',
  MENTOR = 'mentor',
  BATCHMANAGER = 'batch_manager',
  SUPPORT = 'support',
  SALESANDMARKETING = 'sales_and_marketing',
  INSTRUCTOR = 'instructor',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

export const RoleHierarchy = {
  [UserRole.BLOCKED]: -2,
  [UserRole.DELETED]: -1,
  [UserRole.STUDENT]: 1,
  [UserRole.MENTOR]: 2,
  [UserRole.SUPPORT]: 3,
  [UserRole.SALESANDMARKETING]: 4,
  [UserRole.BATCHMANAGER]: 5,
  [UserRole.INSTRUCTOR]: 6,
  [UserRole.ADMIN]: 7,
  [UserRole.SUPER_ADMIN]: 8
} as const;

export interface IUser extends Document {
  _id: string; // Unique ID of the user
  name: Name; // First and last name of the user
  email: string; // Email address of the user
  avatar: string; // URL to the user's avatar image
  role: UserRole; // Roles of the user
  bio: string; // Biography of the user
  isActive: boolean; // Whether the user has verified their email address
  coursesEnrolled: Array<{ courseId: string }>; // Array of course IDs the user is enrolled in (for students)
  coursesTaught: Array<{ courseId: string }>; // Array of course IDs the user is teaching (for instructors)
  lastActivity: Date; // The date the user last logged in
  signAccessToken: () => string; // Method to sign a JWT access token for the user
  signRefreshToken: () => string; // Method to sign a JWT refresh token for the user
  refreshToken: string | undefined; // Refresh token for the user
  deviceInfo: DeviceInfo; // Information about the user's device
  phone: {
    code: string; // Country code of the user's phone number
    number: string; // Phone number of the user
  }; // Phone number of the user
  personal_info: PersonalInfo; // Personal information of the user
  batches: Array<{ batchId: string }>; // Array of batch IDs the user is part of
}

export interface IUserDao {
  findAll(
    filter: Record<string, string | number | boolean | Date> | object,
    sort: Record<string, 1 | -1>,
    skip: number,
    limit: number
  ): Promise<IUser[]>;
  findById(id: string): Promise<IUser | null>;
  findOne(
    filter: Record<string, string | number | boolean | Date> | object
  ): Promise<IUser | null>;
  create(data: Partial<IUser>): Promise<IUser>;
  update(id: string, data: Partial<IUser>): Promise<IUser | null>;
  delete(id: string): Promise<boolean>;
  count(filter: Record<string, string | number | boolean>): Promise<number>;
  findByIdWithRefreshToken(id: string): Promise<IUser | null>;
  save(user: IUser): Promise<IUser>;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}
