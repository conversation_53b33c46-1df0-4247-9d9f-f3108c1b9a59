import mongoose, { FilterQuery, SortOrder, Types } from 'mongoose';
import { ICodeSubmission } from './code-submission.interface';

export interface ISubmissionStorage {
  _id: Types.ObjectId | string; // Allow both ObjectId and string representation
  userId: Types.ObjectId | string; // Allow both ObjectId and string representation
  questionId: Types.ObjectId | string; // Allow both ObjectId and string representation
  submissionId: [Types.ObjectId | string] | [ICodeSubmission]; // Allow both ObjectId and string representation
  language: string;
  hasSeenSolution: boolean;
  status: string; // e.g., "pending", "completed", "failed"
  code: string;
  date: Date;
  isExecuting: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ISubmissionStorageDao {
  findAll(
    filter: FilterQuery<ISubmissionStorage>,
    sort: { [key: string]: SortOrder },
    skip?: number,
    limit?: number,
    populate?: string | string[]
  ): Promise<ISubmissionStorage[]>;

  findById(
    id: string,
    populate?: string | string[]
  ): Promise<ISubmissionStorage | null>;

  findOne(
    filter: FilterQuery<ISubmissionStorage>,
    populate?: string | string[]
  ): Promise<ISubmissionStorage | null>;

  create(
    data: Partial<ISubmissionStorage>,
    session?: mongoose.ClientSession
  ): Promise<ISubmissionStorage>;

  update(
    filter: FilterQuery<ISubmissionStorage>,
    data: Partial<ISubmissionStorage>,
    session?: mongoose.ClientSession
  ): Promise<ISubmissionStorage | null>;

  delete(
    filter: FilterQuery<ISubmissionStorage>,
    session?: mongoose.ClientSession
  ): Promise<ISubmissionStorage | null>;
}
