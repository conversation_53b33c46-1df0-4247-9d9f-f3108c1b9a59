import mongoose, { Document, FilterQuery, Types } from 'mongoose';

interface IAttachment {
  name: string;
  url: string;
  type: string;
}

interface IDoubtResponse {
  user: Types.ObjectId;
  userName: string;
  userAvatar?: string;
  userRole: string;
  content: string;
  attachments?: IAttachment[];
  codeSnippet?: string;
  createdAt: Date;
  isAnswer: boolean;
}

export interface IDoubt extends Document {
  user: Types.ObjectId;
  userName: string;
  userAvatar?: string;
  course: Types.ObjectId;
  module?: Types.ObjectId;
  subModule?: Types.ObjectId;

  contentType?: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab';
  contentId?: Types.ObjectId;
  contentModel?: 'Video' | 'Note' | 'MCQ' | 'CodingProblem' | 'CodingLab';

  title: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high';
  attachments?: IAttachment[];
  codeSnippet?: string;

  responses: IDoubtResponse[];
  assignedTo?: Types.ObjectId;
  resolvedBy?: Types.ObjectId;
  resolvedAt?: Date;

  createdAt: Date;
  updatedAt: Date;
}

export interface IDoubtDao {
  create(
    data: Partial<IDoubt>,
    session?: mongoose.ClientSession
  ): Promise<IDoubt>;

  findById(id: string): Promise<IDoubt | null>;

  findAll(
    filter: FilterQuery<IDoubt>,
    sort: { [key: string]: -1 | 1 },
    skip: number,
    limit: number
  ): Promise<IDoubt[]>;

  update(id: string, data: Partial<IDoubt>): Promise<IDoubt | null>;

  markAsResolved(
    id: string,
    resolvedBy: Types.ObjectId
  ): Promise<IDoubt | null>;

  assignDoubt(id: string, assignedTo: Types.ObjectId): Promise<IDoubt | null>;
}
