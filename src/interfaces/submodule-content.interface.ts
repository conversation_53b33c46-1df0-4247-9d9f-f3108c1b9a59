import mongoose, { Document, FilterQuery, Types } from 'mongoose';
import { ISubModule } from './submodule.interface';
import { IVideo } from './video.interface';
import { INote } from './note.interface';
import { IMCQ } from './mcq.interface';
import { ICodingProblem } from './coding-problem.interface';
import { ICodingLab } from './coding-lab.interface';

export interface ISubmoduleContent extends Document {
  _id: Types.ObjectId | string;
  submodule: Types.ObjectId | ISubModule | string;
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab';
  contentId:
    | Types.ObjectId
    | IVideo
    | INote
    | IMCQ
    | ICodingProblem
    | ICodingLab
    | string;
  contentModel: 'Video' | 'Note' | 'MCQ' | 'CodingProblem' | 'CodingLab';
  section: 'lesson' | 'practice';
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ISubmoduleContentDao {
  findAll(
    filter: FilterQuery<ISubmoduleContent>,
    sort: { [key: string]: -1 | 1 },
    skip: number,
    limit: number
  ): Promise<ISubmoduleContent[]>;
  findById(id: string): Promise<ISubmoduleContent | null>;
  create(
    data: Partial<ISubmoduleContent>,
    session?: mongoose.ClientSession
  ): Promise<ISubmoduleContent>;
  update(
    id: string,
    data: Partial<ISubmoduleContent>
  ): Promise<ISubmoduleContent | null>;
  delete(id: string): Promise<ISubmoduleContent | null>;
  findOne(
    filter: FilterQuery<ISubmoduleContent>
  ): Promise<ISubmoduleContent | null>;
}

export interface ISubmoduleContentModel
  extends mongoose.Model<ISubmoduleContent> {
  calculateMcqTotalPoints(
    submoduleId: mongoose.Types.ObjectId | string
  ): Promise<number>;
}
