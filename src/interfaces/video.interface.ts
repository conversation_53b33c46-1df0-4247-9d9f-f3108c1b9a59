import mongoose, { Document, FilterQuery } from 'mongoose';

export interface IVideo extends Document {
  title: string;
  description: string;
  videoId: string; // Unique ID from video service (generic)
  provider?: string; // Video service provider (e.g., 'vdocipher', 'youtube', etc.)
  duration?: number; // in seconds
  points: number; // Points awarded for watching
  tags: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IVideoDao {
  findAll(
    filter: FilterQuery<IVideo>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number,
    populate?: string | string[]
  ): Promise<IVideo[]>;
  create(
    data: Partial<IVideo>,
    session?: mongoose.ClientSession
  ): Promise<IVideo>;

  update(id: string, data: Partial<IVideo>): Promise<IVideo | null>;

  findById(id: string, populate?: string | string[]): Promise<IVideo | null>;

  delete(
    id: string | mongoose.Types.ObjectId,
    session?: mongoose.ClientSession
  ): Promise<IVideo | null>;
}
