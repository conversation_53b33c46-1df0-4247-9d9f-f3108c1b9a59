import mongoose, { Document } from 'mongoose';

export interface INote extends Document {
  title: string;
  content: string;
  images?: string[];
  points: number;
  tags?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface INoteDao {
  create(
    data: Partial<INote>,
    session?: mongoose.ClientSession
  ): Promise<INote>;
  findById(
    id: string | mongoose.Types.ObjectId,
    populate?: string | string[]
  ): Promise<INote | null>;
  update(
    id: string | mongoose.Types.ObjectId,
    data: Partial<INote>,
    session?: mongoose.ClientSession
  ): Promise<INote | null>;
  delete(id: string | mongoose.Types.ObjectId): Promise<INote | null>;
}
