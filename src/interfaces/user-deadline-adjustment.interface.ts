import { Document, Types } from 'mongoose';

export interface IPauseHistory {
  startDate: Date;
  endDate?: Date;
  daysUsed: number;
  status: 'active' | 'completed';
}

export interface ISubmoduleAdjustment {
  submodule: Types.ObjectId;
  additionalDays: number;
  customStartDate?: Date;
  customDeadline?: Date;
  isExempt: boolean;
  reason?: string;
}

export interface IUserDeadlineAdjustment extends Document {
  user: Types.ObjectId;
  batch: Types.ObjectId;
  totalPauseDays: number;
  pauseHistory: IPauseHistory[];
  submoduleAdjustments: ISubmoduleAdjustment[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
