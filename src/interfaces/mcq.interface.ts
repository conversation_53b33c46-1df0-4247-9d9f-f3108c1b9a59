import mongoose, { Document, FilterQuery } from 'mongoose';

export interface IMCQ extends Document {
  title: string;
  question: string;
  image?: string;
  codeSnippets?: {
    language: string;
    code: string;
  }[];
  options: {
    text: string;
    isCorrect: boolean | undefined;
  }[];
  selectionType: 'single' | 'multiple';
  points: number;
  explanation?: {
    text?: string;
    video?: string;
    image?: string;
  };
  difficulty: 'easy' | 'medium' | 'hard';
  tags?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IMCQDao {
  findAll(
    filter: FilterQuery<IMCQ>,
    sort: { [key: string]: -1 | 1 },
    skip: number,
    limit: number
  ): Promise<IMCQ[]>;

  findById(id: string): Promise<IMCQ | null>;

  create(data: Partial<IMCQ>, session?: mongoose.ClientSession): Promise<IMCQ>;

  update(id: string, data: Partial<IMCQ>): Promise<IMCQ | null>;

  delete(id: string): Promise<IMCQ | null>;

  findOne(filter: FilterQuery<IMCQ>): Promise<IMCQ | null>;
}
