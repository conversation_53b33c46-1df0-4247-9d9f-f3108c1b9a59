import mongoose, { Document, FilterQuery, Types } from 'mongoose';

export interface ILeaderboard extends Document {
  course: Types.ObjectId;
  batch: Types.ObjectId;
  period: 'daily' | 'weekly' | 'monthly' | 'overall';
  startDate: Date;
  endDate: Date;
  rankings: UserRankingInfo[];
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserRankingInfo {
  user: Types.ObjectId;
  userName?: string;
  userAvatar?: string;
  rank: number;
  points: number;
  progress: number;
  streak: number;
}

export interface ILeaderboardDao {
  findAll(
    filter: FilterQuery<ILeaderboard>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number
  ): Promise<ILeaderboard[]>;

  findById(id: string): Promise<ILeaderboard | null>;

  findOne(filter: FilterQuery<ILeaderboard>): Promise<ILeaderboard | null>;

  create(
    data: Partial<ILeaderboard>,
    session?: mongoose.ClientSession
  ): Promise<ILeaderboard>;

  update(id: string, data: Partial<ILeaderboard>): Promise<ILeaderboard | null>;

  delete(id: string): Promise<ILeaderboard | null>;

  findByBatch(
    batchId: string,
    period: 'daily' | 'weekly' | 'monthly' | 'overall'
  ): Promise<ILeaderboard | null>;

  updateRankings(
    id: string,
    rankings: ILeaderboard['rankings']
  ): Promise<ILeaderboard | null>;
}

export const ALLOWED_PERIODS = [
  'daily',
  'weekly',
  'monthly',
  'overall'
] as const;
export type Period = (typeof ALLOWED_PERIODS)[number];
