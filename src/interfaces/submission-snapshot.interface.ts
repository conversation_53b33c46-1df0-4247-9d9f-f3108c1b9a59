import { Document, Types } from 'mongoose';

export interface IDeadlineSnapshot {
  originalDeadline: Date;
  adjustedDeadline: Date;
  startDate: Date;
  penaltyRules: Array<{
    daysLate: number;
    penaltyPercentage: number;
  }>;
  batchDeadlineVersion: number; // Version of batch deadline when submission was made
  userAdjustmentVersion: number; // Version of user adjustments when submission was made
}

export interface ISubmodulePositionSnapshot {
  moduleId: Types.ObjectId;
  moduleOrder: number;
  submoduleOrder: number;
  globalPosition: number; // Position across all modules
  previousSubmoduleId?: Types.ObjectId;
  nextSubmoduleId?: Types.ObjectId;
  courseStructureVersion: number; // Version of course structure when submission was made
}

export interface ISubmissionSnapshot extends Document {
  user: Types.ObjectId;
  batch: Types.ObjectId;
  submodule: Types.ObjectId;
  contentId: Types.ObjectId;
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab';
  
  // Submission details
  submissionDate: Date;
  originalPoints: number;
  earnedPoints: number;
  
  // Frozen deadline state at time of submission
  deadlineSnapshot: IDeadlineSnapshot;
  
  // Frozen submodule position at time of submission
  positionSnapshot: ISubmodulePositionSnapshot;
  
  // Calculated penalty (frozen at submission time)
  penaltyCalculation: {
    daysLate: number;
    penaltyPercentage: number;
    penaltyApplied: number;
    pointsAfterPenalty: number;
    wasOnTime: boolean;
  };
  
  // Unlock validation (frozen at submission time)
  unlockValidation: {
    wasUnlocked: boolean;
    unlockReason: string;
    prerequisitesMet: boolean;
    previousSubmoduleCompleted?: boolean;
    dateRequirementMet?: boolean;
  };
  
  // Metadata
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ISubmissionSnapshotDao {
  create(data: Partial<ISubmissionSnapshot>): Promise<ISubmissionSnapshot>;
  findByUserAndContent(
    userId: string,
    contentId: string,
    submoduleId: string
  ): Promise<ISubmissionSnapshot | null>;
  findByUserAndSubmodule(
    userId: string,
    submoduleId: string
  ): Promise<ISubmissionSnapshot[]>;
  findByUserAndBatch(
    userId: string,
    batchId: string
  ): Promise<ISubmissionSnapshot[]>;
}
