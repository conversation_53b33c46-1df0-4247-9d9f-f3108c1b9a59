import mongoose, { Document, FilterQuery, Types } from 'mongoose';

export interface IBatchPause extends Document {
  user: Types.ObjectId;
  batch: Types.ObjectId;
  course: Types.ObjectId;
  startDate: Date;
  endDate: Date;
  reason: string;
  status: 'active' | 'completed';
  daysUsed: number;
  comments?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IBatchPauseDao {
  findAll(
    filter: FilterQuery<IBatchPause>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number
  ): Promise<IBatchPause[]>;

  findById(id: string): Promise<IBatchPause | null>;

  findOne(filter: FilterQuery<IBatchPause>): Promise<IBatchPause | null>;

  create(
    data: Partial<IBatchPause>,
    session?: mongoose.ClientSession
  ): Promise<IBatchPause>;

  update(id: string, data: Partial<IBatchPause>): Promise<IBatchPause | null>;

  delete(id: string): Promise<IBatchPause | null>;

  findActiveByUser(userId: string): Promise<IBatchPause[]>;

  completePause(id: string, daysUsed: number): Promise<IBatchPause | null>;
}
