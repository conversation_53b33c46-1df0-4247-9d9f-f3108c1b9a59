import mongoose, { SortOrder } from 'mongoose';

export interface IActivityLog {
  userId: mongoose.Types.ObjectId;
  userName: string;
  action: string;
  entityType?: string;
  description: string;
  entityId?: mongoose.Types.ObjectId;
  details?: Record<string, string | number | boolean | object | undefined>;
  ipAddress?: string;
  userAgent?: string;
  createdAt?: Date;
}

export interface IActivityLogDao {
  findAll(
    filter: Record<string, string | number | boolean | Date | object>,
    sort: Record<string, SortOrder>,
    skip: number,
    limit: number
  ): Promise<IActivityLog[]>;

  findById(id: string): Promise<IActivityLog | null>;

  findOne(
    filter: Record<string, string | number | boolean | Date | object>
  ): Promise<IActivityLog | null>;

  create(data: Partial<IActivityLog>): Promise<IActivityLog>;

  update(id: string, data: Partial<IActivityLog>): Promise<IActivityLog | null>;

  delete(id: string): Promise<IActivityLog | null>;

  findByUser(
    userId: string,
    filter: Record<string, string | number | boolean | Date | object>,
    sort: Record<string, SortOrder>,
    skip: number,
    limit: number
  ): Promise<IActivityLog[]>;

  findByAction(
    actionType: string,
    filter: Record<string, string | number | boolean | Date | object>,
    sort: Record<string, SortOrder>,
    skip: number,
    limit: number
  ): Promise<IActivityLog[]>;

  count(
    filter: Record<string, string | number | boolean | Date | object>
  ): Promise<number>;

  deleteMany(
    filter: Record<string, string | number | boolean | Date | object>
  ): Promise<number>;
}

export const ActionTypes = {
  ANNOUNCEMENT: {
    CREATE: 'announcement.create',
    UPDATE: 'announcement.update',
    DELETE: 'announcement.delete',
    VIEW: 'announcement.view',
    ISACTIVE: 'announcement.isActive',
    ISDELETED: 'announcement.isDeleted',
    ISARCHIVED: 'announcement.isArchived'
  },
  CONTENT: {
    CREATE: 'content.create',
    UPDATE: 'content.update',
    DELETE: 'content.delete',
    VIEW: 'content.view',
    COMPLETE: 'content.complete'
  },
  ASSESSMENT: {
    CREATE: 'assessment.create',
    UPDATE: 'assessment.update',
    DELETE: 'assessment.delete',
    ATTEMPT: 'assessment.attempt',
    SUBMIT: 'assessment.submit',
    COMPLETE: 'assessment.complete',
    GRADE: 'assessment.grade'
  },
  MODULE: {
    CREATE: 'module.create',
    UPDATE: 'module.update',
    DELETE: 'module.delete',
    COMPLETE: 'module.complete'
  },
  SUBMODULE: {
    CREATE: 'submodule.create',
    UPDATE: 'submodule.update',
    DELETE: 'submodule.delete',
    COMPLETE: 'submodule.complete',
    UNLOCK: 'submodule.unlock'
  },
  BATCHSTATUS: {
    PAUSE: 'batch.pause',
    CONTINUE: 'batch.continue',
    COMPLETE: 'batch.complete'
  },
  USER: {
    CREATE: 'user.create',
    UPDATE: 'user.update',
    DELETE: 'user.delete',
    LOGIN: 'user.login',
    LOGOUT: 'user.logout',
    PASSWORD_CHANGE: 'user.password_change',
    PASSWORD_RESET: 'user.password_reset',
    EMAIL_VERIFY: 'user.email_verify',
    ROLE_CHANGE: 'user.role_change'
  },
  USER_DEADLINE: {
    CREATE: 'user_deadline.create',
    UPDATE: 'user_deadline.update',
    ADJUST: 'user_deadline.adjust',
    PAUSE_ADJUSTMENT: 'user_deadline.pause_adjustment',
    RESUME_ADJUSTMENT: 'user_deadline.resume_adjustment',
    INITIALIZE: 'user_deadline.initialize'
  },
  BATCH_SUBMODULE_DEADLINE: {
    CREATE: 'batch_submodule_deadline.create',
    UPDATE: 'batch_submodule_deadline.update',
    DELETE: 'batch_submodule_deadline.delete'
  },
  CODE_PAIR_SESSION: {
    CREATE: 'code_pair_session.create',
    UPDATE: 'code_pair_session.update',
    DELETE: 'code_pair_session.delete',
    REQUEST: 'code_pair_session.request',
    SCHEDULE: 'code_pair_session.schedule',
    START: 'code_pair_session.start',
    COMPLETE: 'code_pair_session.complete',
    CANCEL: 'code_pair_session.cancel',
    FEEDBACK: 'code_pair_session.feedback'
  },
  ENROLLED_COURSE: {
    CREATE: 'enrolled_course.create',
    UPDATE: 'enrolled_course.update',
    DELETE: 'enrolled_course.delete',
    PAUSE: 'enrolled_course.pause',
    RESUME: 'enrolled_course.resume',
    EXPIRE: 'enrolled_course.expire',
    EXTEND: 'enrolled_course.extend'
  },
  USER_PROGRESS: {
    UPDATE: 'user_progress.update',
    CONTENT_COMPLETE: 'user_progress.content_complete',
    ASSESSMENT_COMPLETE: 'user_progress.assessment_complete',
    MODULE_COMPLETE: 'user_progress.module_complete',
    SUBMODULE_COMPLETE: 'user_progress.submodule_complete',
    POINTS_EARNED: 'user_progress.points_earned',
    PENALTY_APPLIED: 'user_progress.penalty_applied'
  },
  BATCH_PAUSE: {
    CREATE: 'batch_pause.create',
    UPDATE: 'batch_pause.update',
    START: 'batch_pause.start',
    COMPLETE: 'batch_pause.complete',
    RESUME_EARLY: 'batch_pause.resume_early',
    EXPIRE: 'batch_pause.expire'
  },
  ERROR_LOG: {
    CREATE: 'error_log.create'
  },
  CERTIFICATE: {
    CREATE: 'certificate.create',
    UPDATE: 'certificate.update',
    DELETE: 'certificate.delete',
    ISSUE: 'certificate.issue',
    DOWNLOAD: 'certificate.download',
    VERIFY: 'certificate.verify'
  },
  USER_STREAK: {
    UPDATE: 'user_streak.update',
    INCREMENT: 'user_streak.increment',
    RESET: 'user_streak.reset',
    LONGEST: 'user_streak.longest'
  },
  DOUBT: {
    CREATE: 'doubt.create',
    UPDATE: 'doubt.update',
    DELETE: 'doubt.delete',
    RESPONSE_ADD: 'doubt.response_add',
    ASSIGN: 'doubt.assign',
    RESOLVE: 'doubt.resolve',
    CLOSE: 'doubt.close',
    REOPEN: 'doubt.reopen',
    PRIORITY_CHANGE: 'doubt.priority_change'
  },
  LEADERBOARD: {
    UPDATE: 'leaderboard.update',
    GENERATE: 'leaderboard.generate'
  },
  VIDEO: {
    CREATE: 'video.create',
    UPDATE: 'video.update',
    DELETE: 'video.delete',
    VIEW: 'video.view',
    COMPLETE: 'video.complete'
  },
  CODING_PROBLEM: {
    CREATE: 'coding_problem.create',
    UPDATE: 'coding_problem.update',
    DELETE: 'coding_problem.delete',
    VIEW: 'coding_problem.view',
    ATTEMPT: 'coding_problem.attempt',
    SUBMIT: 'coding_problem.submit',
    COMPLETE: 'coding_problem.complete'
  },
  NOTE: {
    CREATE: 'note.create',
    UPDATE: 'note.update',
    DELETE: 'note.delete',
    VIEW: 'note.view',
    LIST: 'note.list'
  }
};
