import mongoose, { Document, FilterQuery, Types } from 'mongoose';

export interface IBookmark extends Document {
  user: Types.ObjectId | string;
  batch: string;
  submodule: Types.ObjectId;
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab';
  contentId: Types.ObjectId;
  contentModel: string; // Will be set automatically based on contentType
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IBookmarkDao {
  findAll(
    filter: FilterQuery<IBookmark>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number
  ): Promise<IBookmark[]>;

  findById(id: string): Promise<IBookmark | null>;

  create(
    data: Partial<IBookmark>,
    session?: mongoose.ClientSession
  ): Promise<IBookmark>;

  update(id: string, data: Partial<IBookmark>): Promise<IBookmark | null>;

  delete(id: string): Promise<IBookmark | null>;

  findOne(filter: FilterQuery<IBookmark>): Promise<IBookmark | null>;

  getUserBookmarksByBatchId(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId
  ): Promise<IBookmark[]>;

  toggleBookmark(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId,
    contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
    contentId: mongoose.Types.ObjectId
  ): Promise<{ bookmarked: boolean; message: string }>;

  isBookmarked(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
    contentId: mongoose.Types.ObjectId
  ): Promise<boolean>;
}
