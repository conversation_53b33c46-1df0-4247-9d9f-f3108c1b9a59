import { Document, Types } from 'mongoose';

export interface IAnnouncement extends Document {
  title: string;
  content: string;
  author: Types.ObjectId;
  authorName: string;
  course?: Types.ObjectId;
  batch?: Types.ObjectId;
  module?: Types.ObjectId;
  priority: 'low' | 'medium' | 'high';
  attachments?: {
    name: string;
    url: string;
    type: string;
  }[];
  isActive: boolean;
  expiresAt?: Date;
  readBy: Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
}

export interface IAnnouncementDao {
  create(data: Partial<IAnnouncement>): Promise<IAnnouncement>;
  findAll(
    filter: Record<string, unknown>,
    populate?: string | string[]
  ): Promise<IAnnouncement[]>;
  findOne(
    filter: Record<string, unknown>,
    sort?: Record<string, unknown>,
    populate?: string | string[]
  ): Promise<IAnnouncement | null>;
  update(
    filter: Record<string, unknown>,
    data: Partial<IAnnouncement>
  ): Promise<IAnnouncement | null>;
  delete(filter: Record<string, unknown>): Promise<IAnnouncement | null>;
  findById(
    id: string,
    populate?: string | string[]
  ): Promise<IAnnouncement | null>;
  findByIdAndUpdate(
    id: string,
    data: Partial<IAnnouncement>
  ): Promise<IAnnouncement | null>;
}
