import { Document, FilterQuery, Types } from 'mongoose';

export interface IModule extends Document {
  title: string;
  description: string;
  course: Types.ObjectId | string; // Reference to the course
  order: number;
  duration: number;
  isActive: boolean;
  totalPoints: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IModuleDao {
  findAll(
    filter: FilterQuery<IModule>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number,
    populate?: string | string[]
  ): Promise<IModule[]>;

  findById(id: string, populate?: string | string[]): Promise<IModule | null>;

  findOne(
    filter: FilterQuery<IModule>,
    populate?: string | string[]
  ): Promise<IModule | null>;

  create(data: Partial<IModule>): Promise<IModule>;

  update(id: string, data: Partial<IModule>): Promise<IModule | null>;
}
