import mongoose from 'mongoose';

export interface IDiscordAccess {
  channel_id: string;
  role_id: string;
}

export interface ILastViewedContent {
  contentId: mongoose.Types.ObjectId | string;
  submoduleId: mongoose.Types.ObjectId | string;
  moduleId: mongoose.Types.ObjectId | string;
  title?: string;
  viewedAt: Date;
}
export interface IEnrolledCourseLink {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  courseId: mongoose.Types.ObjectId;
  batchId: mongoose.Types.ObjectId;
  enrollmentId: mongoose.Types.ObjectId;
  courseTitle: string;
  purchaseDate: string | number | Date;
  expiryDate?: Date;
  status?: IEnrolledCourseLinkStatus; // 'active' | 'inactive' | 'expired'
  discord: IDiscordAccess;
  pausedUntil: Date | null; // date until the course is paused
  lastActivity: Date; // last activity date of the user in the course
  lastViewedContent?: ILastViewedContent;
  createdAt: Date;
  updatedAt: Date;
}

export enum IEnrolledCourseLinkStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired'
}

export interface IEnrolledCourseLinkDao {
  create(data: Partial<IEnrolledCourseLink>): Promise<IEnrolledCourseLink>;
  findById(id: string): Promise<IEnrolledCourseLink | null>;
  findAll(
    filter?: Record<string, string | number | boolean | object> | object,
    sort?: Record<string, 1 | -1>,
    skip?: number,
    limit?: number
  ): Promise<IEnrolledCourseLink[]>;
  findOne(
    filter: Record<string, string | number | boolean | object> | object
  ): Promise<IEnrolledCourseLink | null>;
  update(
    id: string,
    data: Partial<IEnrolledCourseLink>
  ): Promise<IEnrolledCourseLink | null>;
  delete(id: string): Promise<IEnrolledCourseLink | null>;
}
