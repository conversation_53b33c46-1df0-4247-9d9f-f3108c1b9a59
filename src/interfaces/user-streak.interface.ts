import mongoose, { Document, FilterQuery, Types } from 'mongoose';

export interface IUserStreak extends Document {
  user: Types.ObjectId;
  currentStreak: number;
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab';
  longestStreak: number;
  lastActivityDate: Date;
  streakHistory: {
    date: Date;
    pointsEarned: number;
    activities: {
      type: 'lesson' | 'practice';
      contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab';
      entityId?: Types.ObjectId;
      entityType?: string;
      pointsEarned: number;
      timestamp: Date;
    }[];
  }[];
  totalDaysActive: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUserStreakDao {
  findAll(
    filter: FilterQuery<IUserStreak>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number
  ): Promise<IUserStreak[]>;

  findById(id: string): Promise<IUserStreak | null>;

  findByUser(userId: string): Promise<IUserStreak | null>;

  findOne(filter: FilterQuery<IUserStreak>): Promise<IUserStreak | null>;

  create(
    data: Partial<IUserStreak>,
    session?: mongoose.ClientSession
  ): Promise<IUserStreak>;

  update(id: string, data: Partial<IUserStreak>): Promise<IUserStreak | null>;

  incrementStreak(
    userId: string,
    points: number,
    activityData: {
      type: 'lesson' | 'practice';
      entityId?: Types.ObjectId;
      entityType?: string;
      pointsEarned: number;
    }
  ): Promise<IUserStreak | null>;

  resetStreak(userId: string): Promise<IUserStreak | null>;
}
