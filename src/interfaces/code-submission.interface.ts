import mongoose, { FilterQuery, Types } from 'mongoose';

export interface TestCase {
  stdin: string;
  stdout: string;
  expectedOutput: string;
  passed: boolean;
  error: string | null; // Allow null values to match schema default
  executionTime: number; // in milliseconds
  memoryUsage: number; // in kilobytes
}

export interface ICodeSubmission {
  _id: string | Types.ObjectId; // Allow both ObjectId and string representation
  testcases: TestCase[];
  date: Date;
  language: string;
  code: string;
  submissionStorageId: Types.ObjectId | string; // Allow both ObjectId and string representation
  createdAt?: Date; // added by mongoose timestamps
  updatedAt?: Date; // added by mongoose timestamps
}

export interface ICodeSubmissionDao {
  findAll(
    filter: FilterQuery<ICodeSubmission>,
    sort: { [key: string]: -1 | 1 },
    skip: number,
    limit: number,
    populate?: string | string[]
  ): Promise<ICodeSubmission[]>;

  findById(
    id: string,
    populate?: string | string[]
  ): Promise<ICodeSubmission | null>;

  findOne(
    filter: FilterQuery<ICodeSubmission>,
    populate?: string | string[]
  ): Promise<ICodeSubmission | null>;

  create(
    data: Partial<ICodeSubmission>,
    session?: mongoose.ClientSession
  ): Promise<ICodeSubmission>;

  update(
    id: string,
    data: Partial<ICodeSubmission>
  ): Promise<ICodeSubmission | null>;

  delete(id: string): Promise<ICodeSubmission | null>;
}
