import { Document, FilterQuery, Types } from 'mongoose';

export interface IBatchSubmoduleDeadline extends Document {
  batch: Types.ObjectId;
  submodule: Types.ObjectId;
  startDate: Date;
  deadline: Date;
  dependsOnPreviousSubmodule: boolean;
  days: number;
  penaltyRules: {
    daysLate: number;
    penaltyPercentage: number;
  }[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IBatchSubmoduleDeadlineDao {
  findAll(
    filter: FilterQuery<IBatchSubmoduleDeadline>,
    sort: { [key: string]: -1 | 1 },
    skip: number,
    limit: number
  ): Promise<IBatchSubmoduleDeadline[]>;
  findById(id: string): Promise<IBatchSubmoduleDeadline | null>;
  create(data: IBatchSubmoduleDeadline): Promise<IBatchSubmoduleDeadline>;
  update(
    id: string,
    data: Partial<IBatchSubmoduleDeadline>
  ): Promise<IBatchSubmoduleDeadline | null>;
  delete(id: string): Promise<IBatchSubmoduleDeadline | null>;
  findOne(
    filter: Partial<IBatchSubmoduleDeadline>
  ): Promise<IBatchSubmoduleDeadline | null>;
  findBySubmoduleAndUpdate(
    submodule: string | Types.ObjectId,
    data: Partial<IBatchSubmoduleDeadline>
  ): Promise<IBatchSubmoduleDeadline | null>;
  findOneAndUpdate(
    filter: FilterQuery<IBatchSubmoduleDeadline>,
    data: Partial<IBatchSubmoduleDeadline>
  ): Promise<IBatchSubmoduleDeadline | null>;
}
