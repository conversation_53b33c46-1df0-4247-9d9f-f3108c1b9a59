import mongoose, { Document, FilterQuery } from 'mongoose';

export interface ICodingLab extends Document {
  title: string;
  description: string;
  instructions: string;
  boilerplate: {
    language: string;
    code: string;
    compilerId: string; // Judge0 compiler ID
  }[];
  testCases: {
    input: string;
    expectedOutput: string;
    isHidden: boolean;
    points: number; // Points for this specific test case
  }[];
  points: number; // Total possible points for this lab
  solutions: {
    language: string;
    code: string;
    explanation: string;
  }[];
  tags: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICodingLabDao {
  findAll(
    filter: FilterQuery<ICodingLab>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number
  ): Promise<ICodingLab[]>;

  findById(id: string): Promise<ICodingLab | null>;

  create(
    data: Partial<ICodingLab>,
    session?: mongoose.ClientSession
  ): Promise<ICodingLab>;

  update(id: string, data: Partial<ICodingLab>): Promise<ICodingLab | null>;

  delete(id: string): Promise<ICodingLab | null>;

  findOne(filter: FilterQuery<ICodingLab>): Promise<ICodingLab | null>;

  findByTags(tags: string[]): Promise<ICodingLab[]>;
}
