import mongoose, { Document, Types } from 'mongoose';

export interface ICodePairSession extends Document {
  student: Types.ObjectId;
  mentor: Types.ObjectId;
  course: Types.ObjectId;
  module?: Types.ObjectId;
  subModule?: Types.ObjectId;
  // Content reference
  contentType?: 'mcq' | 'coding_problem' | 'coding_lab';
  contentId?: Types.ObjectId;
  contentModel?: string; // Will be set automatically based on contentType
  sessionId: string;
  status: 'requested' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  scheduledStartTime?: Date;
  scheduledEndTime?: Date;
  actualStartTime?: Date;
  actualEndTime?: Date;
  duration?: number;
  topic: string;
  description: string;
  codeLanguage: string;
  initialCode?: string;
  finalCode?: string;
  notes?: string;
  feedback?: {
    rating?: number;
    comments?: string;
  };
  recordingUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICodePairSessionDao {
  findAll(
    filter: Record<string, unknown>,
    sort: Record<string, 1 | -1>,
    skip: number,
    limit: number
  ): Promise<ICodePairSession[]>;
  findById(id: string): Promise<ICodePairSession | null>;
  findOne(filter: Record<string, unknown>): Promise<ICodePairSession | null>;
  create(
    data: Partial<ICodePairSession>,
    session?: mongoose.ClientSession
  ): Promise<ICodePairSession>;
  update(
    id: string,
    data: Partial<ICodePairSession>
  ): Promise<ICodePairSession | null>;
}
