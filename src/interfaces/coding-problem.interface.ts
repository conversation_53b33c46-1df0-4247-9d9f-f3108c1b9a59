import mongoose, { Document, FilterQuery } from 'mongoose';

interface ITestCase {
  input: string;
  expectedOutput: string;
  isHidden: boolean;
  points: number;
}

interface IBoilerplate {
  language: string;
  code: string;
  compilerId: string;
  mainCode: string;
}

interface IHint {
  language: string;
  text?: string;
  image?: string;
  video?: string;
}

interface ISolution {
  language: string;
  code: string;
  editorial?: string;
  video?: string;
}

export interface ICodingProblem extends Document {
  problemId: string;
  title: string;
  description: string;
  boilerplate: IBoilerplate[];
  testCases: ITestCase[];
  points: number;
  difficulty: 'easy' | 'medium' | 'hard';
  hints?: IHint[];
  solutions?: ISolution[];
  tags?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICodingProblemDao {
  findAll(
    filter: FilterQuery<ICodingProblem>,
    sort: { [key: string]: -1 | 1 },
    skip: number,
    limit: number
  ): Promise<ICodingProblem[]>;

  findById(id: string): Promise<ICodingProblem | null>;

  findByProblemId(problemId: string): Promise<ICodingProblem | null>;

  create(
    data: Partial<ICodingProblem>,
    session?: mongoose.ClientSession
  ): Promise<ICodingProblem>;

  update(
    id: string,
    data: Partial<ICodingProblem>
  ): Promise<ICodingProblem | null>;

  delete(
    id: string,
    session?: mongoose.ClientSession
  ): Promise<ICodingProblem | null>;

  findOne(filter: FilterQuery<ICodingProblem>): Promise<ICodingProblem | null>;

  findByLanguage(language: string): Promise<ICodingProblem[]>;
}
