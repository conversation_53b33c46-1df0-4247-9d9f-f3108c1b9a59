import { ICodingProblemSolution } from '../services/public/coding-problem.service';

export interface IExecutionResult {
  output: string;
  error: string | null;
  executionTime: number;
  memoryUsage: number;
  status: string;
}

export interface IBatchExecutionResult {
  results: Array<
    IExecutionResult & {
      input: string;
      expectedOutput: string;
    }
  >;
}

export interface ITestResult {
  passed: boolean;
  input: string;
  expectedOutput: string;
  actualOutput: string;
  executionTime?: number;
  memoryUsage?: number;
  pointsEarned?: number;
  testCaseId?: string;
  isCustom?: boolean; // Flag to identify custom test cases
}

export interface ISubmissionResult {
  problemId: string;
  allTestsPassed: boolean;
  totalPoints: number;
  maxPoints: number;
  testResults: ITestResult[];
  hasSeenSolution?: boolean; // Flag to indicate if user has seen the solution
  solutions?: ICodingProblemSolution[];
}
