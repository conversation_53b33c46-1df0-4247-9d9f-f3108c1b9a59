import { Document, FilterQuery, Types } from 'mongoose';
import { IModule } from './module.interface';

export interface ISubModule extends Document {
  title: string;
  description: string;
  module: IModule | Types.ObjectId | string; // Reference to the module
  order: number;
  duration: number;
  points: number;
  isActive: boolean;
  totalPoints: number;
  // Section-based structure
  sections: {
    lesson: boolean; // Contains video, code, MCQ, code labs, notes
    practice: boolean; // Contains everything except video and notes
  };

  createdAt: Date;
  updatedAt: Date;
}

export interface ISubModuleDao {
  findAll(
    filter: FilterQuery<ISubModule>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number,
    populate?: string | string[]
  ): Promise<ISubModule[]>;

  findById(
    id: string,
    populate?: string | string[]
  ): Promise<ISubModule | null>;

  findOne(
    filter: FilterQuery<ISubModule>,
    sort?: { [key: string]: -1 | 1 },
    populate?: string | string[]
  ): Promise<ISubModule | null>;

  create(data: Partial<ISubModule>): Promise<ISubModule | null>;

  update(id: string, data: Partial<ISubModule>): Promise<ISubModule | null>;
}
