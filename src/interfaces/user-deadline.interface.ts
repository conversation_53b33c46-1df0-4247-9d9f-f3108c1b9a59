import mongoose, { Document, FilterQuery, Types } from 'mongoose';
export interface IUserDeadline extends Document {
  user: Types.ObjectId;
  batch: Types.ObjectId;
  submodule: Types.ObjectId;
  originalDeadline: Date; // Reference to the original batch deadline
  adjustedDeadline: Date; // Deadline after considering pauses
  adjustedStartDate: Date; // Start date after considering pauses
  pauseAdjustmentDays: number; // Total days added due to pauses
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUserDeadlineDao {
  findAll(
    filter: FilterQuery<IUserDeadline>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number
  ): Promise<IUserDeadline[]>;

  findById(id: string): Promise<IUserDeadline | null>;

  findOne(filter: FilterQuery<IUserDeadline>): Promise<IUserDeadline | null>;

  create(
    data: Partial<IUserDeadline>,
    session?: mongoose.ClientSession
  ): Promise<IUserDeadline>;

  update(
    id: string,
    data: Partial<IUserDeadline>
  ): Promise<IUserDeadline | null>;

  delete(id: string): Promise<IUserDeadline | null>;

  findByUserAndBatch(userId: string, batchId: string): Promise<IUserDeadline[]>;

  findByUserAndSubmodule(
    userId: string,
    submoduleId: string
  ): Promise<IUserDeadline | null>;

  adjustDeadline(
    userId: string | Types.ObjectId,
    batchId: string | Types.ObjectId,
    pauseDays: number,
    pauseStartDate: Date
  ): Promise<number | null>;

  adjustDeadlinesForEarlyResume(
    userId: string | Types.ObjectId,
    batchId: string | Types.ObjectId,
    daysToAdjustBack: number
  ): Promise<number | null>;
}
