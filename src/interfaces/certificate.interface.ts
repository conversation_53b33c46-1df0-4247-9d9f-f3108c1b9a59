import mongoose, { Document, FilterQuery, Types } from 'mongoose';

export interface ICertificate extends Document {
  user: Types.ObjectId;
  course: Types.ObjectId;
  batch: Types.ObjectId;
  type: 'progress' | 'excellence';
  certificateId: string;
  issueDate: Date;
  progress: number;
  certificateUrl: string;
  metadata: {
    userName: string;
    courseName: string;
    batchName: string;
    instructorName: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ICertificateDao {
  findAll(
    filter: FilterQuery<ICertificate>,
    sort?: { [key: string]: -1 | 1 },
    skip?: number,
    limit?: number
  ): Promise<ICertificate[]>;

  findById(id: string): Promise<ICertificate | null>;

  findByCertificateId(certificateId: string): Promise<ICertificate | null>;

  findOne(filter: FilterQuery<ICertificate>): Promise<ICertificate | null>;

  create(
    data: Partial<ICertificate>,
    session?: mongoose.ClientSession
  ): Promise<ICertificate>;

  update(id: string, data: Partial<ICertificate>): Promise<ICertificate | null>;

  delete(id: string): Promise<ICertificate | null>;

  findByUser(userId: string): Promise<ICertificate[]>;

  findByCourse(courseId: string): Promise<ICertificate[]>;
}
