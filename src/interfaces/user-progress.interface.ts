import mongoose, {
  FilterQuery,
  UpdateQuery,
  QueryOptions,
  Document,
  Types
} from 'mongoose';
export interface IUserProgress extends Document {
  user: Types.ObjectId;
  course: Types.ObjectId;
  batch: Types.ObjectId;
  overallProgress: number;
  totalPointsEarned: number;
  moduleProgress: {
    module: Types.ObjectId;
    progress: number;
    pointsEarned: number;
    completedAt?: Date;
    penaltyApplied: number;
  }[];
  subModuleProgress: {
    subModule: Types.ObjectId;
    progress: number;
    batch: Types.ObjectId;
    completed: boolean;
    pointsEarned: number;
    completedAt?: Date;
    penaltyApplied: number;
    daysLate: number;
  }[];
  contentItemProgress: {
    contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab';
    contentId: Types.ObjectId;
    contentModel: 'Video' | 'Note' | 'MCQ' | 'CodingProblem' | 'CodingLab';
    subModuleId: Types.ObjectId | string;
    completed: boolean;
    pointsEarned: number;
    completedAt?: Date;

    // For video content
    watchedDuration?: number;

    // For coding labs and problems
    submissions?: {
      code: string;
      language: string;
      submittedAt: Date;
      testCaseResults: {
        testCaseId: string;
        passed: boolean;
        output: string;
        pointsEarned: number;
      }[];
      totalPoints: number;
      originalPoints: number;
      penaltyApplied: number;
      daysLate: number;
    }[];

    // For MCQs
    selectedOptions?: string[];
    attempts?: number;

    // Common fields for penalty tracking
    originalPoints: number;
    penaltyApplied: number;
    daysLate: number;
  }[];
  lastActivityAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUserProgressDao {
  findAll(
    filter: FilterQuery<IUserProgress>,
    sort?: Record<string, 1 | -1>,
    skip?: number,
    limit?: number,
    populate?: string | string[]
  ): Promise<IUserProgress[]>;
  create(
    data: Partial<IUserProgress>,
    session?: mongoose.ClientSession
  ): Promise<IUserProgress>;
  findById(
    id: string | mongoose.Types.ObjectId,
    populate?: string | string[]
  ): Promise<IUserProgress | null>;
  update(
    id: string,
    data: UpdateQuery<IUserProgress>,
    session?: mongoose.ClientSession
  ): Promise<IUserProgress | null>;
  findByUserAndCourse(
    userId: string | mongoose.Types.ObjectId,
    courseId: string | mongoose.Types.ObjectId,
    populate?: string | string[]
  ): Promise<IUserProgress | null>;
  findOneAndUpdate(
    filter: FilterQuery<IUserProgress>,
    update: UpdateQuery<IUserProgress>,
    options?: QueryOptions,
    session?: mongoose.ClientSession
  ): Promise<IUserProgress | null>;
  findOne(
    filter: FilterQuery<IUserProgress>,
    populate?: string | string[]
  ): Promise<IUserProgress | null>;
}
