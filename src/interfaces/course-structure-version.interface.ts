import { Document, Types } from 'mongoose';

export interface ISubmoduleStructure {
  submoduleId: Types.ObjectId;
  moduleId: Types.ObjectId;
  moduleOrder: number;
  submoduleOrder: number;
  globalPosition: number;
  title: string;
  dependsOnPrevious: boolean;
}

export interface ICourseStructureVersion extends Document {
  course: Types.ObjectId;
  batch: Types.ObjectId;
  version: number;
  
  // Snapshot of course structure at this version
  structure: {
    modules: Array<{
      moduleId: Types.ObjectId;
      order: number;
      title: string;
      submodules: Array<{
        submoduleId: Types.ObjectId;
        order: number;
        title: string;
        globalPosition: number;
        dependsOnPrevious: boolean;
      }>;
    }>;
    totalSubmodules: number;
  };
  
  // Change metadata
  changeType: 'module_reorder' | 'submodule_reorder' | 'module_add' | 'module_remove' | 'submodule_add' | 'submodule_remove';
  changedBy: Types.ObjectId;
  changeReason?: string;
  changeDetails: {
    affectedModules?: Types.ObjectId[];
    affectedSubmodules?: Types.ObjectId[];
    previousPositions?: Array<{
      id: Types.ObjectId;
      type: 'module' | 'submodule';
      oldPosition: number;
      newPosition: number;
    }>;
  };
  
  // Audit trail
  createdAt: Date;
  isActive: boolean;
}

export interface IBatchDeadlineVersion extends Document {
  batch: Types.ObjectId;
  submodule: Types.ObjectId;
  version: number;
  
  // Snapshot of deadline configuration at this version
  deadlineConfig: {
    startDate: Date;
    deadline: Date;
    dependsOnPreviousSubmodule: boolean;
    penaltyRules: Array<{
      daysLate: number;
      penaltyPercentage: number;
    }>;
  };
  
  // Change metadata
  changeType: 'deadline_update' | 'penalty_rules_update' | 'dependency_update';
  changedBy: Types.ObjectId;
  changeReason?: string;
  previousDeadline?: Date;
  newDeadline?: Date;
  
  // Audit trail
  createdAt: Date;
  isActive: boolean;
}

export interface IUserAdjustmentVersion extends Document {
  user: Types.ObjectId;
  batch: Types.ObjectId;
  version: number;
  
  // Snapshot of user adjustments at this version
  adjustmentConfig: {
    totalPauseDays: number;
    pauseHistory: Array<{
      startDate: Date;
      endDate?: Date;
      daysUsed: number;
      status: 'active' | 'completed';
    }>;
    submoduleAdjustments: Array<{
      submodule: Types.ObjectId;
      additionalDays: number;
      customStartDate?: Date;
      customDeadline?: Date;
      isExempt: boolean;
      reason?: string;
    }>;
  };
  
  // Change metadata
  changeType: 'pause_applied' | 'pause_resumed' | 'custom_adjustment' | 'exemption_granted';
  changeReason?: string;
  
  // Audit trail
  createdAt: Date;
  isActive: boolean;
}
