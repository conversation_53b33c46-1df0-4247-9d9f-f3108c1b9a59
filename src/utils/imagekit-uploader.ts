import ImageKit from 'imagekit';
import { config } from '../config/config';
import fs from 'fs';
import path from 'path';

const imagekit = new ImageKit({
  publicKey: config.imagekit.publicKey,
  privateKey: config.imagekit.privateKey,
  urlEndpoint: config.imagekit.urlEndpoint
});

const MAX_RETRIES = 3;

// Define error interface for ImageKit errors
interface ImageKitError extends Error {
  message: string;
  [key: string]: unknown;
}

// uploading from file system
const uploadFile = async (
  localFilePath: string,
  fileName: string
): Promise<string | undefined> => {
  // Normalize and resolve the paths to mitigate path injection attacks
  localFilePath = path.resolve(path.normalize(localFilePath));
  fileName = path.basename(fileName);
  // check if the file exists
  if (!fs.existsSync(localFilePath)) {
    throw new Error('File does not exist');
  }
  // upload the file to imagekit
  let retryCount = 0;
  while (retryCount < MAX_RETRIES) {
    try {
      const responseUrl = await imagekit.upload({
        file: localFilePath,
        fileName: fileName
      });
      // remove the local file after uploading
      fs.unlink(localFilePath, err => {
        if (err) {
          throw err;
        }
      });
      return responseUrl.url;
    } catch (error) {
      retryCount++;
      if (retryCount === MAX_RETRIES) {
        // Use the ImageKitError interface
        const ikError = error as ImageKitError;
        throw new Error(ikError.message || 'Unknown upload error');
      }
      // wait before retrying
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
};

// uploading base64 encoded file
const uploadBase64File = async (
  base64File: string,
  fileName: string,
  folder?: string
): Promise<string | undefined> => {
  let retryCount = 0;
  while (retryCount < MAX_RETRIES) {
    try {
      const responseUrl = await imagekit.upload({
        file: base64File,
        fileName: fileName,
        folder: folder || ''
      });
      return responseUrl.url;
    } catch (error) {
      retryCount++;
      if (retryCount === MAX_RETRIES) {
        // Use the ImageKitError interface
        const ikError = error as ImageKitError;
        throw new Error(ikError.message || 'Unknown upload error');
      }
      // wait before retrying
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
};

// delete file from imagekit
const deleteFile = async (imageId: string): Promise<void> => {
  try {
    await imagekit.deleteFile(imageId);
  } catch (error) {
    // Use the ImageKitError interface
    const ikError = error as ImageKitError;
    throw new Error(ikError.message || 'Unknown delete error');
  }
};

export { uploadFile, uploadBase64File, deleteFile };
