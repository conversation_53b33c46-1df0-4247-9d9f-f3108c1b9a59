import submoduleContentDao from '../dao/submodule-content.dao';

export const reorderSubmoduleContentService = async (
  id: string,
  order: number
): Promise<{ success: boolean; message: string }> => {
  const targetSubmoduleContent = await submoduleContentDao.findById(id);
  if (!targetSubmoduleContent) {
    return { success: false, message: 'Submodule content not found' };
  }
  const submoduleContents = await submoduleContentDao.simulateSubModuleReorder(
    targetSubmoduleContent.submodule as string
  );
  if (!submoduleContents || !Array.isArray(submoduleContents)) {
    return {
      success: false,
      message: 'Submodules data is invalid or undefined'
    };
  }
  // Ensure the input is 1-based (user-facing)
  const totalSubmodules = submoduleContents.length;
  const targetIndex = Array.isArray(submoduleContents)
    ? submoduleContents.findIndex(m => m._id?.toString() === id)
    : -1;
  if (targetIndex === -1) {
    return {
      success: false,
      message: 'Target submodule not found in course modules'
    };
  }
  const from = targetIndex;
  const to = order - 1;
  if (to < 0 || to >= totalSubmodules) {
    return {
      success: false,
      message: 'Invalid target position for module reordering'
    };
  }
  if (from === to) {
    return { success: false, message: 'No change in module position detected' };
  }
  const [movedModule] = submoduleContents.splice(from, 1);
  submoduleContents.splice(to, 0, movedModule);

  // Assign new order values starting from 1
  const reordered = submoduleContents.map((mod, index) => ({
    _id: (mod._id as string).toString(),
    order: index + 1
  }));
  // Apply updates to DB
  await submoduleContentDao.bulkUpdateOrders(reordered);
  return { success: true, message: 'Module order updated successfully' };
};
