import mongoose from 'mongoose';
import SubmoduleContent from '../models/submodule-content.model';
import Video from '../models/video.model';
import Note from '../models/note.model';
import MCQ from '../models/mcq.model';
import CodingProblem from '../models/coding-problem.model';
import CodingLab from '../models/coding-lab.model';
import submoduleContentDao from '../dao/submodule-content.dao';
import { submodulePointsRefactor } from './submodule.utils';
// import { submodulePointsRefactor } from './submodule.utils';

/**
 * Get all content for a submodule, optionally filtered by section
 */
export async function getSubmoduleContent(
  submoduleId: string,
  section?: 'lesson' | 'practice'
) {
  try {
    // Create query object
    const query: Record<string, unknown> = {
      submodule: new mongoose.Types.ObjectId(submoduleId),
      isActive: true
    };

    // Add section filter if provided
    if (section) {
      query.section = section;
    }

    // Get all content references for this submodule
    const contentRefs = await SubmoduleContent.find(query)
      .sort({ order: 1 })
      .lean();

    // Define a generic type for content items with order
    type ContentItem = Record<string, unknown> & { order: number };

    // Group content references by type and section
    const contentBySection: {
      lesson: {
        videos: ContentItem[];
        notes: ContentItem[];
        mcqs: ContentItem[];
        codingProblems: ContentItem[];
        codingLabs: ContentItem[];
      };
      practice: {
        mcqs: ContentItem[];
        codingProblems: ContentItem[];
        codingLabs: ContentItem[];
      };
    } = {
      lesson: {
        videos: [],
        notes: [],
        mcqs: [],
        codingProblems: [],
        codingLabs: []
      },
      practice: {
        mcqs: [],
        codingProblems: [],
        codingLabs: []
      }
    };

    // Populate content items
    for (const ref of contentRefs) {
      let contentItem;
      const sectionKey = ref.section;

      switch (ref.contentType) {
        case 'video':
          contentItem = await Video.findById(ref.contentId).lean();
          if (contentItem && sectionKey === 'lesson') {
            contentBySection.lesson.videos.push({
              ...contentItem,
              order: ref.order
            });
          }
          break;
        case 'note':
          contentItem = await Note.findById(ref.contentId).lean();
          if (contentItem && sectionKey === 'lesson') {
            contentBySection.lesson.notes.push({
              ...contentItem,
              order: ref.order
            });
          }
          break;
        case 'mcq':
          contentItem = await MCQ.findById(ref.contentId).lean();
          if (contentItem) {
            contentBySection[sectionKey].mcqs.push({
              ...contentItem,
              order: ref.order
            });
          }
          break;
        case 'coding_problem':
          contentItem = await CodingProblem.findById(ref.contentId).lean();
          if (contentItem) {
            contentBySection[sectionKey].codingProblems.push({
              ...contentItem,
              order: ref.order
            });
          }
          break;
        case 'coding_lab':
          contentItem = await CodingLab.findById(ref.contentId).lean();
          if (contentItem) {
            contentBySection[sectionKey].codingLabs.push({
              ...contentItem,
              order: ref.order
            });
          }
          break;
      }
    }

    return contentBySection;
  } catch (error) {
    console.error('Error getting submodule content:', error);
    throw error;
  }
}

/**
 * Add content to a submodule
 */
export async function addContentToSubmodule(
  submoduleId: string,
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
  contentId: string,
  section: 'lesson' | 'practice',
  order: number
) {
  try {
    // Validate that the content exists
    let contentExists = false;

    switch (contentType) {
      case 'video':
        contentExists = !!(await Video.findById(contentId));
        break;
      case 'note':
        contentExists = !!(await Note.findById(contentId));
        break;
      case 'mcq':
        contentExists = !!(await MCQ.findById(contentId));
        break;
      case 'coding_problem':
        contentExists = !!(await CodingProblem.findById(contentId));
        break;
      case 'coding_lab':
        contentExists = !!(await CodingLab.findById(contentId));
        break;
    }

    if (!contentExists) {
      throw new Error(
        `Content of type ${contentType} with ID ${contentId} not found`
      );
    }

    // Create the submodule content reference
    const submoduleContent = new SubmoduleContent({
      submodule: new mongoose.Types.ObjectId(submoduleId),
      contentType,
      contentId: new mongoose.Types.ObjectId(contentId),
      section,
      order,
      isActive: true
    });

    await submoduleContent.save();
    return submoduleContent;
  } catch (error) {
    console.error('Error adding content to submodule:', error);
    throw error;
  }
}

/**
 * Remove content from a submodule
 */
export async function removeContentFromSubmodule(
  submoduleId: string,
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
  contentId: string
) {
  try {
    const result = await SubmoduleContent.deleteOne({
      submodule: new mongoose.Types.ObjectId(submoduleId),
      contentType,
      contentId: new mongoose.Types.ObjectId(contentId)
    });

    return result.deletedCount > 0;
  } catch (error) {
    console.error('Error removing content from submodule:', error);
    throw error;
  }
}

/**
 * Find all submodules that use a specific content item
 */
export async function findSubmodulesUsingContent(
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
  contentId: string
) {
  try {
    const contentRefs = await submoduleContentDao.findAll({
      contentType,
      contentId: new mongoose.Types.ObjectId(contentId),
      isActive: true
    });

    const submoduleIds = contentRefs.map(ref => ref.submodule);
    return submoduleIds;
  } catch (error) {
    console.error('Error finding submodules using content:', error);
    throw error;
  }
}

/**
 * Check if a content item is attached to any submodule
 */
export const canDeleteContent = async (
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
  contentId: string
): Promise<{ attached: boolean; canDelete: boolean }> => {
  try {
    const isAttached = await submoduleContentDao.findOne({
      contentType: contentType,
      contentId: new mongoose.Types.ObjectId(contentId),
      isActive: true
    });

    return {
      attached: !!isAttached,
      canDelete: !isAttached
    };
  } catch (error) {
    console.error('Error checking if content can be deleted:', error);
    throw error;
  }
};

export const updatePoints = async (
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
  contentId: string,
  points: number,
  previousPoints: number
): Promise<{ success: boolean; message: string }> => {
  try {
    // Calculate the point difference
    const pointsDifference = points - previousPoints;

    // If there's no difference, no need to update
    if (pointsDifference === 0) {
      return { success: true, message: 'No points change detected' };
    }

    // Find all submodules using this content
    const submodules = await findSubmodulesUsingContent(contentType, contentId);

    if (submodules.length === 0) {
      return {
        success: true,
        message: 'No submodules found using this content'
      };
    }
    // Update points in each submodule
    for (const submoduleId of submodules) {
      await submodulePointsRefactor(submoduleId as string, pointsDifference);
    }

    return { success: true, message: 'Points updated successfully' };
  } catch (error) {
    console.error('Error updating points:', error);

    // Use a consistent error handler or throw a custom error
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error occurred';
    return {
      success: false,
      message: `Error updating points: ${errorMessage}`
    };
  }
};
