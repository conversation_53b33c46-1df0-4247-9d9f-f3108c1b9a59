import PDFDocument from 'pdfkit';
import fs from 'fs';
import path from 'path';
import moment from 'moment';
import ImageKit from 'imagekit';
import { config } from '../config/config';
// import { config } from '../config/config'; // Assuming config is imported from a config file
// Initialize ImageKit
const imagekit = new ImageKit({
  publicKey: config.imagekit.publicKey,
  privateKey: config.imagekit.privateKey,
  urlEndpoint: config.imagekit.urlEndpoint
});

interface CertificateData {
  userName: string;
  courseName: string;
  certificateId: string;
  batchName: string;
  instructorName: string;
  certificateUrl: string;
  type: string;
}

// Paths for static assets
const logoPath = path.join(
  process.cwd(),
  'src',
  'assets',
  'images',
  'sheryiansLogo.png'
);
const signaturePath = path.join(
  process.cwd(),
  'src',
  'assets',
  'images',
  'signature.png'
);

/**
 * Uploads a file to ImageKit
 */
async function uploadToImageKit(
  filePath: string,
  fileName: string
): Promise<string> {
  try {
    const fileBuffer = fs.readFileSync(filePath);

    const response = await imagekit.upload({
      file: fileBuffer,
      fileName: fileName,
      folder: '/certificates'
    });

    return response.url;
  } catch (error) {
    console.error('Error uploading to ImageKit:', error);
    throw new Error(
      `ImageKit upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Generates a PDF certificate, saves it locally in a temp folder, and uploads to ImageKit.
 */
export async function generateCertificate(
  certificateData: CertificateData
): Promise<{ filePath: string; certificateId: string; cloudUrl: string }> {
  const certificateDate = moment().format('MMMM DD, YYYY');
  const tempDir = path.join(__dirname, '../temp');
  const filePath = path.join(tempDir, `${certificateData.certificateId}.pdf`);

  try {
    // Ensure temp directory exists
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const doc = new PDFDocument({
      layout: 'landscape',
      size: 'A4',
      margin: 50
    });
    const stream = fs.createWriteStream(filePath);
    doc.pipe(stream);

    // Add contents
    doc.image(logoPath, doc.page.margins.left + 50, doc.page.margins.top + 15, {
      width: 70
    });

    doc
      .font('Helvetica-Bold')
      .fontSize(30)
      .text(`CERTIFICATE OF ${certificateData.type}`, 50, 100, {
        align: 'center',
        width: doc.page.width - 100
      });

    doc
      .rect(50, 50, doc.page.width - 100, doc.page.height - 100)
      .lineWidth(2)
      .stroke();

    doc
      .font('Helvetica')
      .fontSize(16)
      .text('This certificate is proudly presented to:', 50, 160, {
        align: 'center',
        width: doc.page.width - 100
      });

    doc
      .font('Helvetica-Bold')
      .fontSize(26)
      .text(certificateData.userName, 50, 190, {
        align: 'center',
        width: doc.page.width - 100
      });

    doc
      .font('Helvetica')
      .fontSize(16)
      .text('for successfully completing the course', 50, 240, {
        align: 'center',
        width: doc.page.width - 100
      });

    doc
      .font('Helvetica-Bold')
      .fontSize(20)
      .text(certificateData.courseName, 50, 270, {
        align: 'center',
        width: doc.page.width - 100
      });

    doc
      .font('Helvetica')
      .fontSize(14)
      .text(`Batch: ${certificateData.batchName}`, 50, 310, {
        align: 'center',
        width: doc.page.width - 100
      });

    doc
      .font('Helvetica')
      .fontSize(12)
      .text(`Date: ${certificateDate}`, 100, 380);
    doc
      .fontSize(10)
      .text(`Certificate ID: ${certificateData.certificateId}`, 100, 400);
    doc
      .fontSize(10)
      .text(`Verify at: ${certificateData.certificateUrl}`, 100, 415);

    const signatureX = doc.page.width - 240;
    doc.image(signaturePath, signatureX, 360, { width: 100 });
    doc
      .moveTo(signatureX, 420)
      .lineTo(signatureX + 140, 420)
      .stroke();
    doc
      .font('Helvetica')
      .fontSize(12)
      .text(certificateData.instructorName, signatureX, 425, {
        width: 140,
        align: 'center'
      });

    doc.fontSize(10).text('Instructor', signatureX, 440, {
      width: 140,
      align: 'center'
    });

    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .text('Sheryians Coding School', 50, 500, {
        align: 'center',
        width: doc.page.width - 100
      });

    doc
      .font('Helvetica')
      .fontSize(10)
      .text(
        '23-B, Sector-C, Indrapuri, Bhopal, Madhya Pradesh - 462022',
        50,
        520,
        {
          align: 'center',
          width: doc.page.width - 100
        }
      );

    // Finalize PDF
    doc.end();

    // Wait for stream to finish writing
    await new Promise<void>((resolve, reject) => {
      stream.on('finish', resolve);
      stream.on('error', reject);
    });

    // Upload the generated PDF to ImageKit
    const fileName = `${certificateData.certificateId}.pdf`;
    const cloudUrl = await uploadToImageKit(filePath, fileName);

    return {
      filePath,
      certificateId: certificateData.certificateId,
      cloudUrl
    };
  } catch (error) {
    console.error('Error generating certificate:', error);
    throw new Error(
      `Certificate generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
