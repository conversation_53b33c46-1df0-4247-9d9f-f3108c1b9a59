import { Redis } from 'ioredis';
import { config } from '../config/config';
import logger from '../config/logger';

const redisClient = () => {
  if (config.redisUrl) {
    const client = new Redis(config.redisUrl, {
      maxRetriesPerRequest: null
    });

    client.on('connect', () => {
      logger.warn('Connected to Redis');
    });

    client.on('error', err => {
      logger.error('Error connecting to Redis:', { error: err });
    });

    return client;
  }
  throw new Error('Redis URL is not defined');
};

export const redis = redisClient();
