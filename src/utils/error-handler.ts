import { config } from '../config/config';

class ErrorHandler extends Error {
  statusCode: number;
  data?: object | null | undefined | string;
  message: string;
  success: boolean;
  errors: string[];
  constructor(
    statusCode: number,
    message = `Oops! Something unexpected happened on our end. We're on it! Please try again in a few minutes.`,
    errors: string[] = [],
    stack = ''
  ) {
    super(message);
    this.statusCode = statusCode;
    this.data = null;
    this.message = message;
    this.success = false;
    this.errors = errors;

    if (config.environment !== 'production') {
      if (stack) {
        this.stack = stack;
      } else {
        Error.captureStackTrace(this, this.constructor);
      }
    }
  }
}

export default ErrorHandler;
