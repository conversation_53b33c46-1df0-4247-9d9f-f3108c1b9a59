import mongoose from 'mongoose';
import submoduleDao from '../dao/submodule.dao';
import batchSubmoduleDeadlineDao from '../dao/batch-submodule-deadline.dao';
import <PERSON>rror<PERSON>andler from './error-handler';

import moduleDao from '../dao/module.dao';
import moment from 'moment';
import { ISubModule } from '../interfaces/submodule.interface';
import { IBatchSubmoduleDeadline } from '../interfaces/batch-submodule-deadline.interface';
// import batchSubmoduleDeadlineDao from "../dao/batch-submodule-deadline.dao";

async function getStartDateForFirstSubmodule(
  subModuleDetails: ISubModule,
  batchId: string | mongoose.Types.ObjectId
): Promise<Date> {
  // Find the module of the current submodule
  const module = await moduleDao.findById(subModuleDetails.module as string);
  if (!module) {
    // fallback to current date if module not found
    return new Date();
  }

  // Find the previous module (order - 1)
  const previousModule = await moduleDao.findOne({
    course: module.course,
    order: module.order - 1
  });

  if (previousModule) {
    // Find the last submodule in the previous module
    const lastSubmodule = await submoduleDao.findOne(
      { module: previousModule._id },
      { order: -1 }
    );

    if (lastSubmodule) {
      // Get the deadline of the last submodule in the previous module
      const prevDeadline = await batchSubmoduleDeadlineDao.findOne({
        batch: batchId,
        submodule: lastSubmodule._id
      });
      if (prevDeadline && prevDeadline.deadline) {
        return new Date(prevDeadline.deadline);
      }
    }
  }

  // If no previous module or no deadline found, fallback to current date
  return new Date();
}

export const subModuleDeadlineInitializer = async (
  subModule: string | mongoose.Types.ObjectId,
  startDate: Date,
  dependsOnPreviousSubmodule: boolean,
  batch: string | mongoose.Types.ObjectId
): Promise<{ success: boolean; message: string; data: Date | null }> => {
  // check if the submodule is first in the module
  // if it is the first submodule, set the deadline of this submodule to batch start date
  // get the previous submodule
  // if it is not the first submodule, set the deadline to the previous submodule's deadline endDate + the duration of the current submodule

  // get the submodule from the database
  const subModuleDetails = await submoduleDao.findById(subModule);
  if (!subModuleDetails) {
    return {
      success: false,
      message: 'Submodule not found',
      data: null
    };
  }

  if (dependsOnPreviousSubmodule) {
    const previousModuleDeadlineDetails =
      await getPreviousSubmoduleDeadlineDate(
        subModule,
        subModuleDetails.order,
        batch
      );
    if (!previousModuleDeadlineDetails) {
      return {
        success: false,
        message: 'Previous submodule deadline not found',
        data: null
      };
    }

    return {
      success: true,
      message: 'Submodule deadline initialized successfully',
      data: previousModuleDeadlineDetails
    };
  }

  return {
    success: true,
    message: 'Submodule deadline initialized successfully',
    data: new Date(startDate)
  };
};

export const getPreviousSubmoduleDeadlineDate = async (
  subModule: string | mongoose.Types.ObjectId,
  order: number,
  batch: string | mongoose.Types.ObjectId
): Promise<Date | null> => {
  // get the submodule from the database
  const subModuleDetails = await submoduleDao.findById(subModule);
  if (!subModuleDetails) {
    return null;
  }

  // get the previous submodule
  const previousSubmodule = await submoduleDao.findOne({
    module: subModuleDetails.module,
    order: order - 1
  });

  if (!previousSubmodule) {
    return null;
  }
  const subModuleDeadline = await batchSubmoduleDeadlineDao.findOne({
    batch: batch,
    submodule: previousSubmodule._id
  });
  if (!subModuleDeadline) {
    throw new ErrorHandler(400, 'Previous Submodule deadline not found');
  }
  return subModuleDeadline.deadline;
};

/**
 * Helper function to initialize a missing submodule deadline
 */
export const initializeSubmoduleDeadline = async (
  batchId: string | mongoose.Types.ObjectId,
  submoduleId: string | mongoose.Types.ObjectId,
  startDate?: Date,
  days: number = 10,
  penaltyRules: {
    daysLate: number;
    penaltyPercentage: number;
  }[] = []
): Promise<{ startDate: Date; deadline: Date }> => {
  // Get the submodule details
  const subModuleDetails = await submoduleDao.findById(submoduleId);
  if (!subModuleDetails) {
    throw new Error('Submodule not found');
  }

  let finalStartDate: Date;

  // If no startDate is provided, try to get it from the previous submodule
  if (!startDate) {
    const previousSubmodule = await submoduleDao.findOne({
      module: subModuleDetails.module,
      order: subModuleDetails.order - 1
    });

    if (previousSubmodule) {
      const prevDeadline = await getPreviousSubmoduleDeadlineDate(
        previousSubmodule._id as string,
        previousSubmodule.order,
        batchId
      );
      if (!prevDeadline) {
        throw new Error('Previous submodule deadline not found');
      }
      finalStartDate = prevDeadline;
    } else {
      finalStartDate = await getStartDateForFirstSubmodule(
        subModuleDetails,
        batchId
      );
    }
  } else {
    finalStartDate = startDate;
  }
  // Calculate deadline
  const deadline = moment(finalStartDate).add(days, 'days').toDate();

  // Save the deadline in the database
  await batchSubmoduleDeadlineDao.findOneAndUpdate(
    { batch: batchId, submodule: submoduleId },
    {
      startDate: finalStartDate,
      deadline: deadline,
      days: days,
      penaltyRules: penaltyRules
    },
    { upsert: true, new: true }
  );

  // Process next submodule in cascading fashion
  await initializeNextSubmoduleAcrossModules(
    batchId,
    subModuleDetails,
    deadline,
    days
  );

  return { startDate: finalStartDate, deadline };
};

/**
 * Helper function to find and initialize the next submodule, even across module boundaries
 */
async function initializeNextSubmoduleAcrossModules(
  batchId: string | mongoose.Types.ObjectId,
  currentSubmodule: ISubModule,
  startDate: Date,
  days: number,
  penaltyRules: {
    daysLate: number;
    penaltyPercentage: number;
  }[] = []
): Promise<void> {
  // First try to find the next submodule in the same module
  const nextSubmodule = await submoduleDao.findOne({
    module: currentSubmodule.module,
    order: currentSubmodule.order + 1
  });

  if (nextSubmodule) {
    // Check if next submodule already has a deadline
    const nextDeadlineExists = await batchSubmoduleDeadlineDao.findOne({
      batch: batchId,
      submodule: nextSubmodule._id
    });

    // If next submodule doesn't have a deadline yet, initialize it
    if (!nextDeadlineExists) {
      await initializeSubmoduleDeadline(
        batchId,
        nextSubmodule._id as string,
        startDate,
        days
      );
    }
    return;
  }

  // If no next submodule in the same module, try to find the first submodule of the next module
  const moduleDetails = await moduleDao.findById(
    currentSubmodule.module as string
  );
  if (moduleDetails) {
    const nextModule = await moduleDao.findOne({
      course: moduleDetails.course,
      order: moduleDetails.order + 1
    });

    if (nextModule) {
      // Find the first submodule of the next module
      const firstSubmoduleOfNextModule = await submoduleDao.findOne(
        { module: nextModule._id },
        { order: 1 }
      );

      if (firstSubmoduleOfNextModule) {
        // Check if it already has a deadline
        const nextModuleFirstDeadlineExists =
          await batchSubmoduleDeadlineDao.findOne({
            batch: batchId,
            submodule: firstSubmoduleOfNextModule._id
          });

        // If it doesn't have a deadline yet, initialize it
        if (!nextModuleFirstDeadlineExists) {
          await initializeSubmoduleDeadline(
            batchId,
            firstSubmoduleOfNextModule._id as string,
            startDate,
            days,
            penaltyRules
          );
        }
      }
    }
  }
}

export const shiftSubmoduleDates = async (
  batchId: string,
  courseId: string,
  submoduleId: string,
  newStartDate: string | Date,
  days: number,
  userId: string,
  penaltyRules: {
    daysLate: number;
    penaltyPercentage: number;
  }[]
): Promise<void> => {
  // Validate ObjectId
  const batchObjectId = new mongoose.Types.ObjectId(batchId);
  const submoduleObjectId = new mongoose.Types.ObjectId(submoduleId);
  const courseObjectId = new mongoose.Types.ObjectId(courseId);

  // Get the current submodule deadline
  const current = await batchSubmoduleDeadlineDao.findOne({
    batch: batchObjectId,
    submodule: submoduleObjectId
  });

  // Initialize if not found instead of throwing error
  if (!current) {
    await initializeSubmoduleDeadline(
      batchObjectId,
      submoduleObjectId,
      new Date(newStartDate),
      days
    );
  }

  // Get all modules with submodules and their deadlines
  const allModules = await moduleDao.getModulesWithSubmodulesAndDeadlines(
    courseObjectId.toString(),
    batchObjectId.toString(),
    userId.toString()
  );

  let found = false;
  let currentDate = moment(newStartDate);
  let lastDeadline: moment.Moment | null = null;
  for (const module of allModules.sort((a, b) => a.order - b.order)) {
    for (const sm of module.submodules.sort((a, b) => a.order - b.order)) {
      const smId = (sm._id as mongoose.Types.ObjectId | string).toString();

      if (smId === submoduleId) {
        found = true;

        const startDate = currentDate.toDate();
        const deadline = moment(currentDate).add(days, 'days').toDate();

        if (lastDeadline && currentDate.isBefore(lastDeadline)) {
          throw new Error(
            `Submodule ${smId} startDate overlaps with previous submodule's deadline.`
          );
        }

        await batchSubmoduleDeadlineDao.findBySubmoduleAndUpdate(smId, {
          startDate,
          deadline,
          penaltyRules
        });

        lastDeadline = moment(deadline);
        currentDate = moment(deadline);
      } else if (found && sm.deadlineInfo) {
        const duration: number =
          sm.deadlineInfo &&
          typeof sm.deadlineInfo === 'object' &&
          'days' in sm.deadlineInfo
            ? (sm.deadlineInfo as { days: number }).days
            : (sm.duration ?? 7);

        const startDate = currentDate.toDate();
        const deadline: Date = moment(currentDate)
          .add(duration, 'days')
          .toDate();

        if (lastDeadline && currentDate.isBefore(lastDeadline)) {
          throw new Error(
            `Submodule ${smId} startDate overlaps with previous submodule's deadline.`
          );
        }

        await batchSubmoduleDeadlineDao.findBySubmoduleAndUpdate(
          sm._id.toString(),
          {
            startDate,
            deadline,
            penaltyRules
          }
        );

        lastDeadline = moment(deadline);
        currentDate = moment(deadline);
      }
    }
  }
};

/**
 * Updates the duration for all submodules after a target one (inclusive), and recalculates their deadlines.
 */
// export const updateSubmoduleDurations = async (
//   batchId: string,
//   courseId: string,
//   fromSubmoduleId: string,
//   newDuration: number
// ) => {
//   let current = await batchSubmoduleDeadlineDao.findOne({
//     batch: batchId,
//     submodule: fromSubmoduleId
//   });

//   // Initialize if not found instead of throwing error
//   if (!current) {
//     await initializeSubmoduleDeadline(
//       batchId,
//       fromSubmoduleId,
//       undefined,
//       newDuration
//     );
//     // Refetch after initialization
//     const initialized = await batchSubmoduleDeadlineDao.findOne({
//       batch: batchId,
//       submodule: fromSubmoduleId
//     });
//     if (!initialized)
//       throw new Error('Failed to initialize submodule deadline');
//     current = initialized;
//   }

//   const allModules = await moduleDao.getModulesWithSubmodulesAndDeadlines(
//     courseId,
//     batchId,
//     userId
//   );

//   let found = false;
//   let currentDate = moment(current.startDate);

//   for (const module of allModules.sort((a, b) => a.order - b.order)) {
//     for (const sm of module.submodules.sort((a, b) => a.order - b.order)) {
//       if (String(sm._id) === fromSubmoduleId) found = true;

//       if (found) {
//         const duration = newDuration;
//         const start = currentDate.clone();
//         const deadline = start.clone().add(duration, 'days');

//         await batchSubmoduleDeadlineDao.update(sm._id, {
//           startDate: start.toDate(),
//           deadline: deadline.toDate(),
//           days: duration
//         });

//         currentDate = deadline;
//       }
//     }
//   }
// };

export const applyUniformDurationFromPoint = async (
  batchId: string,
  courseId: string,
  submoduleId: string,
  duration: number,
  userId: string,
  penaltyRules: {
    daysLate: number;
    penaltyPercentage: number;
  }[]
) => {
  // Ensure the first submodule's deadline is initialized if missing
  const firstDeadline = await batchSubmoduleDeadlineDao.findOne({
    batch: batchId,
    submodule: submoduleId
  });
  if (!firstDeadline) {
    // Use today as the start date if not provided
    await initializeSubmoduleDeadline(
      batchId,
      submoduleId,
      new Date(),
      duration
    );
  }

  const allModules = await moduleDao.getModulesWithSubmodulesAndDeadlines(
    courseId,
    batchId,
    userId
  );

  let found = false;
  let currentDate: moment.Moment | null = null;
  let lastDeadline: moment.Moment | null = null;

  for (const module of allModules.sort((a, b) => a.order - b.order)) {
    for (const sm of module.submodules.sort((a, b) => a.order - b.order)) {
      const smId = sm._id.toString();

      // Fetch deadline info from batchSubmoduleDeadlineDao
      const deadlineInfo = await batchSubmoduleDeadlineDao.findOne({
        batch: batchId,
        submodule: smId
      });

      if (smId === submoduleId) {
        found = true;
        if (!deadlineInfo || !deadlineInfo.startDate) {
          throw new Error(`Submodule ${smId} has no valid start date`);
        }
        currentDate = moment(deadlineInfo.startDate);
      }

      if (found) {
        if (lastDeadline && currentDate!.isBefore(lastDeadline)) {
          throw new Error(
            `Submodule ${smId} startDate overlaps with previous submodule's deadline.`
          );
        }

        const startDate = currentDate!.toDate();
        const deadline: Date = moment(currentDate)
          .add(duration, 'days')
          .toDate();

        await batchSubmoduleDeadlineDao.findBySubmoduleAndUpdate(smId, {
          startDate,
          deadline,
          days: duration,
          penaltyRules
        });

        lastDeadline = moment(deadline);
        currentDate = moment(deadline);
      }
    }
  }
};

export const getOrderedSubmodulesFromPoint = async (
  courseId: string | mongoose.Types.ObjectId
): Promise<ISubModule[]> => {
  // Step 1: Get all modules of the course in order
  const modules = await moduleDao.findAll(
    { course: new mongoose.Types.ObjectId(courseId) },
    { order: 1 }
  );

  if (!modules.length) {
    throw new Error('No modules found for the course');
  }

  const moduleIds = modules.map(m => m._id);
  // Step 2: Get all submodules of these modules
  const submodules = await submoduleDao.findAll(
    { module: { $in: moduleIds } },
    { order: 1 },
    0,
    1000
  );

  if (!submodules.length) {
    throw new Error('No submodules found for course modules');
  }
  // Step 3: Create a flat list of submodules in module-order + submodule-order
  // Ensure both module._id and sm.module are compared as strings
  const orderedSubmodules = modules.flatMap(module =>
    submodules
      .filter(sm => {
        // Compare ObjectIds as strings to ensure equality
        return String(sm.module as string) === String(module._id);
      })
      .sort((a, b) => a.order - b.order)
  );
  return orderedSubmodules;
};

export const getOrInitializeDeadlinesForSubmodules = async (
  batchId: string,
  orderedSubmodules: ISubModule[]
): Promise<IBatchSubmoduleDeadline[]> => {
  const submoduleIds = orderedSubmodules.map(sm => sm._id);

  // Step 1: Fetch all existing deadlines
  const existingDeadlines = await batchSubmoduleDeadlineDao.findAll(
    {
      batch: new mongoose.Types.ObjectId(batchId),
      submodule: { $in: submoduleIds }
    },
    { startDate: 1 },
    0,
    1000
  );

  const deadlineMap = new Map(
    existingDeadlines.map(entry => [entry.submodule.toString(), entry])
  );

  const result: IBatchSubmoduleDeadline[] = [];

  for (let i = 0; i < orderedSubmodules.length; i++) {
    const sm = orderedSubmodules[i];
    const smId = String(sm._id);

    if (deadlineMap.has(smId)) {
      result.push(deadlineMap.get(smId)!);
    } else {
      // Initialize deadline based on previous submodule
      let startDate: Date;
      if (i === 0) {
        // If it's the first submodule, use current date as fallback
        startDate = moment().startOf('day').toDate();
      } else {
        const prev = result[i - 1];
        startDate = moment(prev.deadline).toDate();
      }

      const deadline = moment(startDate)
        .add(sm.duration || 7, 'days')
        .toDate();

      const newDeadline = await batchSubmoduleDeadlineDao.create({
        batch: new mongoose.Types.ObjectId(batchId),
        submodule: new mongoose.Types.ObjectId(smId),
        startDate,
        deadline,
        isActive: true,
        dependsOnPreviousSubmodule: true
      });

      result.push(newDeadline);
    }
  }

  return result;
};
