import { Types, Document } from 'mongoose';
import BatchSubmoduleDeadline from '../models/batch-submodule-deadline.model';
import SubModule from '../models/submodule.model';
import UserProgress from '../models/user-progress.model';
import UserDeadline from '../models/user-deadline.model';
import { redis } from './redis';
import { getOrderedSubmodulesFromPoint } from './submodule-deadline.util';
import batchPauseDao from '../dao/batch-pause.dao';

// Cache TTL in seconds (5 minutes)
const CACHE_TTL = 300;

// Type for unlock result
interface UnlockResult {
  unlocked: boolean;
  reason?: string;
}

// Define proper types for Mongoose documents
interface ISubModuleDocument extends Document {
  _id: Types.ObjectId;
  module: Types.ObjectId;
  order: number;
  contents?: Types.ObjectId[];
  assessments?: Types.ObjectId[];
  [key: string]: unknown;
}

interface IBatchSubmoduleDeadlineDocument extends Document {
  _id: Types.ObjectId;
  batch: Types.ObjectId;
  submodule: Types.ObjectId;
  startDate: Date;
  deadline: Date;
  dependsOnPreviousSubmodule: boolean;
  isActive: boolean;
}

interface IUserDeadlineDocument extends Document {
  _id: Types.ObjectId;
  user: Types.ObjectId;
  batch: Types.ObjectId;
  submodule: Types.ObjectId;
  originalDeadline: Date;
  adjustedDeadline: Date;
  adjustedStartDate: Date;
  pauseAdjustmentDays: number;
  isActive: boolean;
}

// Helper function to safely convert ObjectId to string
function safeToString(id: unknown): string {
  if (id instanceof Types.ObjectId) {
    return id.toString();
  }
  if (typeof id === 'object' && id !== null && '_id' in id) {
    return safeToString((id as { _id: unknown })._id);
  }
  if (id === null || id === undefined) {
    return '';
  }
  // Handle other types safely
  if (typeof id === 'string') {
    return id;
  }
  if (typeof id === 'number' || typeof id === 'boolean') {
    return id.toString();
  }
  // For other object types, use a safe default
  return `id_${Object.prototype.toString.call(id)}`;
}

// Helper function to create cache key
function createCacheKey(
  userId: Types.ObjectId,
  batchId: Types.ObjectId,
  submoduleId: Types.ObjectId
): string {
  return `unlock:${safeToString(userId)}:${safeToString(batchId)}:${safeToString(submoduleId)}`;
}

// Helper function to create completion cache key
function createCompletionCacheKey(
  userId: Types.ObjectId,
  batchId: Types.ObjectId,
  submoduleId: Types.ObjectId
): string {
  return `completion:${safeToString(userId)}:${safeToString(batchId)}:${safeToString(submoduleId)}`;
}

// Helper function to safely parse JSON
function safeJsonParse<T>(json: string | null, defaultValue: T): T {
  if (!json) return defaultValue;
  try {
    return JSON.parse(json) as T;
  } catch (e) {
    console.error('Error parsing JSON:', e);
    return defaultValue;
  }
}

/**
 * Utility function to check if a submodule is unlocked for a user
 * A submodule is unlocked if:
 * 1. The current date is after the start date for the batch-submodule
 * 2. If dependsOnPreviousSubmodule is true, the previous submodule must be completed
 */
export async function isSubmoduleUnlocked(
  userId: Types.ObjectId,
  batchId: Types.ObjectId,
  courseId: Types.ObjectId,
  submoduleId: Types.ObjectId
): Promise<UnlockResult> {
  try {
    const cacheKey = createCacheKey(userId, batchId, submoduleId);

    const moduleArrangedList = await getOrderedSubmodulesFromPoint(courseId);

    const cachedResult = await redis.get(cacheKey);
    const defaultResult: UnlockResult = {
      unlocked: false,
      reason: 'Unknown error'
    };
    if (cachedResult) {
      return safeJsonParse<UnlockResult>(cachedResult, defaultResult);
    }

    const [userDeadline, submodule, batchSubmoduleDeadline, userActivePauses] =
      await Promise.all([
        UserDeadline.findOne({
          user: userId,
          batch: batchId,
          submodule: submoduleId,
          isActive: true
        }).lean<IUserDeadlineDocument>(),
        SubModule.findById(submoduleId).lean<ISubModuleDocument>(),
        BatchSubmoduleDeadline.findOne({
          batch: batchId,
          submodule: submoduleId,
          isActive: true
        }).lean<IBatchSubmoduleDeadlineDocument>(),
        batchPauseDao.findOne({
          userId: userId,
          batchId: batchId,
          status: 'active'
        })
      ]);
    const currentDate = new Date();
    // console.warn(userDeadline, submodule, batchSubmoduleDeadline);
    if (userActivePauses) {
      return {
        unlocked: false,
        reason:
          'You have an active pause. Please resume to unlock the submodule.'
      };
    }

    if (!userDeadline && batchSubmoduleDeadline) {
      await UserDeadline.initializeUserDeadlines(userId, batchId);
    }

    if (!submodule) {
      const result: UnlockResult = {
        unlocked: false,
        reason: 'Submodule not found'
      };
      await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_TTL);
      return result;
    }

    const currentSubmoduleIndex = moduleArrangedList.findIndex(
      item => safeToString(item._id) === safeToString(submoduleId)
    );

    if (currentSubmoduleIndex === 0) {
      if (userDeadline) {
        if (
          userDeadline.adjustedStartDate &&
          currentDate < new Date(userDeadline.adjustedStartDate)
        ) {
          const result: UnlockResult = {
            unlocked: false,
            reason:
              'This submodule will be available from ' +
              new Date(userDeadline.adjustedStartDate).toLocaleDateString()
          };
          await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_TTL);
          return result;
        }
      }
      const result: UnlockResult = { unlocked: true };
      await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_TTL);
      return result;
    }

    // Get previous submodule ID for date and progress checks
    const previousSubmoduleId =
      moduleArrangedList[currentSubmoduleIndex - 1]?._id;
    const [userProgress] = await Promise.all([
      UserProgress.findOne(
        { user: userId, batch: batchId },
        { subModuleProgress: 1 }
      ).lean()
    ]);

    const prevProgress = userProgress?.subModuleProgress?.find(
      progress =>
        safeToString(progress.subModule) === safeToString(previousSubmoduleId)
    );

    const previousCompleted = prevProgress?.completed;
    // Determine the earliest available start date between userDeadline and batchSubmoduleDeadline
    let startDate: Date | undefined;
    if (userDeadline?.adjustedStartDate && batchSubmoduleDeadline?.startDate) {
      startDate =
        new Date(userDeadline.adjustedStartDate) <
        new Date(batchSubmoduleDeadline.startDate)
          ? userDeadline.adjustedStartDate
          : batchSubmoduleDeadline.startDate;
    } else if (userDeadline?.adjustedStartDate) {
      startDate = userDeadline.adjustedStartDate;
    } else if (batchSubmoduleDeadline?.startDate) {
      startDate = batchSubmoduleDeadline.startDate;
    } else {
      startDate = undefined;
    }

    // If user-specific start date exists and is in the future, block access

    const dateCondition = startDate && currentDate >= new Date(startDate);

    // ✅ New unlock condition: Either date has come OR previous is completed
    if (previousCompleted || dateCondition) {
      const result: UnlockResult = { unlocked: true };
      await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_TTL);
      return result;
    }
    if (
      userDeadline?.adjustedStartDate &&
      currentDate < new Date(userDeadline.adjustedStartDate)
    ) {
      const result: UnlockResult = {
        unlocked: false,
        reason:
          'This submodule will be available from ' +
          new Date(userDeadline.adjustedStartDate).toLocaleDateString()
      };
      await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_TTL);
      return result;
    }

    const result: UnlockResult = {
      unlocked: false,
      reason: previousCompleted
        ? 'This submodule will be available from ' +
          new Date(startDate!).toLocaleDateString()
        : 'Complete the previous submodule or wait until ' +
          new Date(startDate!).toLocaleDateString()
    };
    await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_TTL);
    return result;
  } catch (error: unknown) {
    console.error('Error checking submodule access:', error);
    return {
      unlocked: false,
      reason: 'An error occurred while checking access'
    };
  }
}

/**
 * Utility function to check if all content and assessments in a submodule are completed
 */
export async function checkSubmoduleCompletion(
  userId: Types.ObjectId,
  batchId: Types.ObjectId,
  submoduleId: Types.ObjectId
): Promise<boolean> {
  try {
    const cacheKey = createCompletionCacheKey(userId, batchId, submoduleId);

    // Try to get from cache
    const cachedResult = await redis.get(cacheKey);
    if (cachedResult !== null) {
      return cachedResult === 'true';
    }

    // Get the submodule and user progress in a single query
    const [submodule, userProgress] = await Promise.all([
      SubModule.findById(submoduleId, {
        contents: 1,
        assessments: 1
      }).lean<ISubModuleDocument>(),
      UserProgress.findOne(
        {
          user: userId,
          batch: batchId
        },
        { contentProgress: 1, assessmentProgress: 1 } // Only retrieve needed fields
      ).lean()
    ]);

    if (!submodule || !userProgress) {
      await redis.set(cacheKey, 'false', 'EX', CACHE_TTL);
      return false;
    }

    // Check if all contents are completed
    const contentIds = (submodule.contents || []).map(content =>
      safeToString(content)
    );

    // Type assertion for contentItemProgress
    const contentItemProgress = Array.isArray(userProgress.contentItemProgress)
      ? userProgress.contentItemProgress
      : [];

    // For backward compatibility, check if content type is in the old format
    const completedContentIds = contentItemProgress
      .filter(
        (progress: {
          completed?: boolean;
          contentType?: string;
          contentId?: unknown;
        }) => Boolean(progress?.completed) && progress?.contentType === 'video'
      )
      .map((progress: { contentId?: unknown }) =>
        safeToString(progress?.contentId)
      );

    const allContentsCompleted =
      contentIds.length === 0 ||
      contentIds.every(id => completedContentIds.includes(id));

    // Check if all assessments are completed
    const assessmentIds = (submodule.assessments || []).map(assessment =>
      safeToString(assessment)
    );

    // Use contentItemProgress for assessments too (MCQs and coding problems)
    const completedAssessmentIds = contentItemProgress
      .filter(
        (progress: {
          completed?: boolean;
          contentType?: string;
          contentId?: unknown;
        }) =>
          Boolean(progress?.completed) &&
          (progress?.contentType === 'mcq' ||
            progress?.contentType === 'coding_problem')
      )
      .map((progress: { contentId?: unknown }) =>
        safeToString(progress?.contentId)
      );

    const allAssessmentsCompleted =
      assessmentIds.length === 0 ||
      assessmentIds.every(id => completedAssessmentIds.includes(id));

    // A submodule is completed if all its contents and assessments are completed
    const isCompleted = allContentsCompleted && allAssessmentsCompleted;

    // Cache the result
    await redis.set(cacheKey, isCompleted ? 'true' : 'false', 'EX', CACHE_TTL);

    return isCompleted;
  } catch (error: unknown) {
    console.error('Error checking submodule completion:', error);
    return false;
  }
}

/**
 * Utility function to update the submodule completion status
 */
export async function updateSubmoduleCompletionStatus(
  userId: Types.ObjectId,
  batchId: Types.ObjectId,
  submoduleId: Types.ObjectId
): Promise<boolean> {
  try {
    const isCompleted = await checkSubmoduleCompletion(
      userId,
      batchId,
      submoduleId
    );

    // If completed, update the user progress
    if (isCompleted) {
      await UserProgress.updateOne(
        {
          user: userId,
          batch: batchId,
          'subModuleProgress.subModule': submoduleId
        },
        {
          $set: {
            'subModuleProgress.$.completed': true,
            'subModuleProgress.$.completedAt': new Date()
          }
        }
      );

      // Invalidate relevant cache entries
      await redis.del(createCompletionCacheKey(userId, batchId, submoduleId));

      // Also invalidate the unlock status for the next submodule
      const submodule = await SubModule.findById(submoduleId, {
        module: 1,
        order: 1
      }).lean<ISubModuleDocument>();
      if (submodule) {
        const nextSubmodule = await SubModule.findOne(
          {
            module: submodule.module,
            order: submodule.order + 1
          },
          { _id: 1 }
        ).lean<ISubModuleDocument>();

        if (nextSubmodule && nextSubmodule._id) {
          await redis.del(createCacheKey(userId, batchId, nextSubmodule._id));
        }
      }
    }

    return isCompleted;
  } catch (error: unknown) {
    console.error('Error updating submodule completion status:', error);
    return false;
  }
}

/**
 * Batch check multiple submodules' unlock status at once
 * More efficient than calling isSubmoduleUnlocked multiple times
 */
export async function batchCheckSubmodulesUnlock(
  userId: Types.ObjectId,
  batchId: Types.ObjectId,
  submoduleIds: Types.ObjectId[]
): Promise<Map<string, UnlockResult>> {
  try {
    const result = new Map<string, UnlockResult>();
    const currentDate = new Date();

    // Try to get cached results first
    const cacheKeys = submoduleIds.map(id =>
      createCacheKey(userId, batchId, id)
    );
    const cachedResults = await Promise.all(
      cacheKeys.map(key => redis.get(key))
    );

    // Create a map of cached results
    const cachedMap = new Map<string, UnlockResult>();
    for (let i = 0; i < submoduleIds.length; i++) {
      if (cachedResults[i]) {
        const defaultResult: UnlockResult = {
          unlocked: false,
          reason: 'Unknown error'
        };
        const parsed = safeJsonParse<UnlockResult>(
          cachedResults[i],
          defaultResult
        );
        cachedMap.set(safeToString(submoduleIds[i]), parsed);
      }
    }

    // Filter out submodules that need to be checked
    const uncachedSubmoduleIds = submoduleIds.filter(
      id => !cachedMap.has(safeToString(id))
    );

    // If all results are cached, return them
    if (uncachedSubmoduleIds.length === 0) {
      return cachedMap;
    }

    // Get all user-specific deadlines in one query
    const userDeadlines = await UserDeadline.find({
      user: userId,
      batch: batchId,
      submodule: { $in: uncachedSubmoduleIds },
      isActive: true
    }).lean<IUserDeadlineDocument[]>();

    // Create a map for quick lookup of user deadlines
    const userDeadlineMap = new Map<string, IUserDeadlineDocument>();
    userDeadlines.forEach(deadline => {
      userDeadlineMap.set(safeToString(deadline.submodule), deadline);
    });

    // Get all batch-submodule deadlines as fallback
    const batchSubmoduleDeadlines = await BatchSubmoduleDeadline.find({
      batch: batchId,
      submodule: { $in: uncachedSubmoduleIds },
      isActive: true
    }).lean<IBatchSubmoduleDeadlineDocument[]>();

    // Create a map for quick lookup of batch deadlines
    const batchDeadlineMap = new Map<string, IBatchSubmoduleDeadlineDocument>();
    batchSubmoduleDeadlines.forEach(deadline => {
      batchDeadlineMap.set(safeToString(deadline.submodule), deadline);
    });

    // If we found batch deadlines but not user deadlines, initialize them
    if (batchSubmoduleDeadlines.length > 0 && userDeadlines.length === 0) {
      await UserDeadline.initializeUserDeadlines(userId, batchId);
    }

    // Get all submodules in one query
    const submodules = await SubModule.find({
      _id: { $in: uncachedSubmoduleIds }
    }).lean<ISubModuleDocument[]>();

    // Create maps for quick lookup
    const submoduleMap = new Map<string, ISubModuleDocument>();
    submodules.forEach(submodule => {
      submoduleMap.set(safeToString(submodule._id), submodule);
    });

    // Group submodules by module
    const moduleSubmodules = new Map<string, ISubModuleDocument[]>();
    submodules.forEach(submodule => {
      const moduleId = safeToString(submodule.module);
      if (!moduleSubmodules.has(moduleId)) {
        moduleSubmodules.set(moduleId, []);
      }
      const moduleSubmoduleList = moduleSubmodules.get(moduleId);
      if (moduleSubmoduleList) {
        moduleSubmoduleList.push(submodule);
      }
    });

    // Get user progress in one query
    const userProgress = await UserProgress.findOne(
      {
        user: userId,
        batch: batchId
      },
      { subModuleProgress: 1 }
    ).lean();

    // Create a map of completed submodules
    const completedSubmodules = new Set<string>();
    if (userProgress && userProgress.subModuleProgress) {
      userProgress.subModuleProgress
        .filter(progress => progress.completed)
        .forEach(progress => {
          completedSubmodules.add(safeToString(progress.subModule));
        });
    }

    // Add cached results to the result map
    cachedMap.forEach((value, key) => {
      result.set(key, value);
    });

    // Process each uncached submodule
    for (const submoduleId of uncachedSubmoduleIds) {
      const submoduleIdStr = safeToString(submoduleId);

      // First check for user-specific deadline, then fall back to batch deadline
      const userDeadline = userDeadlineMap.get(submoduleIdStr);
      const batchDeadline = batchDeadlineMap.get(submoduleIdStr);
      const submodule = submoduleMap.get(submoduleIdStr);

      // If no deadline or submodule found
      if ((!userDeadline && !batchDeadline) || !submodule) {
        const unlockResult: UnlockResult = {
          unlocked: false,
          reason: 'No deadline information or submodule not found'
        };
        result.set(submoduleIdStr, unlockResult);
        await redis.set(
          createCacheKey(userId, batchId, submoduleId),
          JSON.stringify(unlockResult),
          'EX',
          CACHE_TTL
        );
        continue;
      }

      // Check start date - use user deadline if available, otherwise batch deadline
      const startDate = userDeadline
        ? userDeadline.adjustedStartDate
        : batchDeadline?.startDate;

      if (startDate && currentDate < new Date(startDate)) {
        const formattedDate = new Date(startDate).toLocaleDateString();
        const unlockResult: UnlockResult = {
          unlocked: false,
          reason: `This submodule will be available from ${formattedDate}`
        };
        result.set(submoduleIdStr, unlockResult);
        await redis.set(
          createCacheKey(userId, batchId, submoduleId),
          JSON.stringify(unlockResult),
          'EX',
          CACHE_TTL
        );
        continue;
      }

      // If no dependency on previous submodule or it's the first one
      if (
        (batchDeadline && !batchDeadline.dependsOnPreviousSubmodule) ||
        submodule.order === 1
      ) {
        const unlockResult: UnlockResult = { unlocked: true };
        result.set(submoduleIdStr, unlockResult);
        await redis.set(
          createCacheKey(userId, batchId, submoduleId),
          JSON.stringify(unlockResult),
          'EX',
          CACHE_TTL
        );
        continue;
      }

      // Find the previous submodule in the same module
      const moduleId = safeToString(submodule.module);
      const moduleSubmoduleList = moduleSubmodules.get(moduleId) || [];
      const previousSubmodule = moduleSubmoduleList.find(
        sm => sm.order === submodule.order - 1
      );

      // If no previous submodule found
      if (!previousSubmodule) {
        const unlockResult: UnlockResult = { unlocked: true };
        result.set(submoduleIdStr, unlockResult);
        await redis.set(
          createCacheKey(userId, batchId, submoduleId),
          JSON.stringify(unlockResult),
          'EX',
          CACHE_TTL
        );
        continue;
      }

      // Check if previous submodule is completed
      const prevSubmoduleId = safeToString(previousSubmodule._id);
      if (completedSubmodules.has(prevSubmoduleId)) {
        const unlockResult: UnlockResult = { unlocked: true };
        result.set(submoduleIdStr, unlockResult);
        await redis.set(
          createCacheKey(userId, batchId, submoduleId),
          JSON.stringify(unlockResult),
          'EX',
          CACHE_TTL
        );
      } else {
        const unlockResult: UnlockResult = {
          unlocked: false,
          reason: 'Complete the previous submodule to unlock this one'
        };
        result.set(submoduleIdStr, unlockResult);
        await redis.set(
          createCacheKey(userId, batchId, submoduleId),
          JSON.stringify(unlockResult),
          'EX',
          CACHE_TTL
        );
      }
    }

    return result;
  } catch (error: unknown) {
    console.error('Error batch checking submodules unlock status:', error);
    // Return a map with all submodules locked due to error
    return new Map(
      submoduleIds.map(id => [
        safeToString(id),
        { unlocked: false, reason: 'An error occurred while checking access' }
      ])
    );
  }
}

/**
 * Invalidate cache for a specific user-batch-submodule combination
 * Call this when data that affects unlock status changes
 */
export async function invalidateSubmoduleCache(
  userId: Types.ObjectId,
  batchId: Types.ObjectId,
  submoduleId: Types.ObjectId
): Promise<void> {
  await Promise.all([
    redis.del(createCacheKey(userId, batchId, submoduleId)),
    redis.del(createCompletionCacheKey(userId, batchId, submoduleId))
  ]);
}
