import mongoose from 'mongoose';
import submoduleDao from '../dao/submodule.dao';
import { updateModulePoints } from './module.utils';
import BatchSubmoduleDeadline from '../models/batch-submodule-deadline.model';
import { ISubModule } from '../interfaces/submodule.interface';

export const simulateSubModuleReorder = async (
  submoduleId: string,
  newPosition: number
): Promise<{ success: boolean; message: string; data?: ISubModule[] }> => {
  const targetSubmodule = await submoduleDao.findById(submoduleId);
  if (!targetSubmodule) {
    return { success: false, message: 'Submodule not found' };
  }

  const submodules = await submoduleDao.simulateSubModuleReorder(
    targetSubmodule.module as string
  );
  if (!Array.isArray(submodules) || submodules.length === 0) {
    return { success: false, message: 'Submodules data is invalid or empty' };
  }

  const totalSubmodules = submodules.length;
  const targetIndex = submodules.findIndex(
    sm => sm._id?.toString() === submoduleId
  );
  if (targetIndex === -1) {
    return { success: false, message: 'Target submodule not found in list' };
  }

  const from = targetIndex;
  const to = newPosition - 1;

  if (to < 0 || to >= totalSubmodules) {
    return { success: false, message: 'Invalid target position' };
  }
  if (from === to) {
    return { success: false, message: 'No change in position' };
  }

  const originalSubmodules = submodules.map(sm => ({
    id: String(sm._id),
    order: sm.order
  }));

  const updatedSubmodules = [...submodules];
  const [moved] = updatedSubmodules.splice(from, 1);
  updatedSubmodules.splice(to, 0, moved);

  const reordered = updatedSubmodules.map((sm, i) => ({
    _id: String(sm._id),
    order: i + 1
  }));

  await submoduleDao.bulkUpdateOrders(reordered);

  const minAffected = Math.min(from, to) + 1;
  const maxAffected = Math.max(from, to) + 1;
  const affectedIds = originalSubmodules
    .filter(sm => sm.order >= minAffected && sm.order <= maxAffected)
    .map(sm => sm.id);

  if (!affectedIds.includes(submoduleId)) {
    affectedIds.push(submoduleId);
  }

  const deadlines = await BatchSubmoduleDeadline.find({
    submodule: { $in: affectedIds }
  }).exec();

  const batchMap = new Map<string, typeof deadlines>();
  for (const d of deadlines) {
    const batchId = d.batch.toString();
    if (!batchMap.has(batchId)) {
      batchMap.set(batchId, []);
    }
    batchMap.get(batchId)!.push(d);
  }

  const reorderedMap = new Map<string, number>();
  reordered.forEach(r => reorderedMap.set(r._id, r.order));

  const originalOrderMap = new Map<
    string,
    { startDate: Date; deadline: Date; days: number }
  >();
  for (const d of deadlines) {
    const subId = d.submodule.toString();
    const orig = originalSubmodules.find(sm => sm.id === subId);
    if (orig) {
      originalOrderMap.set(orig.order.toString(), {
        startDate: d.startDate,
        deadline: d.deadline,
        days: d.days
      });
    }
  }

  for (const [, batchDeadlines] of batchMap.entries()) {
    const bulkOps = [];

    for (const d of batchDeadlines) {
      const subId = d.submodule.toString();
      const newOrder = reorderedMap.get(subId);
      if (!newOrder) continue;

      const newData = originalOrderMap.get(newOrder.toString());
      if (!newData) continue;

      bulkOps.push({
        updateOne: {
          filter: { _id: d._id },
          update: {
            startDate: newData.startDate,
            deadline: newData.deadline,
            days: newData.days
          }
        }
      });
    }

    if (bulkOps.length) {
      await BatchSubmoduleDeadline.bulkWrite(bulkOps);
    }
  }

  return {
    success: true,
    message: 'Submodule reordered and deadlines updated',
    data: updatedSubmodules
  };
};

export const submodulePointsRefactor = async (
  submoduleId: string | mongoose.Types.ObjectId,
  points: number
): Promise<{ success: boolean; message: string }> => {
  const submodule = await submoduleDao.findById(submoduleId);
  if (!submodule) {
    return { success: false, message: 'Submodule not found' };
  }

  // check if the submodule has moduleId then we need to update the module points
  // if it is not attached to the module, then we can update the submodule directly

  await updateModulePoints(submodule.module as string, points);

  const updatedSubmodule = await submoduleDao.update(submoduleId as string, {
    points: submodule.points + points
  });

  if (!updatedSubmodule) {
    return { success: false, message: 'Failed to update submodule points' };
  }

  return {
    success: true,
    message: `previous points ${submodule.points} + ${points}`
  };
};
