import mongoose, { Types } from 'mongoose';
import BatchSubmoduleDeadline from '../models/batch-submodule-deadline.model';
import UserDeadlineAdjustment from '../models/user-deadline-adjustment.model';
import {
  IUserDeadlineAdjustment,
  IPauseHistory,
  ISubmoduleAdjustment
} from '../interfaces/user-deadline-adjustment.interface';
import moduleDao from '../dao/module.dao';

export interface ComputedDeadline {
  submoduleId: mongoose.Types.ObjectId;
  startDate: Date;
  deadline: Date;
  originalDeadline: Date;
  originalStartDate: Date;
  pauseAdjustmentDays: number;
  additionalAdjustmentDays: number;
  isExempt: boolean;
  hasCustomDates: boolean;
}

export interface BatchDeadlineInfo {
  _id: mongoose.Types.ObjectId;
  batch: mongoose.Types.ObjectId;
  submodule: mongoose.Types.ObjectId;
  startDate: Date;
  deadline: Date;
  dependsOnPreviousSubmodule: boolean;
  penaltyRules: Array<{
    daysLate: number;
    penaltyPercentage: number;
  }>;
  isActive: boolean;
}

interface ModuleWithSubModule {
  _id: string;
  title: string;
  description: string;
  order: number;
  duration: number;
  totalPoints: number;
  submodules: {
    _id: string;
    title: string;
    description: string;
    order: number;
    duration: number;
    totalPoints: number;
    isUnlocked: boolean;
    deadlineInfo: {
      startDate: Date | null;
      deadline: Date | null;
      dependsOnPreviousSubmodule: boolean | null;
    } | null;
    startDate?: Date;
    deadline?: Date;
    isExempt?: boolean;
    hasCustomDates?: boolean;
    pauseAdjustmentDays?: number;
    additionalAdjustmentDays?: number;
  }[];
}
export class DeadlineCalculator {
  /**
   * Calculate deadline for a specific user-batch-submodule combination
   */
  static async getUserSubmoduleDeadline(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId
  ): Promise<ComputedDeadline | null> {
    try {
      // 1. Get base deadline from batch-submodule
      const batchDeadline = await BatchSubmoduleDeadline.findOne({
        batch: batchId,
        submodule: submoduleId,
        isActive: true
      }).lean();

      if (!batchDeadline) {
        return null;
      }

      // 2. Get user adjustments
      const userAdjustment = await UserDeadlineAdjustment.findOne({
        user: userId,
        batch: batchId,
        isActive: true
      }).lean();

      // 3. Calculate final deadline
      return this.computeDeadline(
        batchDeadline as unknown as BatchDeadlineInfo,
        userAdjustment as unknown as IUserDeadlineAdjustment | null,
        submoduleId
      );
    } catch (error) {
      console.error('Error calculating user submodule deadline:', error);
      return null;
    }
  }

  /**
   * Calculate deadlines for all submodules in a batch for a specific user
   */
  static async getUserBatchDeadlines(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId
  ): Promise<ComputedDeadline[]> {
    try {
      // 1. Get all batch deadlines at once
      const batchDeadlines = await BatchSubmoduleDeadline.find({
        batch: batchId,
        isActive: true
      }).lean();

      // 2. Get user adjustments once
      const userAdjustment = await UserDeadlineAdjustment.findOne({
        user: userId,
        batch: batchId,
        isActive: true
      }).lean();

      // 3. Compute all deadlines in memory
      return batchDeadlines
        .map(batchDeadline =>
          this.computeDeadline(
            batchDeadline as unknown as BatchDeadlineInfo,
            userAdjustment as unknown as IUserDeadlineAdjustment | null,
            batchDeadline.submodule
          )
        )
        .filter((deadline): deadline is ComputedDeadline => deadline !== null);
    } catch (error) {
      console.error('Error calculating user batch deadlines:', error);
      return [];
    }
  }

  /**
   * Calculate deadlines for multiple users in a batch (for admin/instructor views)
   */
  static async getBatchUsersDeadlines(
    userIds: mongoose.Types.ObjectId[],
    batchId: mongoose.Types.ObjectId
  ): Promise<Map<string, ComputedDeadline[]>> {
    try {
      const result = new Map<string, ComputedDeadline[]>();

      // 1. Get all batch deadlines
      const batchDeadlines = await BatchSubmoduleDeadline.find({
        batch: batchId,
        isActive: true
      }).lean();

      // 2. Get all user adjustments for this batch
      const userAdjustments = await UserDeadlineAdjustment.find({
        user: { $in: userIds },
        batch: batchId,
        isActive: true
      }).lean();

      // Create a map for quick lookup
      const adjustmentMap = new Map<string, IUserDeadlineAdjustment>();
      userAdjustments.forEach(adj => {
        adjustmentMap.set(
          (adj as IUserDeadlineAdjustment).user.toString(),
          adj as IUserDeadlineAdjustment
        );
      });

      // 3. Compute deadlines for each user
      userIds.forEach(userId => {
        const userAdjustment = adjustmentMap.get(userId.toString()) || null;
        const userDeadlines = batchDeadlines
          .map(batchDeadline =>
            this.computeDeadline(
              batchDeadline as unknown as BatchDeadlineInfo,
              userAdjustment,
              batchDeadline.submodule
            )
          )
          .filter(
            (deadline): deadline is ComputedDeadline => deadline !== null
          );

        result.set(userId.toString(), userDeadlines);
      });

      return result;
    } catch (error) {
      console.error('Error calculating batch users deadlines:', error);
      return new Map();
    }
  }

  /**
   * Core computation logic for calculating a deadline
   */
  private static computeDeadline(
    batchDeadline: BatchDeadlineInfo,
    userAdjustment: IUserDeadlineAdjustment | null,
    submoduleId: mongoose.Types.ObjectId
  ): ComputedDeadline {
    let finalDeadline = new Date(batchDeadline.deadline);
    let finalStartDate = new Date(batchDeadline.startDate);
    let pauseAdjustmentDays = 0;
    let additionalAdjustmentDays = 0;
    let isExempt = false;
    let hasCustomDates = false;

    if (userAdjustment) {
      // Apply global pause days
      pauseAdjustmentDays = userAdjustment.totalPauseDays;
      finalDeadline.setDate(finalDeadline.getDate() + pauseAdjustmentDays);
      finalStartDate.setDate(finalStartDate.getDate() + pauseAdjustmentDays);

      // Apply submodule-specific adjustments
      const submoduleAdj = userAdjustment.submoduleAdjustments.find(
        (adj: ISubmoduleAdjustment) =>
          adj.submodule.toString() === submoduleId.toString()
      );

      if (submoduleAdj) {
        // Check for exemption
        if (submoduleAdj.isExempt) {
          isExempt = true;
        }

        // Apply custom dates if provided
        if (submoduleAdj.customStartDate) {
          finalStartDate = new Date(submoduleAdj.customStartDate);
          hasCustomDates = true;
        }

        if (submoduleAdj.customDeadline) {
          finalDeadline = new Date(submoduleAdj.customDeadline);
          hasCustomDates = true;
        } else if (submoduleAdj.additionalDays) {
          // Apply additional days only if no custom deadline
          additionalAdjustmentDays = submoduleAdj.additionalDays;
          finalDeadline.setDate(
            finalDeadline.getDate() + additionalAdjustmentDays
          );
        }
      }
    }

    return {
      submoduleId: batchDeadline.submodule,
      startDate: finalStartDate,
      deadline: finalDeadline,
      originalDeadline: batchDeadline.deadline,
      originalStartDate: batchDeadline.startDate,
      pauseAdjustmentDays,
      additionalAdjustmentDays,
      isExempt,
      hasCustomDates
    };
  }

  /**
   * Get remaining pause days for a user in a batch
   */
  static async getRemainingPauseDays(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId
  ): Promise<number> {
    try {
      const userAdjustment = await UserDeadlineAdjustment.findOne({
        user: userId,
        batch: batchId,
        isActive: true
      }).lean();

      if (!userAdjustment) {
        return 60; // Full 60 days available
      }

      const totalUsedDays = (
        userAdjustment as IUserDeadlineAdjustment
      ).pauseHistory.reduce(
        (sum: number, pause: IPauseHistory) => sum + pause.daysUsed,
        0
      );

      return Math.max(0, 60 - totalUsedDays);
    } catch (error) {
      console.error('Error getting remaining pause days:', error);
      return 0;
    }
  }

  /**
   * Check if a user has any active pauses
   */
  static async hasActivePause(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId
  ): Promise<boolean> {
    try {
      const userAdjustment = await UserDeadlineAdjustment.findOne({
        user: userId,
        batch: batchId,
        isActive: true,
        'pauseHistory.status': 'active'
      }).lean();

      return Boolean(userAdjustment);
    } catch (error) {
      console.error('Error checking active pause:', error);
      return false;
    }
  }
  /**
   * Maps calculated deadlines to module/submodule structure
   */
  private static mapDeadlinesToModules = (
    modules: ModuleWithSubModule[],
    deadlines: ComputedDeadline[]
  ): unknown[] => {
    // Create a map for quick lookup of deadlines by submodule ID
    const deadlineMap = new Map<string, ComputedDeadline>();
    for (const deadline of deadlines) {
      deadlineMap.set(deadline.submoduleId.toString(), deadline);
    }

    // Map deadlines to submodules
    return modules.map(module => {
      if (module.submodules && Array.isArray(module.submodules)) {
        module.submodules = module.submodules.map(submodule => {
          const deadline = deadlineMap.get(submodule._id.toString());
          if (deadline) {
            // Add deadline info in the same format expected by the original code
            submodule.startDate = deadline.startDate;
            submodule.deadline = deadline.deadline;

            // Add any additional properties needed for your logic
            submodule.isExempt = deadline.isExempt;
            submodule.hasCustomDates = deadline.hasCustomDates;
            submodule.pauseAdjustmentDays = deadline.pauseAdjustmentDays;
            submodule.additionalAdjustmentDays =
              deadline.additionalAdjustmentDays;
          }
          return submodule;
        });
      }
      return module;
    });
  };

  static getModulesWithCalculatedDeadlines = async (
    courseId: string | Types.ObjectId,
    batchId: string | Types.ObjectId,
    userId: string | Types.ObjectId
  ): Promise<unknown[]> => {
    // Run these operations in parallel for better performance
    const [modules, deadlines] = await Promise.all([
      // 1. Get modules with submodules
      moduleDao.getModulesWithSubmodules(courseId),

      // 2. Calculate deadlines for all submodules in the batch for this user
      DeadlineCalculator.getUserBatchDeadlines(
        new Types.ObjectId(userId.toString()),
        new Types.ObjectId(batchId.toString())
      )
    ]);

    // 3. Map deadlines to modules/submodules and return
    return this.mapDeadlinesToModules(modules, deadlines);
  };
}
