import { Types } from 'mongoose';

/**
 * Generates a unique 16-digit certificate ID based on user, course, and batch IDs
 * @param userId User ID
 * @param courseId Course ID
 * @param batchId Batch ID
 * @returns 16-digit certificate ID
 */
export const generateCertificateId = (
  userId: string | Types.ObjectId,
  courseId: string | Types.ObjectId,
  batchId: string | Types.ObjectId
): string => {
  // Extract portions from each ID
  const userPart = String(userId).replace(/\D/g, '').slice(-3).padStart(3, '0');
  const coursePart = String(courseId)
    .replace(/\D/g, '')
    .slice(-3)
    .padStart(3, '0');
  const batchPart = String(batchId)
    .replace(/\D/g, '')
    .slice(-3)
    .padStart(3, '0');

  // Generate timestamp component (6 digits)
  const timestamp = Date.now().toString().slice(-6);

  // Combine for 15 digits
  const baseId = `${userPart}${coursePart}${batchPart}${timestamp}`;

  // Add check digit using <PERSON>hn algorithm
  const checkDigit = calculateLuhnChecksum(baseId);

  return `${baseId}${checkDigit}`;
};

/**
 * Calculates Luhn algorithm check digit for validation
 */
const calculateLuhnChecksum = (digits: string): number => {
  let sum = 0;
  let alternate = false;

  for (let i = digits.length - 1; i >= 0; i--) {
    let n = Number(digits.charAt(i));
    if (isNaN(n)) n = 0;

    if (alternate) {
      n *= 2;
      if (n > 9) n -= 9;
    }

    sum += n;
    alternate = !alternate;
  }

  // Return check digit that makes sum divisible by 10
  return (10 - (sum % 10)) % 10;
};

/**
 * Verifies if a certificate ID is valid using Luhn algorithm
 */
export const verifyCertificateId = (certificateId: string): boolean => {
  if (!/^\d{16}$/.test(certificateId)) return false;

  const digits = certificateId.slice(0, 15);
  const checksum = parseInt(certificateId.slice(15), 10);

  return checksum === calculateLuhnChecksum(digits);
};
