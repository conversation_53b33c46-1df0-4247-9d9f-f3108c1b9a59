import moment from 'moment-timezone';

// Set default timezone to IST for the entire application
moment.tz.setDefault('Asia/Kolkata');

export const DateUtils = {
  /**
   * Get current date and time in IST
   */
  getCurrentDate(): Date | string {
    return moment().tz('Asia/Kolkata').format();
  },

  /**
   * Format date to ISO string in IST
   */
  formatISO(date: Date | string | number): string {
    return moment(date).tz('Asia/Kolkata').format();
  },

  /**
   * Parse a date string and return Date object in IST
   */
  parseDate(dateString: string): Date {
    return moment.tz(dateString, 'Asia/Kolkata').toDate();
  },

  /**
   * Convert any date to IST
   */
  toIST(date: Date | string | number): Date {
    return moment(date).tz('Asia/Kolkata').toDate();
  },

  /**
   * Get start of day in IST
   */
  startOfDay(date: Date | string | number = new Date()): Date {
    return moment(date).tz('Asia/Kolkata').startOf('day').toDate();
  },

  /**
   * Get end of day in IST
   */
  endOfDay(date: Date | string | number = new Date()): Date {
    return moment(date).tz('Asia/Kolkata').endOf('day').toDate();
  },

  /**
   * Add time to a date in IST
   */
  add(
    date: Date | string | number,
    amount: number,
    unit: moment.unitOfTime.DurationConstructor
  ): Date {
    return moment(date).tz('Asia/Kolkata').add(amount, unit).toDate();
  },

  /**
   * Difference between dates in the specified unit
   */
  diff(
    date1: Date | string | number,
    date2: Date | string | number,
    unit: moment.unitOfTime.Diff = 'days'
  ): number {
    return moment(date1)
      .tz('Asia/Kolkata')
      .diff(moment(date2).tz('Asia/Kolkata'), unit);
  }
};
