import mongoose from 'mongoose';
import moduleDao from '../dao/module.dao';

/**
 * Simulates and applies module reordering based on user input.
 * Updates the database using a bulk operation.
 */
export const simulateModuleReorder = async (
  moduleId: string,
  newPosition: number
): Promise<{ success: boolean; message: string }> => {
  const targetModule = await moduleDao.findById(moduleId);
  if (!targetModule) {
    return { success: false, message: 'Module not found' };
  }

  const modules = await moduleDao.simulateModuleReorder(targetModule.course);
  if (!modules || !Array.isArray(modules)) {
    return { success: false, message: 'Modules data is invalid or undefined' };
  }

  // Ensure the input is 1-based (user-facing)
  const totalModules = modules.length;
  const targetIndex = Array.isArray(modules)
    ? modules.findIndex(m => m._id?.toString() === moduleId)
    : -1;

  if (targetIndex === -1) {
    return {
      success: false,
      message: 'Target module not found in course modules'
    };
  }

  const from = targetIndex;
  const to = newPosition - 1;

  if (to < 0 || to >= totalModules) {
    return {
      success: false,
      message: 'Invalid target position for module reordering'
    };
  }

  if (from === to) {
    return { success: false, message: 'No change in module position detected' };
  }

  const [movedModule] = modules.splice(from, 1);
  modules.splice(to, 0, movedModule);

  // Assign new order values starting from 1
  const reordered = modules.map((mod, index) => ({
    _id: (mod._id as string).toString(),
    order: index + 1
  }));

  // Apply updates to DB
  await moduleDao.bulkUpdateOrders(reordered);
  return { success: true, message: 'Module order updated successfully' };
};

export const updateModulePoints = async (
  moduleId: string | mongoose.Types.ObjectId,
  points: number
): Promise<{ success: boolean; message: string }> => {
  const targetModule = await moduleDao.findById(moduleId);
  if (!targetModule) {
    return { success: false, message: 'Module not found' };
  }
  //update the points in the module
  const updatedModule = await moduleDao.update(targetModule._id as string, {
    totalPoints: targetModule.totalPoints + points
  });
  if (!updatedModule) {
    return { success: false, message: 'Failed to update module points' };
  }
  return { success: true, message: 'Module points updated successfully' };
};
