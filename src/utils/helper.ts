import { Request, Response, NextFunction, RequestHandler } from 'express';
import { Validation<PERSON>hain, validationResult } from 'express-validator';
import ResponseHandler from './response-handler';

export const validateChain = (
  validations: ValidationChain[]
): RequestHandler[] => {
  return [
    ...validations,
    (req: Request, res: Response, next: NextFunction): void => {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json(
          ResponseHandler.validationError(
            errors.array().map(error => ({
              message: String(error.msg)
            }))
          )
        );
        return;
      }
      next();
    }
  ];
};
