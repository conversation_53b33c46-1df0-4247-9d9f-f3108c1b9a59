import mongoose, { Schema, Types, Model } from 'mongoose';
import { ISubModule } from '../interfaces/submodule.interface';
import <PERSON>rror<PERSON>and<PERSON> from '../utils/error-handler';

// Define interfaces for the content progress
interface ContentProgressData {
  completed: boolean;
  pointsEarned: number;
  completedAt?: Date;
}

interface ContentWithProgressData {
  contentId: string;
  title: string;
  type: string;
  order: number;
  points: number;
  section: string;
  progress: ContentProgressData | null;
}

interface SubmoduleContentResponse {
  submoduleId: Types.ObjectId;
  title: string;
  description: string;
  moduleId: Types.ObjectId;
  order: number;
  progress: number;
  progressDetails: {
    videoProgress: {
      completed: number;
      total: number;
    };
    codingProblemsProgress: {
      attempted: number;
      total: number;
    };
    mcqsProgress: {
      attempted: number;
      total: number;
    };
    overallScore: {
      userPoints: number;
      totalPoints: number;
    };
  };
  content: {
    lessons: ContentWithProgressData[];
    practice: ContentWithProgressData[];
  };
}
interface AggregationResult {
  _id: Types.ObjectId;
  title: string;
  description: string;
  module: Types.ObjectId;
  order: number;
  contents: Array<{
    _id: Types.ObjectId;
    contentId: Types.ObjectId;
    contentType: string;
    order: number;
    section?: string;
    title: string;
    points: number;
  }>;
  userProgress?: {
    _id: Types.ObjectId;
    contentItemProgress: Array<{
      contentId: Types.ObjectId;
      contentType: string;
      subModuleId?: Types.ObjectId; // Add this field to match our updated schema
      completed: boolean;
      pointsEarned: number;
      completedAt?: Date;
    }>;
    subModuleProgress: Array<{
      subModule: Types.ObjectId;
      completed: boolean;
      pointsEarned: number;
      progressPercentage: number;
    }>;
  };
}
// Extend the interface to include the static method
interface ISubModuleModel extends Model<ISubModule> {
  getContentWithProgress(
    submoduleId: string,
    userId: string
  ): Promise<SubmoduleContentResponse>;
}

// Define the main schema
const subModuleSchema = new Schema<ISubModule, ISubModuleModel>(
  {
    title: {
      type: String,
      required: [true, 'SubModule title is required'],
      trim: true
    },
    description: {
      type: String,
      required: [true, 'SubModule description is required']
    },
    module: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Module',
      required: [true, 'Module reference is required']
    },
    order: {
      type: Number,
      required: [true, 'SubModule order is required']
    },
    duration: {
      type: Number, // in minutes
      required: [true, 'SubModule duration is required']
    },
    points: {
      type: Number,
      default: 0
    },
    isActive: {
      type: Boolean,
      default: true
    },

    // Section-based structure
    sections: {
      lesson: {
        type: Boolean,
        required: [true, 'Lesson section is required'],
        default: false
      },
      practice: {
        type: Boolean,
        required: [true, 'Practice section is required'],
        default: false
      }
    }
  },
  { timestamps: true }
);

// Create indexes for common queries
subModuleSchema.index({ module: 1, order: 1 });

// Helper function to format the aggregation result (within the schema scope)
function formatSubmoduleResponse(
  data: AggregationResult
): SubmoduleContentResponse {
  // Extract basic submodule data
  const submodule = {
    submoduleId: data._id,
    title: data.title,
    description: data.description,
    moduleId: data.module,
    order: data.order
  };

  // Process contents and calculate metrics
  const contents = data.contents || [];
  const userProgress = data.userProgress || { contentItemProgress: [] };
  const subModuleId = data._id.toString();

  // Create progress lookup map for O(1) access using composite keys
  const progressMap: Record<string, ContentProgressData> = {};
  if (
    userProgress.contentItemProgress &&
    Array.isArray(userProgress.contentItemProgress)
  ) {
    userProgress.contentItemProgress.forEach(item => {
      if (item.contentId && item.subModuleId) {
        // Use composite key with both contentId and subModuleId
        const compositeKey = `${item.contentId.toString()}_${item.subModuleId.toString()}`;
        progressMap[compositeKey] = {
          completed: item.completed || false,
          pointsEarned: item.pointsEarned || 0,
          completedAt: item.completedAt
        };
      } else if (item.contentId) {
        // Fallback for backward compatibility with existing data
        progressMap[item.contentId.toString()] = {
          completed: item.completed || false,
          pointsEarned: item.pointsEarned || 0,
          completedAt: item.completedAt
        };
      }
    });
  }

  // Calculate progress metrics
  let videoTotal = 0;
  let videoCompleted = 0;
  let codingTotal = 0;
  let codingAttempted = 0;
  let mcqTotal = 0;
  let mcqAttempted = 0;
  let totalPossiblePoints = 0;
  let earnedPoints = 0;

  // Organize content into categories
  const lessons: ContentWithProgressData[] = [];
  const practice: ContentWithProgressData[] = [];

  contents.forEach(content => {
    const contentId = content.contentId ? content.contentId.toString() : '';
    // Try the composite key first, then fall back to just contentId for backward compatibility
    const compositeKey = `${contentId}_${subModuleId}`;
    const progress =
      progressMap[compositeKey] || progressMap[contentId] || null;
    const points = content.points || 0;

    totalPossiblePoints += points;
    if (progress) {
      earnedPoints += progress.pointsEarned || 0;
    }

    // Count by content type
    if (content.contentType === 'video') {
      videoTotal++;
      if (progress?.completed) videoCompleted++;
    } else if (content.contentType === 'coding_problem') {
      codingTotal++;
      if (progress) codingAttempted++;
    } else if (content.contentType === 'mcq') {
      mcqTotal++;
      if (progress) mcqAttempted++;
    }

    // Create content item with progress
    const contentWithProgress: ContentWithProgressData = {
      contentId,
      title: content.title || '',
      type: content.contentType,
      order: content.order || 0,
      points,
      section: content.section || 'main',
      progress
    };

    // Categorize content based on section field rather than content type
    if (content.section === 'lesson') {
      lessons.push(contentWithProgress);
    } else if (content.section === 'practice') {
      practice.push(contentWithProgress);
    }
  });

  // Sort by order
  lessons.sort((a, b) => a.order - b.order);
  practice.sort((a, b) => a.order - b.order);

  // Calculate completion percentage
  let percentComplete =
    totalPossiblePoints > 0 ? (earnedPoints / totalPossiblePoints) * 100 : 0;
  percentComplete = Number(percentComplete.toFixed(2)); // Ensure it's a string with 2 decimal places
  // Return the formatted response
  return {
    ...submodule,
    progress: percentComplete,
    progressDetails: {
      videoProgress: {
        completed: videoCompleted,
        total: videoTotal
      },
      codingProblemsProgress: {
        attempted: codingAttempted,
        total: codingTotal
      },
      mcqsProgress: {
        attempted: mcqAttempted,
        total: mcqTotal
      },
      overallScore: {
        userPoints: earnedPoints,
        totalPoints: totalPossiblePoints
      }
    },
    content: {
      lessons,
      practice
    }
  };
}

// Add static method to the SubModule schema
subModuleSchema.statics.getContentWithProgress = async function (
  submoduleId: string,
  userId: string
): Promise<SubmoduleContentResponse> {
  const objectIdSubmoduleId = new Types.ObjectId(submoduleId);
  const objectIdUserId = new Types.ObjectId(userId);

  const result = await this.aggregate<AggregationResult>([
    // Stage 1: Match the specific submodule
    {
      $match: { _id: objectIdSubmoduleId }
    },

    // Stage 2: Get basic submodule info
    {
      $project: {
        _id: 1,
        title: 1,
        description: 1,
        module: 1,
        order: 1
      }
    },

    // Stage 3: Lookup submodule content
    {
      $lookup: {
        from: 'submodulecontents',
        let: { submoduleId: '$_id' },
        pipeline: [
          { $match: { $expr: { $eq: ['$submodule', '$$submoduleId'] } } },
          // Populate the content based on contentType
          {
            $lookup: {
              from: 'videos',
              localField: 'contentId',
              foreignField: '_id',
              as: 'videoContent'
            }
          },
          {
            $lookup: {
              from: 'mcqs',
              localField: 'contentId',
              foreignField: '_id',
              as: 'mcqContent'
            }
          },
          {
            $lookup: {
              from: 'codingproblems',
              localField: 'contentId',
              foreignField: '_id',
              as: 'codingContent'
            }
          },
          {
            $lookup: {
              from: 'notes',
              localField: 'contentId',
              foreignField: '_id',
              as: 'noteContent'
            }
          },
          // Add content details based on type
          {
            $addFields: {
              contentDetails: {
                $cond: [
                  { $eq: ['$contentType', 'video'] },
                  { $arrayElemAt: ['$videoContent', 0] },
                  {
                    $cond: [
                      { $eq: ['$contentType', 'mcq'] },
                      { $arrayElemAt: ['$mcqContent', 0] },
                      {
                        $cond: [
                          { $eq: ['$contentType', 'coding_problem'] },
                          { $arrayElemAt: ['$codingContent', 0] },
                          { $arrayElemAt: ['$noteContent', 0] }
                        ]
                      }
                    ]
                  }
                ]
              }
            }
          },
          // Project only needed fields
          {
            $project: {
              _id: 1,
              contentId: 1,
              contentType: 1,
              order: 1,
              section: 1,
              title: '$contentDetails.title',
              points: { $ifNull: ['$contentDetails.points', 0] }
            }
          },
          // Sort by order
          { $sort: { order: 1 } }
        ],
        as: 'contents'
      }
    },

    // Stage 4: Lookup user progress
    {
      $lookup: {
        from: 'userprogresses',
        let: { submoduleId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$user', objectIdUserId]
              }
            }
          },
          // Project only needed progress fields with filtered contentItemProgress
          {
            $project: {
              _id: 1,
              // Filter contentItemProgress to include entries that have this subModuleId OR have no subModuleId (for backward compatibility)
              contentItemProgress: {
                $filter: {
                  input: '$contentItemProgress',
                  as: 'item',
                  cond: {
                    $or: [
                      { $eq: ['$$item.subModuleId', '$$submoduleId'] },
                      { $eq: [{ $ifNull: ['$$item.subModuleId', null] }, null] }
                    ]
                  }
                }
              },
              subModuleProgress: {
                $filter: {
                  input: '$subModuleProgress',
                  as: 'progress',
                  cond: { $eq: ['$$progress.subModule', '$$submoduleId'] }
                }
              }
            }
          }
        ],
        as: 'userProgress'
      }
    },

    // Stage 5: Unwind and process user progress
    {
      $addFields: {
        userProgress: { $arrayElemAt: ['$userProgress', 0] }
      }
    }
  ]);

  // Process the aggregation result
  if (!result || result.length === 0) {
    throw new ErrorHandler(404, 'Submodule not found');
  }

  if (!result || result.length === 0 || !result[0]) {
    throw new ErrorHandler(404, 'Submodule not found');
  }
  const aggregationResult = result[0];
  if (!aggregationResult || typeof aggregationResult !== 'object') {
    throw new ErrorHandler(500, 'Invalid aggregation result');
  }
  return formatSubmoduleResponse(aggregationResult);
};

// Create the model with the proper interface
const SubModule = mongoose.model<ISubModule, ISubModuleModel>(
  'SubModule',
  subModuleSchema
);
export default SubModule;
