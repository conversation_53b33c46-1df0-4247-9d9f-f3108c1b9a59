import mongoose, { Schema } from 'mongoose';
import { IBookmark } from '../interfaces/bookmark.interface';

const bookmarkSchema = new Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: true
    },
    submodule: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SubModule',
      required: true
    },
    contentType: {
      type: String,
      enum: ['video', 'note', 'mcq', 'coding_problem', 'coding_lab'],
      required: true
    },
    contentId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      refPath: 'contentModel'
    },
    contentModel: {
      type: String,
      required: true,
      enum: ['Video', 'Note', 'MCQ', 'CodingProblem', 'CodingLab']
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Prevent duplicate bookmarks
bookmarkSchema.index(
  { user: 1, contentType: 1, contentId: 1, batch: 1 },
  { unique: true }
);

// Helper method to map contentType to contentModel
bookmarkSchema.pre('validate', function (next) {
  const contentTypeToModel: Record<string, string> = {
    video: 'Video',
    note: 'Note',
    mcq: 'MCQ',
    coding_problem: 'CodingProblem',
    coding_lab: 'CodingLab'
  };

  // Use type assertion to access document properties
  const doc = this as unknown as {
    contentType?: string;
    contentModel?: string;
  };

  // Set contentModel based on contentType
  if (doc.contentType && contentTypeToModel[doc.contentType]) {
    doc.contentModel = contentTypeToModel[doc.contentType];
  }

  next();
});

// Static method to get all bookmarks for a user in a batch
bookmarkSchema.statics.getUserBookmarks = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId
): Promise<IBookmark[]> {
  try {
    const model = this as mongoose.Model<IBookmark>;

    // Find all bookmarks for this user and batch
    const bookmarks = await model
      .find({
        user: userId,
        batch: batchId,
        isActive: true
      })
      .populate('submodule', 'title')
      .populate({
        path: 'contentId',
        select: 'title',
        // Using type assertion to handle the missing refPath property in type definitions
        refPath: 'contentModel'
      } as mongoose.PopulateOptions)
      .sort({ createdAt: -1 });

    return bookmarks;
  } catch (error: unknown) {
    console.error('Error getting user bookmarks:', error);
    return [];
  }
};

// Static method to toggle a bookmark (add if doesn't exist, remove if it does)
bookmarkSchema.statics.toggleBookmark = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  submoduleId: mongoose.Types.ObjectId,
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
  contentId: mongoose.Types.ObjectId
): Promise<{ bookmarked: boolean; message: string }> {
  try {
    const model = this as mongoose.Model<IBookmark>;
    const contentTypeToModel: Record<string, string> = {
      video: 'Video',
      note: 'Note',
      mcq: 'MCQ',
      coding_problem: 'CodingProblem',
      coding_lab: 'CodingLab'
    };

    // Create query object
    const query: Record<string, unknown> = {
      user: userId,
      batch: batchId,
      submodule: submoduleId,
      contentType: contentType,
      contentId: contentId,
      contentModel: contentTypeToModel[contentType],
      isActive: true
    };

    // Check if bookmark already exists
    const existingBookmark = await model.findOne(query);

    if (existingBookmark) {
      // Remove existing bookmark
      await model.findByIdAndDelete(existingBookmark._id);
      return {
        bookmarked: false,
        message: `Item removed from bookmarks`
      };
    } else {
      // Create new bookmark
      const newBookmark = new model(query);

      await newBookmark.save();
      return {
        bookmarked: true,
        message: `Item added to bookmarks`
      };
    }
  } catch (error: unknown) {
    console.error('Error toggling bookmark:', error);

    // Check if error is due to duplicate key (already bookmarked)
    const mongoError = error as { code?: number; name?: string };
    if (
      mongoError &&
      mongoError.name === 'MongoError' &&
      mongoError.code === 11000
    ) {
      return {
        bookmarked: true,
        message: 'This item is already bookmarked'
      };
    }

    return {
      bookmarked: false,
      message: 'An error occurred while toggling bookmark'
    };
  }
};

// Static method to check if an item is bookmarked
bookmarkSchema.statics.isBookmarked = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
  contentId: mongoose.Types.ObjectId
): Promise<boolean> {
  try {
    const model = this as mongoose.Model<IBookmark>;

    // Create query object
    const query: Record<string, unknown> = {
      user: userId,
      batch: batchId,
      contentType: contentType,
      contentId: contentId,
      isActive: true
    };

    // Check if bookmark exists
    const count = await model.countDocuments(query);
    return count > 0;
  } catch (error: unknown) {
    console.error('Error checking bookmark status:', error);
    return false;
  }
};

// Update the interface to include the static methods
interface BookmarkModel extends mongoose.Model<IBookmark> {
  getUserBookmarks(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId
  ): Promise<IBookmark[]>;

  toggleBookmark(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId,
    contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
    contentId: mongoose.Types.ObjectId
  ): Promise<{ bookmarked: boolean; message: string }>;

  isBookmarked(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    contentType: 'video' | 'note' | 'mcq' | 'coding_problem' | 'coding_lab',
    contentId: mongoose.Types.ObjectId
  ): Promise<boolean>;
}

const Bookmark = mongoose.model<IBookmark, BookmarkModel>(
  'Bookmark',
  bookmarkSchema
);

export default Bookmark;
