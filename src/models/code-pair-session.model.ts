import mongoose, { Schema } from 'mongoose';
import { ICodePairSession } from '../interfaces/code-pair-session.interface';

const codePairSessionSchema: Schema<ICodePairSession> = new Schema(
  {
    student: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Student reference is required']
    },
    mentor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Mentor reference is required']
    },
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course',
      required: [true, 'Course reference is required']
    },
    module: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Module'
    },
    subModule: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SubModule'
    },
    // Content reference
    contentType: {
      type: String,
      enum: ['mcq', 'coding_problem', 'coding_lab']
    },
    contentId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'contentModel'
    },
    contentModel: {
      type: String,
      enum: ['MCQ', 'CodingProblem', 'CodingLab']
    },
    sessionId: {
      type: String,
      required: [true, 'Session ID is required'],
      unique: true
    },
    status: {
      type: String,
      enum: ['requested', 'scheduled', 'in_progress', 'completed', 'cancelled'],
      default: 'requested'
    },
    scheduledStartTime: {
      type: Date
    },
    scheduledEndTime: {
      type: Date
    },
    actualStartTime: {
      type: Date
    },
    actualEndTime: {
      type: Date
    },
    duration: {
      type: Number // in minutes
    },
    topic: {
      type: String,
      required: [true, 'Session topic is required']
    },
    description: {
      type: String,
      required: [true, 'Session description is required']
    },
    codeLanguage: {
      type: String,
      required: [true, 'Code language is required']
    },
    initialCode: {
      type: String
    },
    finalCode: {
      type: String
    },
    notes: {
      type: String
    },
    feedback: {
      _id: false,
      rating: {
        type: Number,
        min: 1,
        max: 5
      },
      comments: {
        type: String
      }
    },
    recordingUrl: {
      type: String
    }
  },
  { timestamps: true }
);

// Helper method to map contentType to contentModel
codePairSessionSchema.pre('validate', function (next) {
  const contentTypeToModel: Record<string, string> = {
    mcq: 'MCQ',
    coding_problem: 'CodingProblem',
    coding_lab: 'CodingLab'
  };

  // Use type assertion to access document properties
  const doc = this as unknown as {
    contentType?: string;
    contentModel?: string;
  };

  // Set contentModel based on contentType
  if (doc.contentType && contentTypeToModel[doc.contentType]) {
    doc.contentModel = contentTypeToModel[doc.contentType];
  }

  next();
});

// Create indexes for common queries
codePairSessionSchema.index({ student: 1 });
codePairSessionSchema.index({ mentor: 1 });
codePairSessionSchema.index({ status: 1 });
codePairSessionSchema.index({ sessionId: 1 }, { unique: true });
codePairSessionSchema.index({ contentType: 1, contentId: 1 });

const CodePairSession = mongoose.model<ICodePairSession>(
  'CodePairSession',
  codePairSessionSchema
);
export default CodePairSession;
