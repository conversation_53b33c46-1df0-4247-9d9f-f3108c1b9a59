import mongoose, { Schema } from 'mongoose';
import { IBatchPause } from '../interfaces/batch-pause.interface';

// Maximum allowed pause days per user per batch
const MAX_PAUSE_DAYS = 60;

// Helper function to calculate days between two dates
function calculateDaysBetween(startDate: Date, endDate: Date): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}

const batchPauseSchema: Schema<IBatchPause> = new Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User reference is required']
    },
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: [true, 'Batch reference is required']
    },
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course',
      required: [true, 'Course reference is required']
    },
    startDate: {
      type: Date,
      required: [true, 'Pause start date is required']
    },
    endDate: {
      type: Date,
      required: [true, 'Pause end date is required']
    },
    reason: {
      type: String,
      required: [true, 'Pause reason is required']
    },
    status: {
      type: String,
      enum: ['active', 'completed'],
      default: 'active'
    },
    daysUsed: {
      type: Number,
      default: 0
    },
    comments: {
      type: String
    }
  },
  { timestamps: true }
);

// Pre-save hook to validate the 60-day limit and calculate daysUsed
batchPauseSchema.pre('save', async function (next) {
  try {
    if (this.isNew) {
      // Calculate days for this pause request
      const pauseDays = calculateDaysBetween(this.startDate, this.endDate);
      this.daysUsed = pauseDays;

      // Find all existing pauses for this user and batch
      const model = mongoose.model<IBatchPause>('BatchPause');
      const existingPauses = await model
        .find({
          user: this.user,
          batch: this.batch,
          _id: { $ne: this._id }, // Exclude current document if it exists
          status: { $in: ['active', 'completed'] }
        })
        .lean();

      // Calculate total days used
      let totalDaysUsed = pauseDays;

      // Helper function to safely add days from a pause object
      const addDaysFromPause = (pause: unknown): number => {
        if (pause && typeof pause === 'object' && 'daysUsed' in pause) {
          return Number(pause.daysUsed) || 0;
        }
        return 0;
      };

      // Sum up all days used from existing pauses
      if (Array.isArray(existingPauses)) {
        totalDaysUsed += existingPauses.reduce(
          (sum, pause) => sum + addDaysFromPause(pause),
          0
        );
      }

      // Check if total exceeds the limit
      if (totalDaysUsed > MAX_PAUSE_DAYS) {
        return next(
          new Error(
            `Cannot exceed the maximum pause limit of ${MAX_PAUSE_DAYS} days. You have already used ${totalDaysUsed - pauseDays} days and are requesting ${pauseDays} more days.`
          )
        );
      }
    }
    next();
  } catch (error: unknown) {
    next(error instanceof Error ? error : new Error(String(error)));
  }
});

// Static method to get remaining pause days for a user in a batch
batchPauseSchema.statics.getRemainingPauseDays = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId
): Promise<number> {
  // Type assertion for 'this' to avoid TypeScript errors
  const model = this as mongoose.Model<IBatchPause>;

  const pauses = await model
    .find({
      user: userId,
      batch: batchId,
      status: { $in: ['active', 'completed'] }
    })
    .lean();

  // Helper function to safely add days from a pause object
  const addDaysFromPause = (pause: unknown): number => {
    if (pause && typeof pause === 'object' && 'daysUsed' in pause) {
      return Number(pause.daysUsed) || 0;
    }
    return 0;
  };

  // Sum up all days used from existing pauses
  let usedDays = 0;
  if (Array.isArray(pauses)) {
    usedDays = pauses.reduce((sum, pause) => sum + addDaysFromPause(pause), 0);
  }

  return Math.max(0, MAX_PAUSE_DAYS - usedDays);
};

// Post-save hook to shift deadlines when a batch pause is created
batchPauseSchema.post('save', async function () {
  try {
    // Only process for newly created active pauses
    if (this.isNew && this.status === 'active') {
      const pauseDays = this.daysUsed;
      const userId = this.user;
      const batchId = this.batch;

      // Import the UserDeadline model with proper interface
      interface UserDeadlineModel {
        initializeUserDeadlines(
          userId: mongoose.Types.ObjectId,
          batchId: mongoose.Types.ObjectId
        ): Promise<number>;
        adjustDeadlinesForPause(
          userId: mongoose.Types.ObjectId,
          batchId: mongoose.Types.ObjectId,
          pauseDays: number,
          pauseStartDate: Date
        ): Promise<number>;
      }

      const UserDeadline = mongoose.model(
        'UserDeadline'
      ) as unknown as UserDeadlineModel;

      // First, ensure user deadlines are initialized
      // This is important for users who are pausing for the first time
      await UserDeadline.initializeUserDeadlines(userId, batchId);

      // Now adjust the deadlines for this specific user
      const updatedCount = await UserDeadline.adjustDeadlinesForPause(
        userId,
        batchId,
        pauseDays,
        this.startDate
      );

      // Use console.warn instead of console.log to comply with ESLint rules
      console.warn(
        `Successfully shifted ${updatedCount} deadlines by ${pauseDays} days for user ${userId.toString()} in batch ${batchId.toString()}`
      );
    }
  } catch (error: unknown) {
    console.error('Error shifting deadlines after batch pause:', error);
  }
});

// Create indexes for common queries
batchPauseSchema.index({ user: 1, batch: 1 });
batchPauseSchema.index({ status: 1 });

// Static method to resume a batch pause early
batchPauseSchema.statics.resumePauseEarly = async function (
  pauseId: mongoose.Types.ObjectId
): Promise<boolean> {
  try {
    const model = this as mongoose.Model<IBatchPause>;

    // Find the active pause
    const pause = await model.findOne({
      _id: pauseId,
      status: 'active'
    });

    if (!pause) {
      return false; // Pause not found or not active
    }

    const currentDate = new Date();
    const startDate = new Date(pause.startDate);
    let actualDaysUsed = 0;

    // Case 1: If pause hasn't started yet (future pause being canceled)
    if (startDate > currentDate) {
      // No days used if canceling before start date
      actualDaysUsed = 1;
    }
    // Case 2: Pause started but being resumed early
    else {
      const diffTime = Math.abs(currentDate.getTime() - startDate.getTime());
      actualDaysUsed = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      // Ensure we count at least 1 day if resumed on the same day it started
      actualDaysUsed = Math.max(1, actualDaysUsed);
    }

    // Calculate days to adjust back (original days - actual days)
    const daysToAdjustBack = pause.daysUsed - actualDaysUsed;

    // Update the pause with actual days used and completed status
    await model.updateOne(
      { _id: pauseId },
      {
        $set: {
          status: 'completed',
          daysUsed: actualDaysUsed,
          endDate: currentDate
        }
      }
    );

    // If we need to adjust deadlines back
    if (daysToAdjustBack > 0) {
      // Import the UserDeadline model
      interface UserDeadlineModel {
        adjustDeadlinesForEarlyResumption(
          userId: mongoose.Types.ObjectId,
          batchId: mongoose.Types.ObjectId,
          daysToAdjustBack: number
        ): Promise<number>;
      }

      const UserDeadline = mongoose.model(
        'UserDeadline'
      ) as unknown as UserDeadlineModel;

      // Adjust deadlines back for early resumption
      await UserDeadline.adjustDeadlinesForEarlyResumption(
        pause.user,
        pause.batch,
        daysToAdjustBack
      );
    }

    return true;
  } catch (error: unknown) {
    console.error('Error resuming batch pause early:', error);
    return false;
  }
};

// Static method to complete a batch pause (without adjusting deadlines)
batchPauseSchema.statics.completePause = async function (
  pauseId: mongoose.Types.ObjectId
): Promise<boolean> {
  try {
    const model = this as mongoose.Model<IBatchPause>;

    // Find and update the pause status to 'completed'
    const result = await model.updateOne(
      { _id: pauseId, status: 'active' },
      { $set: { status: 'completed' } }
    );

    return result.modifiedCount > 0;
  } catch (error: unknown) {
    console.error('Error completing batch pause:', error);
    return false;
  }
};

// Static method to check and complete all expired pauses
batchPauseSchema.statics.completeExpiredPauses =
  async function (): Promise<number> {
    try {
      const model = this as mongoose.Model<IBatchPause>;
      const currentDate = new Date();

      // Find and update all expired pauses
      const result = await model.updateMany(
        {
          status: 'active',
          endDate: { $lt: currentDate }
        },
        { $set: { status: 'completed' } }
      );

      return result.modifiedCount;
    } catch (error: unknown) {
      console.error('Error completing expired batch pauses:', error);
      return 0;
    }
  };

// Update the interface to include the static methods
interface BatchPauseModel extends mongoose.Model<IBatchPause> {
  getRemainingPauseDays(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId
  ): Promise<number>;

  completePause(pauseId: mongoose.Types.ObjectId): Promise<boolean>;

  resumePauseEarly(pauseId: mongoose.Types.ObjectId): Promise<boolean>;

  completeExpiredPauses(): Promise<number>;
}

const BatchPause = mongoose.model<IBatchPause, BatchPauseModel>(
  'BatchPause',
  batchPauseSchema
);
export default BatchPause;
