import mongoose, { Schema } from 'mongoose';
import { IUserDeadlineAdjustment } from '../interfaces/user-deadline-adjustment.interface';

const userDeadlineAdjustmentSchema = new Schema<IUserDeadlineAdjustment>(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User reference is required']
    },
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: [true, 'Batch reference is required']
    },
    
    // Global adjustments that apply to all submodules
    totalPauseDays: {
      type: Number,
      default: 0,
      min: [0, 'Total pause days cannot be negative']
    },
    
    // Track pause history for the 60-day limit
    pauseHistory: [{
      _id: false,
      startDate: {
        type: Date,
        required: true
      },
      endDate: {
        type: Date
      },
      daysUsed: {
        type: Number,
        required: true,
        min: 0
      },
      status: {
        type: String,
        enum: ['active', 'completed'],
        default: 'active'
      }
    }],
    
    // Submodule-specific adjustments (only when different from global)
    submoduleAdjustments: [{
      _id: false,
      submodule: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SubModule',
        required: true
      },
      additionalDays: {
        type: Number,
        default: 0
      },
      customStartDate: {
        type: Date
      },
      customDeadline: {
        type: Date
      },
      isExempt: {
        type: Boolean,
        default: false
      },
      reason: {
        type: String
      }
    }],
    
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Compound index for fast lookups
userDeadlineAdjustmentSchema.index({ user: 1, batch: 1 }, { unique: true });
userDeadlineAdjustmentSchema.index({ batch: 1 });
userDeadlineAdjustmentSchema.index({ 'submoduleAdjustments.submodule': 1 });

// Static method to get or create user adjustment record
userDeadlineAdjustmentSchema.statics.getOrCreateAdjustment = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  session?: mongoose.ClientSession
): Promise<IUserDeadlineAdjustment> {
  const model = this as mongoose.Model<IUserDeadlineAdjustment>;
  
  let adjustment = await model.findOne({
    user: userId,
    batch: batchId,
    isActive: true
  }).session(session || null);
  
  if (!adjustment) {
    adjustment = await model.create([{
      user: userId,
      batch: batchId,
      totalPauseDays: 0,
      pauseHistory: [],
      submoduleAdjustments: [],
      isActive: true
    }], { session });
    adjustment = adjustment[0];
  }
  
  return adjustment;
};

// Static method to apply batch pause
userDeadlineAdjustmentSchema.statics.applyBatchPause = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  pauseDays: number,
  pauseStartDate: Date,
  session?: mongoose.ClientSession
): Promise<{ success: boolean; totalPauseDays: number; remainingDays: number }> {
  const model = this as mongoose.Model<IUserDeadlineAdjustment>;
  
  const adjustment = await model.getOrCreateAdjustment(userId, batchId, session);
  
  // Check 60-day limit
  const totalUsedDays = adjustment.pauseHistory.reduce((sum, pause) => sum + pause.daysUsed, 0);
  const remainingDays = 60 - totalUsedDays;
  
  if (pauseDays > remainingDays) {
    return {
      success: false,
      totalPauseDays: adjustment.totalPauseDays,
      remainingDays
    };
  }
  
  // Add to pause history
  adjustment.pauseHistory.push({
    startDate: pauseStartDate,
    daysUsed: pauseDays,
    status: 'active'
  });
  
  // Update total pause days
  adjustment.totalPauseDays += pauseDays;
  
  await adjustment.save({ session });
  
  return {
    success: true,
    totalPauseDays: adjustment.totalPauseDays,
    remainingDays: remainingDays - pauseDays
  };
};

// Static method to resume batch pause early
userDeadlineAdjustmentSchema.statics.resumeBatchPause = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  actualDaysUsed: number,
  session?: mongoose.ClientSession
): Promise<{ success: boolean; daysAdjustedBack: number }> {
  const model = this as mongoose.Model<IUserDeadlineAdjustment>;
  
  const adjustment = await model.findOne({
    user: userId,
    batch: batchId,
    isActive: true
  }).session(session || null);
  
  if (!adjustment) {
    return { success: false, daysAdjustedBack: 0 };
  }
  
  // Find the active pause
  const activePause = adjustment.pauseHistory.find(pause => pause.status === 'active');
  if (!activePause) {
    return { success: false, daysAdjustedBack: 0 };
  }
  
  // Calculate days to adjust back
  const daysAdjustedBack = activePause.daysUsed - actualDaysUsed;
  
  // Update pause history
  activePause.daysUsed = actualDaysUsed;
  activePause.endDate = new Date();
  activePause.status = 'completed';
  
  // Update total pause days
  adjustment.totalPauseDays -= daysAdjustedBack;
  
  await adjustment.save({ session });
  
  return { success: true, daysAdjustedBack };
};

// Static method to add submodule-specific adjustment
userDeadlineAdjustmentSchema.statics.addSubmoduleAdjustment = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  submoduleId: mongoose.Types.ObjectId,
  adjustmentData: {
    additionalDays?: number;
    customStartDate?: Date;
    customDeadline?: Date;
    isExempt?: boolean;
    reason?: string;
  },
  session?: mongoose.ClientSession
): Promise<boolean> {
  const model = this as mongoose.Model<IUserDeadlineAdjustment>;
  
  const adjustment = await model.getOrCreateAdjustment(userId, batchId, session);
  
  // Remove existing adjustment for this submodule
  adjustment.submoduleAdjustments = adjustment.submoduleAdjustments.filter(
    adj => adj.submodule.toString() !== submoduleId.toString()
  );
  
  // Add new adjustment
  adjustment.submoduleAdjustments.push({
    submodule: submoduleId,
    additionalDays: adjustmentData.additionalDays || 0,
    customStartDate: adjustmentData.customStartDate,
    customDeadline: adjustmentData.customDeadline,
    isExempt: adjustmentData.isExempt || false,
    reason: adjustmentData.reason
  });
  
  await adjustment.save({ session });
  return true;
};

// Interface for static methods
interface UserDeadlineAdjustmentModel extends mongoose.Model<IUserDeadlineAdjustment> {
  getOrCreateAdjustment(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    session?: mongoose.ClientSession
  ): Promise<IUserDeadlineAdjustment>;
  
  applyBatchPause(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    pauseDays: number,
    pauseStartDate: Date,
    session?: mongoose.ClientSession
  ): Promise<{ success: boolean; totalPauseDays: number; remainingDays: number }>;
  
  resumeBatchPause(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    actualDaysUsed: number,
    session?: mongoose.ClientSession
  ): Promise<{ success: boolean; daysAdjustedBack: number }>;
  
  addSubmoduleAdjustment(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId,
    adjustmentData: {
      additionalDays?: number;
      customStartDate?: Date;
      customDeadline?: Date;
      isExempt?: boolean;
      reason?: string;
    },
    session?: mongoose.ClientSession
  ): Promise<boolean>;
}

const UserDeadlineAdjustment = mongoose.model<IUserDeadlineAdjustment, UserDeadlineAdjustmentModel>(
  'UserDeadlineAdjustment',
  userDeadlineAdjustmentSchema
);

export default UserDeadlineAdjustment;
