import mongoose, { Schema } from 'mongoose';
import { ISubmissionStorage } from '../interfaces/submission-storage.interface';

const submissionStorageSchema: Schema<ISubmissionStorage> = new Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'user',
      required: true
    },
    questionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'CodingProblem',
      required: true
    },
    submissionId: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'CodeSubmission',
        required: false
      }
    ],
    language: {
      type: String,
      required: true
    },
    hasSeenSolution: {
      type: Boolean,
      default: false
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed'],
      default: 'pending'
    },
    code: {
      type: String,
      required: true
    },
    date: {
      type: Date,
      default: Date.now
    },
    isExecuting: {
      type: Boolean,
      default: false
    }
  },
  { timestamps: true }
);

const SubmissionStorage = mongoose.model<ISubmissionStorage>(
  'SubmissionStorage',
  submissionStorageSchema
);

export default SubmissionStorage;
