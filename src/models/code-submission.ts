import mongoose, { Schema } from 'mongoose';
import { ICodeSubmission } from '../interfaces/code-submission.interface';

const codeSubmissionSchema: Schema<ICodeSubmission> = new Schema(
  {
    testcases: [
      {
        stdin: { type: String, required: true },
        stdout: { type: String, default: '' },
        expectedOutput: { type: String, required: true },
        passed: { type: Boolean, default: false },
        error: { type: String, default: null },
        executionTime: { type: Number, default: 0 }, // in milliseconds
        memoryUsage: { type: Number, default: 0 } // in kilobytes
      }
    ],
    date: { type: Date, default: Date.now },
    language: { type: String, required: true },
    code: { type: String, required: true },
    submissionStorageId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'submissionStorage',
      required: true
    }
  },
  { timestamps: true }
);

const CodeSubmission = mongoose.model<ICodeSubmission>(
  'CodeSubmission',
  codeSubmissionSchema
);

export default CodeSubmission;
