import mongoose, { Schema } from 'mongoose';
import {
  ICourseStructureVersion,
  IBatchDeadlineVersion,
  IUserAdjustmentVersion
} from '../interfaces/course-structure-version.interface';

// Course Structure Version Schema
const courseStructureVersionSchema = new Schema<ICourseStructureVersion>(
  {
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course',
      required: [true, 'Course reference is required']
    },
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: [true, 'Batch reference is required']
    },
    version: {
      type: Number,
      required: [true, 'Version number is required']
    },

    structure: {
      _id: false,
      modules: [
        {
          _id: false,
          moduleId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Module',
            required: true
          },
          order: {
            type: Number,
            required: true
          },
          title: {
            type: String,
            required: true
          },
          submodules: [
            {
              _id: false,
              submoduleId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'SubModule',
                required: true
              },
              order: {
                type: Number,
                required: true
              },
              title: {
                type: String,
                required: true
              },
              globalPosition: {
                type: Number,
                required: true
              },
              dependsOnPrevious: {
                type: Boolean,
                default: true
              }
            }
          ]
        }
      ],
      totalSubmodules: {
        type: Number,
        required: true
      }
    },

    changeType: {
      type: String,
      enum: [
        'module_reorder',
        'submodule_reorder',
        'module_add',
        'module_remove',
        'submodule_add',
        'submodule_remove'
      ],
      required: [true, 'Change type is required']
    },
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Changed by user is required']
    },
    changeReason: {
      type: String
    },
    changeDetails: {
      _id: false,
      affectedModules: [
        {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Module'
        }
      ],
      affectedSubmodules: [
        {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'SubModule'
        }
      ],
      previousPositions: [
        {
          _id: false,
          id: {
            type: mongoose.Schema.Types.ObjectId,
            required: true
          },
          type: {
            type: String,
            enum: ['module', 'submodule'],
            required: true
          },
          oldPosition: {
            type: Number,
            required: true
          },
          newPosition: {
            type: Number,
            required: true
          }
        }
      ]
    },

    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Batch Deadline Version Schema
const batchDeadlineVersionSchema = new Schema<IBatchDeadlineVersion>(
  {
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: [true, 'Batch reference is required']
    },
    submodule: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SubModule',
      required: [true, 'Submodule reference is required']
    },
    version: {
      type: Number,
      required: [true, 'Version number is required']
    },

    deadlineConfig: {
      _id: false,
      startDate: {
        type: Date,
        required: true
      },
      deadline: {
        type: Date,
        required: true
      },
      dependsOnPreviousSubmodule: {
        type: Boolean,
        default: true
      },
      penaltyRules: [
        {
          _id: false,
          daysLate: {
            type: Number,
            required: true
          },
          penaltyPercentage: {
            type: Number,
            required: true
          }
        }
      ]
    },

    changeType: {
      type: String,
      enum: ['deadline_update', 'penalty_rules_update', 'dependency_update'],
      required: [true, 'Change type is required']
    },
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Changed by user is required']
    },
    changeReason: {
      type: String
    },
    previousDeadline: {
      type: Date
    },
    newDeadline: {
      type: Date
    },

    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// User Adjustment Version Schema
const userAdjustmentVersionSchema = new Schema<IUserAdjustmentVersion>(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User reference is required']
    },
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: [true, 'Batch reference is required']
    },
    version: {
      type: Number,
      required: [true, 'Version number is required']
    },

    adjustmentConfig: {
      _id: false,
      totalPauseDays: {
        type: Number,
        default: 0
      },
      pauseHistory: [
        {
          _id: false,
          startDate: {
            type: Date,
            required: true
          },
          endDate: {
            type: Date
          },
          daysUsed: {
            type: Number,
            required: true
          },
          status: {
            type: String,
            enum: ['active', 'completed'],
            required: true
          }
        }
      ],
      submoduleAdjustments: [
        {
          _id: false,
          submodule: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'SubModule',
            required: true
          },
          additionalDays: {
            type: Number,
            default: 0
          },
          customStartDate: {
            type: Date
          },
          customDeadline: {
            type: Date
          },
          isExempt: {
            type: Boolean,
            default: false
          },
          reason: {
            type: String
          }
        }
      ]
    },

    changeType: {
      type: String,
      enum: ['pause_applied', 'pause_resumed', 'custom_adjustment', 'exemption_granted'],
      required: [true, 'Change type is required']
    },
    changeReason: {
      type: String
    },

    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Indexes
courseStructureVersionSchema.index({ course: 1, batch: 1, version: 1 }, { unique: true });
courseStructureVersionSchema.index({ course: 1, batch: 1, createdAt: -1 });

batchDeadlineVersionSchema.index({ batch: 1, submodule: 1, version: 1 }, { unique: true });
batchDeadlineVersionSchema.index({ batch: 1, submodule: 1, createdAt: -1 });

userAdjustmentVersionSchema.index({ user: 1, batch: 1, version: 1 }, { unique: true });
userAdjustmentVersionSchema.index({ user: 1, batch: 1, createdAt: -1 });

// Static methods for CourseStructureVersion
courseStructureVersionSchema.statics.createVersion = async function (
  courseId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  structure: any,
  changeType: string,
  changedBy: mongoose.Types.ObjectId,
  changeDetails: any,
  changeReason?: string
): Promise<ICourseStructureVersion> {
  const model = this as mongoose.Model<ICourseStructureVersion>;

  // Get next version number
  const latestVersion = await model
    .findOne({ course: courseId, batch: batchId })
    .sort({ version: -1 })
    .lean();

  const nextVersion = latestVersion ? latestVersion.version + 1 : 1;

  return await model.create({
    course: courseId,
    batch: batchId,
    version: nextVersion,
    structure,
    changeType,
    changedBy,
    changeReason,
    changeDetails,
    isActive: true
  });
};

// Static methods for BatchDeadlineVersion
batchDeadlineVersionSchema.statics.createVersion = async function (
  batchId: mongoose.Types.ObjectId,
  submoduleId: mongoose.Types.ObjectId,
  deadlineConfig: any,
  changeType: string,
  changedBy: mongoose.Types.ObjectId,
  changeReason?: string,
  previousDeadline?: Date,
  newDeadline?: Date
): Promise<IBatchDeadlineVersion> {
  const model = this as mongoose.Model<IBatchDeadlineVersion>;

  // Get next version number
  const latestVersion = await model
    .findOne({ batch: batchId, submodule: submoduleId })
    .sort({ version: -1 })
    .lean();

  const nextVersion = latestVersion ? latestVersion.version + 1 : 1;

  return await model.create({
    batch: batchId,
    submodule: submoduleId,
    version: nextVersion,
    deadlineConfig,
    changeType,
    changedBy,
    changeReason,
    previousDeadline,
    newDeadline,
    isActive: true
  });
};

// Static methods for UserAdjustmentVersion
userAdjustmentVersionSchema.statics.createVersion = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  adjustmentConfig: any,
  changeType: string,
  changeReason?: string
): Promise<IUserAdjustmentVersion> {
  const model = this as mongoose.Model<IUserAdjustmentVersion>;

  // Get next version number
  const latestVersion = await model
    .findOne({ user: userId, batch: batchId })
    .sort({ version: -1 })
    .lean();

  const nextVersion = latestVersion ? latestVersion.version + 1 : 1;

  return await model.create({
    user: userId,
    batch: batchId,
    version: nextVersion,
    adjustmentConfig,
    changeType,
    changeReason,
    isActive: true
  });
};

// Create models
const CourseStructureVersion = mongoose.model<ICourseStructureVersion>(
  'CourseStructureVersion',
  courseStructureVersionSchema
);

const BatchDeadlineVersion = mongoose.model<IBatchDeadlineVersion>(
  'BatchDeadlineVersion',
  batchDeadlineVersionSchema
);

const UserAdjustmentVersion = mongoose.model<IUserAdjustmentVersion>(
  'UserAdjustmentVersion',
  userAdjustmentVersionSchema
);

export { CourseStructureVersion, BatchDeadlineVersion, UserAdjustmentVersion };
