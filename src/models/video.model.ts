import mongoose, { Schema } from 'mongoose';
import { IVideo } from '../interfaces/video.interface';

const videoSchema = new Schema(
  {
    title: {
      type: String,
      required: [true, 'Video title is required'],
      trim: true
    },
    description: {
      type: String,
      required: [true, 'Video description is required']
    },
    videoId: {
      type: String,
      required: [true, 'Video ID is required'],
      unique: true
    },
    provider: {
      type: String,
      default: 'vdocipher' // Default provider, can be changed in the future
    },
    duration: {
      type: Number
    },
    points: {
      type: Number,
      required: [true, 'Points are required'],
      min: [0, 'Points cannot be negative'],
      default: 1
    },
    tags: [
      {
        type: String,
        trim: true
      }
    ],
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Create indexes for common queries
videoSchema.index({ provider: 1 });
videoSchema.index({ tags: 1 });
videoSchema.index({ points: 1 });

const Video = mongoose.model<IVideo>('Video', videoSchema);
export default Video;
