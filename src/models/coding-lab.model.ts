import mongoose, { Schema, Document } from 'mongoose';
import { ICodingLab } from '../interfaces/coding-lab.interface';

const codingLabSchema = new Schema(
  {
    title: {
      type: String,
      required: [true, 'Lab title is required'],
      trim: true
    },
    description: {
      type: String,
      required: [true, 'Lab description is required']
    },
    instructions: {
      type: String,
      required: [true, 'Lab instructions are required']
    },
    boilerplate: [
      {
        language: {
          type: String,
          required: true
        },
        code: {
          type: String,
          required: true
        },
        compilerId: {
          type: String,
          required: true
        }
      }
    ],
    testCases: [
      {
        input: {
          type: String,
          required: true
        },
        expectedOutput: {
          type: String,
          required: true
        },
        isHidden: {
          type: Boolean,
          default: false
        },
        points: {
          type: Number,
          required: true,
          min: [0, 'Points cannot be negative'],
          default: 1
        }
      }
    ],
    points: {
      type: Number,
      required: [true, 'Total points are required'],
      min: [0, 'Total points cannot be negative']
    },
    solutions: [
      {
        language: {
          type: String,
          required: true
        },
        code: {
          type: String,
          required: true
        },
        explanation: {
          type: String,
          required: true
        }
      }
    ],
    tags: [
      {
        type: String,
        trim: true
      }
    ],
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Calculate total points from test cases
codingLabSchema.pre('validate', function (this: ICodingLab & Document, next) {
  if (this.testCases && this.testCases.length > 0) {
    const calculatedTotal = this.testCases.reduce(
      (sum, testCase) => sum + (testCase.points || 0),
      0
    );

    // If points is not set or doesn't match calculated total, update it
    if (!this.points || this.points !== calculatedTotal) {
      this.points = calculatedTotal;
    }
  }
  next();
});

// Create indexes for common queries
codingLabSchema.index({ tags: 1 });
codingLabSchema.index({ points: 1 });
codingLabSchema.index({ 'boilerplate.language': 1 });
codingLabSchema.index({ 'solutions.language': 1 });

const CodingLab = mongoose.model<ICodingLab>('CodingLab', codingLabSchema);
export default CodingLab;
