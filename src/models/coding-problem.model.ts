import mongoose, { Schema, Document } from 'mongoose';
import { ICodingProblem } from '../interfaces/coding-problem.interface';

const codingProblemSchema = new Schema(
  {
    problemId: {
      type: String,
      required: [true, 'Problem ID is required'],
      unique: true,
      trim: true
    },
    title: {
      type: String,
      required: [true, 'Problem title is required'],
      trim: true
    },
    description: {
      type: String,
      required: [true, 'Problem description is required']
    },
    boilerplate: [
      {
        _id: false,
        language: {
          type: String,
          required: true
        },
        code: {
          type: String,
          required: true
        },
        compilerId: {
          type: String,
          required: true
        },
        mainCode: {
          type: String,
          required: true
        }
      }
    ],
    testCases: [
      {
        _id: false,
        input: {
          type: String,
          required: true
        },
        expectedOutput: {
          type: String,
          required: true
        },
        isHidden: {
          type: Boolean,
          default: false
        },
        points: {
          type: Number,
          required: true,
          min: [0, 'Points cannot be negative'],
          default: 1
        }
      }
    ],
    points: {
      type: Number,
      required: [true, 'Total points are required'],
      min: [0, 'Total points cannot be negative']
    },
    difficulty: {
      type: String,
      enum: ['easy', 'medium', 'hard'],
      required: [true, 'Difficulty level is required']
    },
    hints: [
      {
        _id: false,
        language: {
          type: String,
          required: true
        },
        text: String,
        image: String, // ImageKit URL
        video: String // VDOCipher ID
      }
    ],
    solutions: [
      {
        _id: false,
        language: {
          type: String,
          required: true
        },
        code: {
          type: String,
          required: true
        },
        editorial: String,
        video: String // VDOCipher ID
      }
    ],
    tags: [
      {
        type: String,
        trim: true
      }
    ],
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Calculate total points from test cases
codingProblemSchema.pre(
  'validate',
  function (this: ICodingProblem & Document, next) {
    if (this.testCases && this.testCases.length > 0) {
      const calculatedTotal = this.testCases.reduce(
        (sum, testCase) => sum + (testCase.points || 0),
        0
      );

      // If points is not set or doesn't match calculated total, update it
      if (!this.points || this.points !== calculatedTotal) {
        this.points = calculatedTotal;
      }
    }
    next();
  }
);

// Create indexes for common queries
codingProblemSchema.index({ difficulty: 1 });
codingProblemSchema.index({ tags: 1 });
codingProblemSchema.index({ points: 1 });
codingProblemSchema.index({ 'boilerplate.language': 1 });
codingProblemSchema.index({ 'solutions.language': 1 });
codingProblemSchema.index({ 'hints.language': 1 });

const CodingProblem = mongoose.model<ICodingProblem>(
  'CodingProblem',
  codingProblemSchema
);
export default CodingProblem;
