import mongoose, { Schema } from 'mongoose';
import { IBatchSubmoduleDeadline } from '../interfaces/batch-submodule-deadline.interface';

const batchSubmoduleDeadlineSchema = new Schema(
  {
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: [true, 'Batch reference is required']
    },
    submodule: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SubModule',
      required: [true, 'SubModule reference is required']
    },
    startDate: {
      type: Date,
      required: [true, 'Start date is required']
    },
    deadline: {
      type: Date,
      required: [true, 'Deadline is required']
    },
    dependsOnPreviousSubmodule: {
      type: Boolean,
      default: true
    },
    penaltyRules: [
      {
        _id: false,
        daysLate: {
          type: Number,
          required: true
        },
        penaltyPercentage: {
          type: Number,
          required: true
        }
      }
    ],
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Create compound index for batch and submodule
batchSubmoduleDeadlineSchema.index(
  { batch: 1, submodule: 1 },
  { unique: true }
);

const BatchSubmoduleDeadline = mongoose.model<IBatchSubmoduleDeadline>(
  'BatchSubmoduleDeadline',
  batchSubmoduleDeadlineSchema
);

export default BatchSubmoduleDeadline;
