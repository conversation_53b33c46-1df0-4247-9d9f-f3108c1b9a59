import mongoose, { Schema } from 'mongoose';
import { ISubmoduleContent } from '../interfaces/submodule-content.interface';

const submoduleContentSchema = new Schema(
  {
    submodule: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SubModule',
      required: [true, 'Submodule reference is required']
    },
    contentType: {
      type: String,
      enum: ['video', 'note', 'mcq', 'coding_problem', 'coding_lab'],
      required: [true, 'Content type is required']
    },
    contentId: {
      type: mongoose.Schema.Types.ObjectId,
      required: [true, 'Content ID is required'],
      refPath: 'contentModel'
    },
    contentModel: {
      type: String,
      required: [true, 'Content model is required'],
      enum: ['Video', 'Note', 'MCQ', 'CodingProblem', 'CodingLab']
    },
    section: {
      type: String,
      enum: ['lesson', 'practice'],
      required: [true, 'Section is required']
    },
    order: {
      type: Number,
      required: [true, 'Order is required']
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Create indexes for common queries
submoduleContentSchema.index({ submodule: 1, section: 1, order: 1 });
submoduleContentSchema.index({ contentType: 1, contentId: 1 });

// Helper method to map contentType to contentModel
submoduleContentSchema.pre('validate', function (next) {
  const contentTypeToModel: Record<string, string> = {
    video: 'Video',
    note: 'Note',
    mcq: 'MCQ',
    coding_problem: 'CodingProblem',
    coding_lab: 'CodingLab'
  };

  // Use type assertion to access document properties
  const doc = this as unknown as {
    contentType?: string;
    contentModel?: string;
  };

  if (doc.contentType && contentTypeToModel[doc.contentType]) {
    doc.contentModel = contentTypeToModel[doc.contentType];
  }

  next();
});

const SubmoduleContent = mongoose.model<ISubmoduleContent>(
  'SubmoduleContent',
  submoduleContentSchema
);
export default SubmoduleContent;
