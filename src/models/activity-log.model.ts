import mongoose, { Schema } from 'mongoose';
import { IActivityLog } from '../interfaces/activity-log.interface';

// Define the schema for the activity log
const ActivityLogSchema = new Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    userName: {
      type: String,
      required: true
    },
    action: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: [true, 'Description is required']
    },
    details: {
      type: Schema.Types.Mixed
    },
    ipAddress: {
      type: String
    },
    userAgent: {
      type: String
    }
  },
  {
    timestamps: true
  }
);
ActivityLogSchema.index({ userId: 1 });
ActivityLogSchema.index({ action: 1 });

const ActivityLog = mongoose.model<IActivityLog>(
  'ActivityLog',
  ActivityLogSchema
);
export default ActivityLog;
