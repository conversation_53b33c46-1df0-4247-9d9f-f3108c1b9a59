import mongoose, { Schema, Document } from 'mongoose';
import { IMCQ } from '../interfaces/mcq.interface';

const mcqSchema = new Schema(
  {
    title: {
      type: String,
      required: [true, 'MCQ title is required'],
      trim: true
    },
    question: {
      type: String,
      required: [true, 'Question is required']
    },
    image: {
      type: String // ImageKit URL
    },
    codeSnippets: [
      {
        _id: false,
        language: {
          type: String,
          required: true
        },
        code: {
          type: String,
          required: true
        }
      }
    ],
    options: [
      {
        _id: false,
        text: {
          type: String,
          required: true
        },
        isCorrect: {
          type: Boolean,
          required: true
        }
      }
    ],
    selectionType: {
      type: String,
      enum: ['single', 'multiple'],
      required: [true, 'Selection type is required'],
      default: 'single'
    },
    points: {
      type: Number,
      required: [true, 'Points are required'],
      min: [0, 'Points cannot be negative'],
      default: 1
    },
    explanation: {
      text: String,
      video: String, // VDOCipher ID
      image: String // ImageKit URL
    },
    difficulty: {
      type: String,
      enum: ['easy', 'medium', 'hard'],
      required: [true, 'Difficulty level is required']
    },
    tags: [
      {
        _id: false,
        type: String,
        trim: true
      }
    ],
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Validation to ensure single selection type has exactly one correct answer
mcqSchema.pre('validate', function (this: IMCQ & Document, next) {
  if (this.selectionType === 'single') {
    const correctCount = this.options.filter(option => option.isCorrect).length;
    if (correctCount !== 1) {
      const error = new Error(
        'Single selection MCQs must have exactly one correct answer'
      );
      return next(error);
    }
  } else if (this.selectionType === 'multiple') {
    const correctCount = this.options.filter(option => option.isCorrect).length;
    if (correctCount < 1) {
      const error = new Error(
        'Multiple selection MCQs must have at least one correct answer'
      );
      return next(error);
    }
  }
  next();
});

// Create indexes for common queries
mcqSchema.index({ difficulty: 1 });
mcqSchema.index({ tags: 1 });
mcqSchema.index({ points: 1 });
mcqSchema.index({ selectionType: 1 });

const MCQ = mongoose.model<IMCQ>('MCQ', mcqSchema);
export default MCQ;
