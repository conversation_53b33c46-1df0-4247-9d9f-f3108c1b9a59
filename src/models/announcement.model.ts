import mongoose, { Schema } from 'mongoose';
import { IAnnouncement } from '../interfaces/announcement.interface';

const announcementSchema: Schema<IAnnouncement> = new Schema(
  {
    title: {
      type: String,
      required: [true, 'Announcement title is required'],
      trim: true
    },
    content: {
      type: String,
      required: [true, 'Announcement content is required']
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Author reference is required']
    },
    authorName: {
      type: String,
      required: [true, 'Author name is required']
    },
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course'
    },
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch'
    },
    module: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Module'
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    attachments: [
      {
        _id: false,
        name: {
          type: String,
          required: true
        },
        url: {
          type: String,
          required: true
        },
        type: {
          type: String,
          required: true
        }
      }
    ],
    isActive: {
      type: Boolean,
      default: true
    },
    expiresAt: {
      type: Date
    },
    readBy: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    ]
  },
  { timestamps: true }
);

// Create indexes for common queries
announcementSchema.index({ course: 1, batch: 1, createdAt: -1 });
announcementSchema.index({ priority: 1 });
announcementSchema.index({ isActive: 1 });

const Announcement = mongoose.model<IAnnouncement>(
  'Announcement',
  announcementSchema
);
export default Announcement;
