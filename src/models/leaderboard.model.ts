import mongoose, { Schema } from 'mongoose';
import { ILeaderboard } from '../interfaces/leaderboard.interface';

const leaderboardSchema: Schema<ILeaderboard> = new Schema(
  {
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course',
      required: [true, 'Course reference is required']
    },
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: [true, 'Batch reference is required']
    },
    period: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'overall'],
      required: [true, 'Leaderboard period is required']
    },
    startDate: {
      type: Date,
      required: [true, 'Start date is required']
    },
    endDate: {
      type: Date,
      required: [true, 'End date is required']
    },
    rankings: [
      {
        _id: false,
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true
        },
        userName: {
          type: String,
          required: true
        },
        userAvatar: {
          type: String
        },
        rank: {
          type: Number,
          required: true
        },
        points: {
          type: Number,
          required: true
        },
        progress: {
          type: Number,
          required: true
        },
        streak: {
          type: Number,
          default: 0
        }
      }
    ],
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  { timestamps: true }
);

// Create indexes for common queries
leaderboardSchema.index({ course: 1, batch: 1, period: 1 });
leaderboardSchema.index({ period: 1, startDate: 1, endDate: 1 });

const Leaderboard = mongoose.model<ILeaderboard>(
  'Leaderboard',
  leaderboardSchema
);
export default Leaderboard;
