import mongoose, { Schema } from 'mongoose';
import { IDoubt } from '../interfaces/doubt.interface';

const doubtSchema: Schema<IDoubt> = new Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User reference is required']
    },
    userName: {
      type: String,
      required: [true, 'User name is required']
    },
    userAvatar: {
      type: String
    },
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course',
      required: [true, 'Course reference is required']
    },
    module: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Module'
    },
    subModule: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SubModule'
    },

    // Content reference
    contentType: {
      type: String,
      enum: ['video', 'note', 'mcq', 'coding_problem', 'coding_lab']
    },
    contentId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'contentModel'
    },
    contentModel: {
      type: String,
      enum: ['Video', 'Note', 'MCQ', 'CodingProblem', 'CodingLab']
    },
    title: {
      type: String,
      required: [true, 'Doubt title is required'],
      trim: true
    },
    description: {
      type: String,
      required: [true, 'Doubt description is required']
    },
    status: {
      type: String,
      enum: ['open', 'in_progress', 'resolved', 'closed'],
      default: 'open'
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    attachments: [
      {
        _id: false,
        name: {
          type: String,
          required: true
        },
        url: {
          type: String,
          required: true
        },
        type: {
          type: String,
          required: true
        }
      }
    ],
    codeSnippet: {
      type: String
    },
    responses: [
      {
        _id: false,
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
          required: true
        },
        userName: {
          type: String,
          required: true
        },
        userAvatar: {
          type: String
        },
        userRole: {
          type: String,
          required: true
        },
        content: {
          type: String,
          required: true
        },
        attachments: [
          {
            _id: false,
            name: {
              type: String,
              required: true
            },
            url: {
              type: String,
              required: true
            },
            type: {
              type: String,
              required: true
            }
          }
        ],
        codeSnippet: {
          type: String
        },
        createdAt: {
          type: Date,
          default: Date.now
        },
        isAnswer: {
          type: Boolean,
          default: false
        }
      }
    ],
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    resolvedAt: {
      type: Date
    }
  },
  { timestamps: true }
);

// Helper method to map contentType to contentModel
doubtSchema.pre('validate', function (next) {
  const contentTypeToModel: Record<string, string> = {
    video: 'Video',
    note: 'Note',
    mcq: 'MCQ',
    coding_problem: 'CodingProblem',
    coding_lab: 'CodingLab'
  };

  // Use type assertion to access document properties
  const doc = this as unknown as {
    contentType?: string;
    contentModel?: string;
  };

  // Handle new content structure
  if (doc.contentType && contentTypeToModel[doc.contentType]) {
    doc.contentModel = contentTypeToModel[doc.contentType];
  }

  next();
});

// Create indexes for common queries
doubtSchema.index({ user: 1 });
doubtSchema.index({ course: 1 });
doubtSchema.index({ status: 1 });
doubtSchema.index({ priority: 1 });
doubtSchema.index({ assignedTo: 1 });
doubtSchema.index({ contentType: 1, contentId: 1 });

const Doubt = mongoose.model<IDoubt>('Doubt', doubtSchema);
export default Doubt;
