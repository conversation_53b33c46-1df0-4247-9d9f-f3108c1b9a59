import mongoose, { Schema } from 'mongoose';
import { INote } from '../interfaces/note.interface';

const noteSchema = new Schema(
  {
    title: {
      type: String,
      required: [true, 'Note title is required'],
      trim: true
    },
    content: {
      type: String,
      required: [true, 'Note content is required']
    },
    images: [
      {
        type: String // ImageKit URLs
      }
    ],
    points: {
      type: Number,
      required: [true, 'Points are required'],
      min: [0, 'Points cannot be negative'],
      default: 1
    },
    tags: [
      {
        type: String,
        trim: true
      }
    ],
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Create indexes for common queries
noteSchema.index({ tags: 1 });
noteSchema.index({ points: 1 });

const Note = mongoose.model<INote>('Note', noteSchema);
export default Note;
