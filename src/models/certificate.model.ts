import mongoose, { Schema } from 'mongoose';
import { ICertificate } from '../interfaces/certificate.interface';

const certificateSchema: Schema<ICertificate> = new Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User reference is required']
    },
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course',
      required: [true, 'Course reference is required']
    },
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: [true, 'Batch reference is required']
    },
    type: {
      type: String,
      enum: ['progress', 'excellence'],
      required: [true, 'Certificate type is required']
    },
    certificateId: {
      type: String,
      required: [true, 'Certificate ID is required'],
      unique: true
    },
    issueDate: {
      type: Date,
      default: Date.now
    },
    progress: {
      type: Number,
      required: [true, 'Progress percentage is required']
    },
    certificateUrl: {
      type: String,
      required: [true, 'Certificate URL is required']
    },
    metadata: {
      _id: false,
      userName: {
        type: String,
        required: true
      },
      courseName: {
        type: String,
        required: true
      },
      batchName: {
        type: String,
        required: true
      },
      instructorName: {
        type: String,
        required: true
      }
    }
  },
  { timestamps: true }
);

// Create indexes for common queries
certificateSchema.index({ user: 1, course: 1, type: 1 });

const Certificate = mongoose.model<ICertificate>(
  'Certificate',
  certificateSchema
);
export default Certificate;
