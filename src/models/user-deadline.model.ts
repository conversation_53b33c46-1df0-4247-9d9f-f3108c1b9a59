import mongoose, { Schema } from 'mongoose';
import { IUserDeadline } from '../interfaces/user-deadline.interface';
import batchSubmoduleDeadlineModel from './batch-submodule-deadline.model';
const userDeadlineSchema = new Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User reference is required']
    },
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: [true, 'Batch reference is required']
    },
    submodule: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SubModule',
      required: [true, 'SubModule reference is required']
    },
    originalDeadline: {
      type: Date,
      required: [true, 'Original deadline is required']
    },
    adjustedDeadline: {
      type: Date,
      required: [true, 'Adjusted deadline is required']
    },
    adjustedStartDate: {
      type: Date,
      required: [true, 'Adjusted start date is required']
    },
    pauseAdjustmentDays: {
      type: Number,
      default: 0
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Create compound index for user, batch, and submodule
userDeadlineSchema.index({ user: 1, batch: 1, submodule: 1 }, { unique: true });

// Static method to adjust deadlines for a user when they pause a batch
userDeadlineSchema.statics.adjustDeadlinesForPause = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  pauseDays: number,
  pauseStartDate: Date
): Promise<number> {
  try {
    const model = this as mongoose.Model<IUserDeadline>;

    // Find all upcoming deadlines for this user and batch
    // We consider a deadline as "upcoming" if it's after the pause start date
    const upcomingDeadlines = await model.find({
      user: userId,
      batch: batchId,
      adjustedDeadline: { $gte: pauseStartDate },
      isActive: true
    });

    // Shift each deadline by the pause duration
    const updatePromises = upcomingDeadlines.map(deadline => {
      // Calculate new dates by adding pause days
      const newStartDate = new Date(deadline.adjustedStartDate);
      newStartDate.setDate(newStartDate.getDate() + pauseDays);

      const newDeadline = new Date(deadline.adjustedDeadline);
      newDeadline.setDate(newDeadline.getDate() + pauseDays);

      // Update the deadline
      return model.updateOne(
        { _id: deadline._id },
        {
          $set: {
            adjustedStartDate: newStartDate,
            adjustedDeadline: newDeadline,
            pauseAdjustmentDays: deadline.pauseAdjustmentDays + pauseDays
          }
        }
      );
    });

    // Execute all updates
    const results = await Promise.all(updatePromises);

    // Count the number of modified documents
    const modifiedCount = results.reduce(
      (count, result) => count + result.modifiedCount,
      0
    );

    return modifiedCount;
  } catch (error: unknown) {
    console.error('Error adjusting deadlines for pause:', error);
    return 0;
  }
};

// Static method to initialize user deadlines from batch deadlines
userDeadlineSchema.statics.initializeUserDeadlines = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  session: mongoose.ClientSession
): Promise<number> {
  try {
    const model = this as mongoose.Model<IUserDeadline>;

    // Import the BatchSubmoduleDeadline model with interface
    interface IBatchSubmoduleDeadlineDoc {
      _id: mongoose.Types.ObjectId;
      batch: mongoose.Types.ObjectId;
      submodule: mongoose.Types.ObjectId;
      startDate: Date;
      deadline: Date;
      isActive: boolean;
    }

    // const BatchSubmoduleDeadline = mongoose.model('BatchSubmoduleDeadline',batchSubmoduleDeadlineSchema);

    // Get all batch deadlines
    const batchDeadlinesRaw = await batchSubmoduleDeadlineModel
      .find({
        batch: batchId,
        isActive: true
      })
      .session(session)
      .lean();

    // Convert to the expected type with proper type safety
    const batchDeadlines =
      batchDeadlinesRaw as unknown as IBatchSubmoduleDeadlineDoc[];

    // Create user deadlines for each batch deadline
    const userDeadlines = batchDeadlines.map(batchDeadline => ({
      user: userId,
      batch: batchId,
      submodule: batchDeadline.submodule,
      originalDeadline: batchDeadline.deadline,
      adjustedDeadline: batchDeadline.deadline,
      adjustedStartDate: batchDeadline.startDate,
      pauseAdjustmentDays: 0,
      isActive: true
    }));

    // Insert all user deadlines
    if (userDeadlines.length > 0) {
      await model.insertMany(userDeadlines, { ordered: false, session });
    }

    return userDeadlines.length;
  } catch (error: unknown) {
    // If error is due to duplicate keys, some deadlines might already exist
    if (
      error &&
      typeof error === 'object' &&
      'code' in error &&
      error.code === 11000
    ) {
      return 0;
    }

    console.error('Error initializing user deadlines:', error);
    return 0;
  }
};

// Static method to adjust deadlines when a student resumes a batch pause early
userDeadlineSchema.statics.adjustDeadlinesForEarlyResumption = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  daysToAdjustBack: number
): Promise<number> {
  try {
    const model = this as mongoose.Model<IUserDeadline>;

    // Find all deadlines for this user and batch
    const userDeadlines = await model.find({
      user: userId,
      batch: batchId,
      isActive: true
    });

    // Shift each deadline back by the unused pause days
    const updatePromises = userDeadlines.map(deadline => {
      // Calculate new dates by subtracting unused days
      const newStartDate = new Date(deadline.adjustedStartDate);
      newStartDate.setDate(newStartDate.getDate() - daysToAdjustBack);

      const newDeadline = new Date(deadline.adjustedDeadline);
      newDeadline.setDate(newDeadline.getDate() - daysToAdjustBack);

      // Update the deadline
      return model.updateOne(
        { _id: deadline._id },
        {
          $set: {
            adjustedStartDate: newStartDate,
            adjustedDeadline: newDeadline,
            pauseAdjustmentDays: Math.max(
              0,
              deadline.pauseAdjustmentDays - daysToAdjustBack
            )
          }
        }
      );
    });

    // Execute all updates
    const results = await Promise.all(updatePromises);

    // Count the number of modified documents
    const modifiedCount = results.reduce(
      (count, result) => count + result.modifiedCount,
      0
    );

    return modifiedCount;
  } catch (error: unknown) {
    console.error('Error adjusting deadlines for early resumption:', error);
    return 0;
  }
};

// Update the interface to include the static methods
interface UserDeadlineModel extends mongoose.Model<IUserDeadline> {
  adjustDeadlinesForPause(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    pauseDays: number,
    pauseStartDate: Date
  ): Promise<number>;

  adjustDeadlinesForEarlyResumption(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    daysToAdjustBack: number
  ): Promise<number>;

  initializeUserDeadlines(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    session?: mongoose.ClientSession
  ): Promise<number>;
}

const UserDeadline = mongoose.model<IUserDeadline, UserDeadlineModel>(
  'UserDeadline',
  userDeadlineSchema
);

export default UserDeadline;
