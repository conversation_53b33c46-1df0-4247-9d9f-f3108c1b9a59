import mongoose, { Schema } from 'mongoose';
import { IUserStreak } from '../interfaces/user-streak.interface';

const userStreakSchema: Schema<IUserStreak> = new Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User reference is required']
    },
    currentStreak: {
      type: Number,
      default: 0
    },
    longestStreak: {
      type: Number,
      default: 0
    },
    lastActivityDate: {
      type: Date,
      default: Date.now
    },
    streakHistory: [
      {
        _id: false,
        date: {
          type: Date,
          required: true
        },
        pointsEarned: {
          type: Number,
          default: 0
        },
        activities: [
          {
            _id: false,
            // NOTE: Always derive 'type', 'contentType', etc. for activities from the corresponding contentItemProgress entry in user-progress.model.ts. Do NOT generate these independently here.
            type: {
              type: String,
              enum: ['lesson', 'practice'],
              required: true
            },
            contentType: {
              type: String,
              enum: ['video', 'note', 'mcq', 'coding_problem', 'coding_lab'],
              required: true
            },
            entityId: {
              type: mongoose.Schema.Types.ObjectId
            },
            entityType: {
              type: String
            },
            pointsEarned: {
              type: Number,
              default: 0
            },
            timestamp: {
              type: Date,
              default: Date.now
            }
          }
        ]
      }
    ],
    totalDaysActive: {
      type: Number,
      default: 0
    }
  },
  { timestamps: true }
);

// Create indexes for common queries
userStreakSchema.index({ user: 1 }, { unique: true });
userStreakSchema.index({ currentStreak: -1 });
userStreakSchema.index({ longestStreak: -1 });

const UserStreak = mongoose.model<IUserStreak>('UserStreak', userStreakSchema);
export default UserStreak;
