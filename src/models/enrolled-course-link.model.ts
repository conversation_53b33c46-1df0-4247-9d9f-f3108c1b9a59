import mongoose, { Schema } from 'mongoose';
import {
  IEnrolledCourseLink,
  IEnrolledCourseLinkStatus
} from '../interfaces/enrolled-course-link.interface';

const enrolledCourseLinkSchema = new Schema<IEnrolledCourseLink>(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'user',
      required: [true, 'User ID is required']
    },
    enrollmentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'enrollment'
    },
    courseId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'course',
      required: [true, 'Course ID is required']
    },
    batchId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'batch',
      required: [true, 'Batch ID is required']
    },
    courseTitle: {
      type: String,
      required: [true, 'Course title is required']
    },
    purchaseDate: {
      type: Date,
      required: [true, 'Purchase date is required']
    },
    expiryDate: {
      type: Date
    },
    status: {
      type: String,
      enum: Object.values(IEnrolledCourseLinkStatus),
      default: IEnrolledCourseLinkStatus.INACTIVE
    },
    discord: {
      _id: false,
      channel_id: {
        type: String,
        required: false
      },
      role_id: {
        type: String,
        required: false
      }
    },
    pausedUntil: {
      type: Date,
      default: null
    },
    lastActivity: {
      type: Date,
      default: Date.now
    },
    // Add this to the schema
    lastViewedContent: {
      type: {
        contentId: {
          type: mongoose.Schema.Types.ObjectId,
          required: true
        },
        submoduleId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'SubModule',
          required: true
        },
        moduleId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Module',
          required: true
        },
        title: {
          type: String
        },
        viewedAt: {
          type: Date,
          default: Date.now
        }
      },
      _id: false,
      required: false
    }
  },
  { timestamps: true }
);

enrolledCourseLinkSchema.index({ userId: 1, courseId: 1 });
enrolledCourseLinkSchema.index({ userId: 1, batchId: 1 });

const EnrolledCourseLink = mongoose.model<IEnrolledCourseLink>(
  'EnrolledCourseLink',
  enrolledCourseLinkSchema
);

export default EnrolledCourseLink;
