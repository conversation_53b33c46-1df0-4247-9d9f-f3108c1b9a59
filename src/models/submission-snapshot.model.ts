import mongoose, { Schem<PERSON> } from 'mongoose';
import { ISubmissionSnapshot } from '../interfaces/submission-snapshot.interface';

const submissionSnapshotSchema = new Schema<ISubmissionSnapshot>(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User reference is required']
    },
    batch: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Batch',
      required: [true, 'Batch reference is required']
    },
    submodule: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SubModule',
      required: [true, 'Submodule reference is required']
    },
    contentId: {
      type: mongoose.Schema.Types.ObjectId,
      required: [true, 'Content ID is required']
    },
    contentType: {
      type: String,
      enum: ['video', 'note', 'mcq', 'coding_problem', 'coding_lab'],
      required: [true, 'Content type is required']
    },

    // Submission details
    submissionDate: {
      type: Date,
      required: [true, 'Submission date is required']
    },
    originalPoints: {
      type: Number,
      required: [true, 'Original points is required']
    },
    earnedPoints: {
      type: Number,
      required: [true, 'Earned points is required']
    },

    // Frozen deadline state at time of submission
    deadlineSnapshot: {
      _id: false,
      originalDeadline: {
        type: Date,
        required: true
      },
      adjustedDeadline: {
        type: Date,
        required: true
      },
      startDate: {
        type: Date,
        required: true
      },
      penaltyRules: [
        {
          _id: false,
          daysLate: {
            type: Number,
            required: true
          },
          penaltyPercentage: {
            type: Number,
            required: true
          }
        }
      ],
      batchDeadlineVersion: {
        type: Number,
        required: true
      },
      userAdjustmentVersion: {
        type: Number,
        required: true
      }
    },

    // Frozen submodule position at time of submission
    positionSnapshot: {
      _id: false,
      moduleId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Module',
        required: true
      },
      moduleOrder: {
        type: Number,
        required: true
      },
      submoduleOrder: {
        type: Number,
        required: true
      },
      globalPosition: {
        type: Number,
        required: true
      },
      previousSubmoduleId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SubModule'
      },
      nextSubmoduleId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'SubModule'
      },
      courseStructureVersion: {
        type: Number,
        required: true
      }
    },

    // Calculated penalty (frozen at submission time)
    penaltyCalculation: {
      _id: false,
      daysLate: {
        type: Number,
        required: true
      },
      penaltyPercentage: {
        type: Number,
        required: true
      },
      penaltyApplied: {
        type: Number,
        required: true
      },
      pointsAfterPenalty: {
        type: Number,
        required: true
      },
      wasOnTime: {
        type: Boolean,
        required: true
      }
    },

    // Unlock validation (frozen at submission time)
    unlockValidation: {
      _id: false,
      wasUnlocked: {
        type: Boolean,
        required: true
      },
      unlockReason: {
        type: String,
        required: true
      },
      prerequisitesMet: {
        type: Boolean,
        required: true
      },
      previousSubmoduleCompleted: {
        type: Boolean
      },
      dateRequirementMet: {
        type: Boolean
      }
    },

    isActive: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

// Indexes for efficient queries
submissionSnapshotSchema.index({ user: 1, batch: 1 });
submissionSnapshotSchema.index({ user: 1, submodule: 1 });
submissionSnapshotSchema.index({ user: 1, contentId: 1, submodule: 1 }, { unique: true });
submissionSnapshotSchema.index({ submissionDate: 1 });
submissionSnapshotSchema.index({ 'deadlineSnapshot.batchDeadlineVersion': 1 });
submissionSnapshotSchema.index({ 'positionSnapshot.courseStructureVersion': 1 });

// Static method to create snapshot with all required data
submissionSnapshotSchema.statics.createSnapshot = async function (
  userId: mongoose.Types.ObjectId,
  batchId: mongoose.Types.ObjectId,
  submoduleId: mongoose.Types.ObjectId,
  contentId: mongoose.Types.ObjectId,
  contentType: string,
  submissionData: {
    submissionDate: Date;
    originalPoints: number;
    earnedPoints: number;
    deadlineSnapshot: any;
    positionSnapshot: any;
    penaltyCalculation: any;
    unlockValidation: any;
  }
): Promise<ISubmissionSnapshot> {
  const model = this as mongoose.Model<ISubmissionSnapshot>;

  // Check if snapshot already exists
  const existing = await model.findOne({
    user: userId,
    contentId: contentId,
    submodule: submoduleId,
    isActive: true
  });

  if (existing) {
    // Update existing snapshot with latest submission
    existing.submissionDate = submissionData.submissionDate;
    existing.originalPoints = submissionData.originalPoints;
    existing.earnedPoints = submissionData.earnedPoints;
    existing.deadlineSnapshot = submissionData.deadlineSnapshot;
    existing.positionSnapshot = submissionData.positionSnapshot;
    existing.penaltyCalculation = submissionData.penaltyCalculation;
    existing.unlockValidation = submissionData.unlockValidation;
    
    return await existing.save();
  }

  // Create new snapshot
  return await model.create({
    user: userId,
    batch: batchId,
    submodule: submoduleId,
    contentId: contentId,
    contentType: contentType,
    ...submissionData,
    isActive: true
  });
};

// Static method to get penalty from snapshot (immutable)
submissionSnapshotSchema.statics.getSnapshotPenalty = async function (
  userId: mongoose.Types.ObjectId,
  contentId: mongoose.Types.ObjectId,
  submoduleId: mongoose.Types.ObjectId
): Promise<{
  penaltyApplied: number;
  daysLate: number;
  pointsAfterPenalty: number;
  wasOnTime: boolean;
} | null> {
  const model = this as mongoose.Model<ISubmissionSnapshot>;

  const snapshot = await model.findOne({
    user: userId,
    contentId: contentId,
    submodule: submoduleId,
    isActive: true
  }).lean();

  if (!snapshot) {
    return null;
  }

  return {
    penaltyApplied: snapshot.penaltyCalculation.penaltyApplied,
    daysLate: snapshot.penaltyCalculation.daysLate,
    pointsAfterPenalty: snapshot.penaltyCalculation.pointsAfterPenalty,
    wasOnTime: snapshot.penaltyCalculation.wasOnTime
  };
};

// Static method to validate unlock status from snapshot
submissionSnapshotSchema.statics.validateUnlockFromSnapshot = async function (
  userId: mongoose.Types.ObjectId,
  submoduleId: mongoose.Types.ObjectId
): Promise<{
  wasUnlocked: boolean;
  unlockReason: string;
  prerequisitesMet: boolean;
} | null> {
  const model = this as mongoose.Model<ISubmissionSnapshot>;

  const snapshot = await model.findOne({
    user: userId,
    submodule: submoduleId,
    isActive: true
  }).lean();

  if (!snapshot) {
    return null;
  }

  return {
    wasUnlocked: snapshot.unlockValidation.wasUnlocked,
    unlockReason: snapshot.unlockValidation.unlockReason,
    prerequisitesMet: snapshot.unlockValidation.prerequisitesMet
  };
};

// Interface for static methods
interface SubmissionSnapshotModel extends mongoose.Model<ISubmissionSnapshot> {
  createSnapshot(
    userId: mongoose.Types.ObjectId,
    batchId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId,
    contentId: mongoose.Types.ObjectId,
    contentType: string,
    submissionData: any
  ): Promise<ISubmissionSnapshot>;

  getSnapshotPenalty(
    userId: mongoose.Types.ObjectId,
    contentId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId
  ): Promise<any>;

  validateUnlockFromSnapshot(
    userId: mongoose.Types.ObjectId,
    submoduleId: mongoose.Types.ObjectId
  ): Promise<any>;
}

const SubmissionSnapshot = mongoose.model<ISubmissionSnapshot, SubmissionSnapshotModel>(
  'SubmissionSnapshot',
  submissionSnapshotSchema
);

export default SubmissionSnapshot;
