{"name": "classroom.sheryians3.0-api", "version": "3.0.0", "description": "This is a REST API for classrooms.sheryians.com and all other platforms", "main": "server.ts", "scripts": {"dev": "nodemon --files server.ts", "build": "tsc", "format:fix": "prettier . --write", "format:check": "prettier --check .", "lint:fix": "eslint . --fix", "lint:check": "eslint .", "test": "jest", "test:watch": "jest --watch --runInBand", "start": "NODE_ENV=production node ./dist/server.js", "prepare": "husky"}, "keywords": ["classroom", "shery<PERSON>"], "author": "", "license": "ISC", "devDependencies": {"@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.14.1", "@types/pdfkit": "^0.13.9", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "eslint": "^9.25.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.5.1", "nodemon": "^3.1.9", "prettier": "^3.5.3", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1"}, "dependencies": {"@imagekit/javascript": "^5.0.0", "@types/express": "^5.0.1", "axios": "^1.8.4", "bullmq": "^5.49.2", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "imagekit": "^6.0.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.1", "node-cron": "^4.0.5", "node-fetch": "^3.3.2", "pdfkit": "^0.17.1", "redlock": "^5.0.0-beta.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xss": "^1.0.15", "zod": "^3.24.3"}, "lint-staged": {"*.ts": ["npm run format:fix", "npm run lint:fix"]}}